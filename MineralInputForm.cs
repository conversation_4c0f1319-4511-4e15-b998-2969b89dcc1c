using System;
using System.Windows.Forms;
using System.Drawing;

namespace BritSystem
{
    public class MineralInputForm : Form
    {
        private TextBox txtV石英;
        private TextBox txtV长石;
        private TextBox txtV碳酸盐矿物;
        private TextBox txtV黏土矿物;
        private Button btnOK;
        private Label lblV石英;
        private Label lblV长石;
        private Label lblV碳酸盐矿物;
        private Label lblV黏土矿物;
        private Button btnCancel;

        public double V石英 { get; private set; }
        public double V长石 { get; private set; }
        public double V碳酸盐矿物 { get; private set; }
        public double V黏土矿物 { get; private set; }

        public MineralInputForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            lblV石英 = new Label();
            txtV石英 = new TextBox();
            lblV长石 = new Label();
            txtV长石 = new TextBox();
            lblV碳酸盐矿物 = new Label();
            txtV碳酸盐矿物 = new TextBox();
            lblV黏土矿物 = new Label();
            txtV黏土矿物 = new TextBox();
            btnOK = new Button();
            btnCancel = new Button();
            SuspendLayout();
            // 
            // lblV石英
            // 
            lblV石英.Location = new Point(0, 0);
            lblV石英.Name = "lblV石英";
            lblV石英.Size = new Size(138, 36);
            lblV石英.TabIndex = 0;
            // 
            // txtV石英
            // 
            txtV石英.Location = new Point(0, 0);
            txtV石英.Name = "txtV石英";
            txtV石英.Size = new Size(100, 30);
            txtV石英.TabIndex = 1;
            // 
            // lblV长石
            // 
            lblV长石.Location = new Point(0, 0);
            lblV长石.Name = "lblV长石";
            lblV长石.Size = new Size(100, 23);
            lblV长石.TabIndex = 2;
            // 
            // txtV长石
            // 
            txtV长石.Location = new Point(0, 0);
            txtV长石.Name = "txtV长石";
            txtV长石.Size = new Size(100, 30);
            txtV长石.TabIndex = 3;
            // 
            // lblV碳酸盐矿物
            // 
            lblV碳酸盐矿物.Location = new Point(0, 0);
            lblV碳酸盐矿物.Name = "lblV碳酸盐矿物";
            lblV碳酸盐矿物.Size = new Size(100, 23);
            lblV碳酸盐矿物.TabIndex = 4;
            // 
            // txtV碳酸盐矿物
            // 
            txtV碳酸盐矿物.Location = new Point(0, 0);
            txtV碳酸盐矿物.Name = "txtV碳酸盐矿物";
            txtV碳酸盐矿物.Size = new Size(100, 30);
            txtV碳酸盐矿物.TabIndex = 5;
            // 
            // lblV黏土矿物
            // 
            lblV黏土矿物.Location = new Point(0, 0);
            lblV黏土矿物.Name = "lblV黏土矿物";
            lblV黏土矿物.Size = new Size(100, 23);
            lblV黏土矿物.TabIndex = 6;
            // 
            // txtV黏土矿物
            // 
            txtV黏土矿物.Location = new Point(0, 0);
            txtV黏土矿物.Name = "txtV黏土矿物";
            txtV黏土矿物.Size = new Size(100, 30);
            txtV黏土矿物.TabIndex = 7;
            // 
            // btnOK
            // 
            btnOK.Location = new Point(0, 0);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(75, 23);
            btnOK.TabIndex = 8;
            btnOK.Click += BtnOK_Click;
            // 
            // btnCancel
            // 
            btnCancel.Location = new Point(0, 0);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(75, 23);
            btnCancel.TabIndex = 9;
            // 
            // MineralInputForm
            // 
            AcceptButton = btnOK;
            CancelButton = btnCancel;
            ClientSize = new Size(335, 244);
            Controls.Add(lblV石英);
            Controls.Add(txtV石英);
            Controls.Add(lblV长石);
            Controls.Add(txtV长石);
            Controls.Add(lblV碳酸盐矿物);
            Controls.Add(txtV碳酸盐矿物);
            Controls.Add(lblV黏土矿物);
            Controls.Add(txtV黏土矿物);
            Controls.Add(btnOK);
            Controls.Add(btnCancel);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "MineralInputForm";
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "输入矿物体积值";
            ResumeLayout(false);
            PerformLayout();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 解析输入值
                if (!double.TryParse(txtV石英.Text, out double v石英))
                {
                    MessageBox.Show("石英体积必须是有效的数字！", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtV石英.Focus();
                    this.DialogResult = DialogResult.None;
                    return;
                }

                if (!double.TryParse(txtV长石.Text, out double v长石))
                {
                    MessageBox.Show("长石体积必须是有效的数字！", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtV长石.Focus();
                    this.DialogResult = DialogResult.None;
                    return;
                }

                if (!double.TryParse(txtV碳酸盐矿物.Text, out double v碳酸盐矿物))
                {
                    MessageBox.Show("碳酸盐矿物体积必须是有效的数字！", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtV碳酸盐矿物.Focus();
                    this.DialogResult = DialogResult.None;
                    return;
                }

                if (!double.TryParse(txtV黏土矿物.Text, out double v黏土矿物))
                {
                    MessageBox.Show("黏土矿物体积必须是有效的数字！", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtV黏土矿物.Focus();
                    this.DialogResult = DialogResult.None;
                    return;
                }

                // 设置属性值
                V石英 = v石英;
                V长石 = v长石;
                V碳酸盐矿物 = v碳酸盐矿物;
                V黏土矿物 = v黏土矿物;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"输入处理错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.DialogResult = DialogResult.None;
            }
        }
    }
}
