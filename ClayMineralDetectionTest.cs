using System;
using System.Data;
using System.Windows.Forms;
using BritSystem.Services;

namespace BritSystem
{
    /// <summary>
    /// 黏土矿物检测测试类，用于测试黏土矿物总量列的检测功能
    /// </summary>
    public class ClayMineralDetectionTest
    {
        /// <summary>
        /// 测试黏土矿物总量列检测
        /// </summary>
        /// <param name="dataTable">数据表</param>
        /// <returns>测试结果</returns>
        public static string TestClayMineralDetection(DataTable dataTable)
        {
            if (dataTable == null || dataTable.Rows.Count == 0 || dataTable.Columns.Count == 0)
                return "数据表为空，无法进行测试";

            try
            {
                // 创建日志构建器
                var logBuilder = new System.Text.StringBuilder();
                logBuilder.AppendLine("===== 黏土矿物总量列检测测试 =====");
                logBuilder.AppendLine($"数据表: {dataTable.TableName}");
                logBuilder.AppendLine($"行数: {dataTable.Rows.Count}");
                logBuilder.AppendLine($"列数: {dataTable.Columns.Count}");
                logBuilder.AppendLine();

                // 1. 使用旧的检测方法
                logBuilder.AppendLine("1. 使用旧的检测方法:");
                string oldResult = ImprovedClayMineralDetection.FindClayMineralTotalColumn(dataTable);
                logBuilder.AppendLine($"   结果: {(string.IsNullOrEmpty(oldResult) ? "未找到" : oldResult)}");
                logBuilder.AppendLine();

                // 2. 使用新的检测方法
                logBuilder.AppendLine("2. 使用新的检测方法:");
                var detector = new ClayMineralColumnDetector(dataTable);
                var newResult = detector.FindClayMineralTotalColumn();
                logBuilder.AppendLine($"   结果: {(newResult.RowIndex < 0 ? "未找到" : $"行={newResult.RowIndex + 1}, 列={newResult.ColumnIndex + 1}, 值='{newResult.ColumnName}'")}");
                logBuilder.AppendLine();

                // 3. 查找所有可能的黏土矿物总量列
                logBuilder.AppendLine("3. 所有可能的黏土矿物总量列:");
                var allColumns = detector.FindAllPossibleClayMineralColumns();
                if (allColumns.Count > 0)
                {
                    foreach (var col in allColumns)
                    {
                        logBuilder.AppendLine($"   - 行={col.RowIndex + 1}, 列={col.ColumnIndex + 1}, 值='{col.CellValue}'");
                    }
                }
                else
                {
                    logBuilder.AppendLine("   未找到任何可能的黏土矿物总量列");
                }
                logBuilder.AppendLine();

                // 4. 使用ColumnDetectionHelper检查每一列
                logBuilder.AppendLine("4. 使用ColumnDetectionHelper检查每一列:");
                for (int col = 0; col < Math.Min(10, dataTable.Columns.Count); col++) // 只检查前10列
                {
                    string columnName = dataTable.Columns[col].ColumnName;
                    bool isClayTotal = ColumnDetectionHelper.IsClayMineralTotalColumn(columnName);
                    logBuilder.AppendLine($"   列 {col + 1}: '{columnName}' -> {(isClayTotal ? "是" : "否")}黏土矿物总量列");
                }
                logBuilder.AppendLine();

                // 5. 总结
                logBuilder.AppendLine("5. 测试总结:");
                if (!string.IsNullOrEmpty(oldResult) && newResult.RowIndex >= 0)
                {
                    logBuilder.AppendLine("   两种方法都找到了黏土矿物总量列");
                    if (oldResult == newResult.ColumnName)
                    {
                        logBuilder.AppendLine("   两种方法找到的列相同");
                    }
                    else
                    {
                        logBuilder.AppendLine($"   两种方法找到的列不同: 旧方法='{oldResult}', 新方法='{newResult.ColumnName}'");
                    }
                }
                else if (!string.IsNullOrEmpty(oldResult))
                {
                    logBuilder.AppendLine($"   只有旧方法找到了黏土矿物总量列: '{oldResult}'");
                }
                else if (newResult.RowIndex >= 0)
                {
                    logBuilder.AppendLine($"   只有新方法找到了黏土矿物总量列: '{newResult.ColumnName}'");
                }
                else
                {
                    logBuilder.AppendLine("   两种方法都未找到黏土矿物总量列");
                }

                return logBuilder.ToString();
            }
            catch (Exception ex)
            {
                return $"测试过程中发生错误: {ex.Message}\n{ex.StackTrace}";
            }
        }

        /// <summary>
        /// 显示测试结果对话框
        /// </summary>
        /// <param name="result">测试结果</param>
        public static void ShowTestResult(string result)
        {
            using (var form = new Form())
            {
                form.Text = "黏土矿物总量列检测测试结果";
                form.Size = new System.Drawing.Size(800, 600);
                form.StartPosition = FormStartPosition.CenterScreen;

                var textBox = new TextBox();
                textBox.Multiline = true;
                textBox.ScrollBars = ScrollBars.Both;
                textBox.Dock = DockStyle.Fill;
                textBox.ReadOnly = true;
                textBox.Font = new System.Drawing.Font("Consolas", 10);
                textBox.Text = result;

                form.Controls.Add(textBox);
                form.ShowDialog();
            }
        }
    }
}
