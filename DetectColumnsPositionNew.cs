using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;

namespace BritSystem
{
    // 扩展方法类
    public static class Extensions
    {
        public static string CleanString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // 移除空格、标点符号，转为小写
            return Regex.Replace(input, @"[\s\p{P}]", "").ToLower();
        }
    }

    public partial class MineralogicalForm
    {

        // 核心数据方法
        private void DetectColumnsPositionNew(DataTable table)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("\n=== 开始检测列位置 ===\n");
                columnPositions.Clear();

                // 防御性检查，确保表格有效
                if (table == null || table.Columns.Count == 0 || table.Rows.Count == 0)
                {
                    throw new ArgumentException("输入数据表无效或为空");
                }

                // 记录表格基本信息
                System.Diagnostics.Debug.WriteLine($"表格列数: {table.Columns.Count}, 行数: {table.Rows.Count}");
                for (int col = 0; col < Math.Min(10, table.Columns.Count); col++)
                {
                    System.Diagnostics.Debug.WriteLine($"列 {col + 1}: {table.Columns[col].ColumnName}");
                }

                // 优先检测脆性列（包含公式）
                bool foundBrittleColumn = false;

                // 第一步：检查列名是否包含"脆性"
                for (int col = 0; col < table.Columns.Count; col++)
                {
                    string columnName = Extensions.CleanString(table.Columns[col].ColumnName);

                    // 检查列名是否精确匹配"脆性指数"
                    if (columnName == Extensions.CleanString("脆性指数"))
                    {
                        // 在该列中查找包含"脆性"关键词的单元格
                        for (int row = 0; row < Math.Min(10, table.Rows.Count); row++)
                        {
                            string cellText = table.Rows[row][col]?.ToString() ?? "";
                            if (Extensions.CleanString(cellText).Contains(Extensions.CleanString("脆性")))
                            {
                                columnPositions.Add("脆性", (row, col));
                                System.Diagnostics.Debug.WriteLine($"在行 {row + 1} 找到脆性关键词");
                                break;
                            }
                        }

                        if (!columnPositions.ContainsKey("脆性"))
                        {
                            columnPositions.Add("脆性", (0, col)); // 如果没有找到包含关键词的单元格，默认使用第一行
                        }
                        foundBrittleColumn = true;
                        System.Diagnostics.Debug.WriteLine($"通过列名检测到脆性列: {table.Columns[col].ColumnName}");
                        break;
                    }
                }

                // 第二步：如果没有找到精确匹配脆性指数的列，则检查包含脆性关键词的列
                if (!foundBrittleColumn)
                {
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        string columnName = Extensions.CleanString(table.Columns[col].ColumnName);

                        // 检查列名是否包含"脆性"但不是"TOC"
                        if (columnName.Contains(Extensions.CleanString("脆性")) &&
                            !columnName.Contains(Extensions.CleanString("TOC")) &&
                            !columnName.Contains(Extensions.CleanString("总有机碳")))
                        {
                            // 在该列中查找包含"脆性"关键词的单元格
                            for (int row = 0; row < Math.Min(10, table.Rows.Count); row++)
                            {
                                string cellText = table.Rows[row][col]?.ToString() ?? "";
                                if (Extensions.CleanString(cellText).Contains(Extensions.CleanString("脆性")))
                                {
                                    columnPositions.Add("脆性", (row, col));
                                    System.Diagnostics.Debug.WriteLine($"在行 {row + 1} 找到脆性关键词");
                                    break;
                                }
                            }

                            if (!columnPositions.ContainsKey("脆性"))
                            {
                                columnPositions.Add("脆性", (0, col));
                            }

                            foundBrittleColumn = true;
                            System.Diagnostics.Debug.WriteLine($"通过列名包含脆性检测到脆性列: {table.Columns[col].ColumnName}");
                            break;
                        }
                    }
                }

                // 第三步：如果还是没有找到脆性列，则检查公式
                if (!foundBrittleColumn)
                {
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        for (int row = 0; row < Math.Min(10, table.Rows.Count); row++)
                        {
                            string cellValue = table.Rows[row][col]?.ToString() ?? "";
                            // 允许包含括号、混合运算符（如加减乘除）
                            if (Regex.IsMatch(cellValue, @"^([A-Z]+\d+[\+\-\*/]?)+$"))
                            {
                                columnPositions.Add("脆性", (row, col));
                                foundBrittleColumn = true;
                                System.Diagnostics.Debug.WriteLine($"通过公式检测到脆性列: {cellValue}");
                                break;
                            }
                        }
                        if (foundBrittleColumn) break;
                    }
                }

                // 第四步：如果还是没有找到脆性列，尝试直接查找数字列
                if (!foundBrittleColumn)
                {
                    // 首先检查是否有列包含0-100之间的数字
                    List<(int col, int numericRows)> candidateColumns = new List<(int, int)>();

                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        // 跳过TOC列
                        string columnName = Extensions.CleanString(table.Columns[col].ColumnName);
                        if (columnName.Contains(Extensions.CleanString("TOC")) ||
                            columnName.Contains(Extensions.CleanString("总有机碳")))
                        {
                            continue;
                        }

                        // 检查前更多行数据是否包含0-100之间的数字
                        int numericRows = 0;
                        int maxRowsToCheck = Math.Min(100, table.Rows.Count); // 扩大检测范围
                        double validRatio = table.Rows.Count > 100 ? 0.2 : 0.3; // 根据数据量调整比例

                        for (int row = 0; row < maxRowsToCheck; row++)
                        {
                            string cellValue = table.Rows[row][col]?.ToString() ?? "";
                            if (double.TryParse(cellValue, out double value) && value >= 0 && value <= 100)
                            {
                                numericRows++;
                            }
                        }

                        // 如果该列至少有一定比例的行是0-100之间的数字，则添加到候选列表
                        // 要求至少有5行或者总行数的动态比例的行是有效数字
                        if (numericRows >= Math.Max(5, validRatio * maxRowsToCheck))
                        {
                            candidateColumns.Add((col, numericRows));
                        }
                    }

                    // 按照数字行数从高到低排序候选列
                    candidateColumns.Sort((a, b) => b.numericRows.CompareTo(a.numericRows));

                    // 选择数字行最多的列作为脆性指数列
                    if (candidateColumns.Count > 0)
                    {
                        int col = candidateColumns[0].col;

                        // 在该列中查找包含"脆性"关键词的单元格
                        for (int searchRow = 0; searchRow < Math.Min(10, table.Rows.Count); searchRow++)
                        {
                            string cellText = table.Rows[searchRow][col]?.ToString() ?? "";
                            if (Extensions.CleanString(cellText).Contains(Extensions.CleanString("脆性")))
                            {
                                columnPositions.Add("脆性", (searchRow, col));
                                System.Diagnostics.Debug.WriteLine($"在行 {searchRow + 1} 找到脆性关键词");
                                break;
                            }
                        }

                        if (!columnPositions.ContainsKey("脆性"))
                        {
                            columnPositions.Add("脆性", (0, col)); // 如果没有找到包含关键词的单元格，默认使用第一行
                        }
                        foundBrittleColumn = true;
                        System.Diagnostics.Debug.WriteLine($"通过数字检测到脆性列: {table.Columns[col].ColumnName}");
                    }
                }

                // 检测其他列（顶深和底深）
                for (int row = 0; row < Math.Min(config.MaxHeaderSearchRows, table.Rows.Count); row++)
                {
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        string cellValue = Extensions.CleanString(table.Rows[row][col].ToString());

                        foreach (var keyword in config.RequiredColumns)
                        {
                            if (keyword == "脆性" && foundBrittleColumn) continue; // 如果已经找到脆性列，跳过

                            if (cellValue.Contains(Extensions.CleanString(keyword)))
                            {
                                if (!columnPositions.ContainsKey(keyword))
                                {
                                    columnPositions.Add(keyword, (row, col));
                                    System.Diagnostics.Debug.WriteLine($"检测到{keyword}列: 行={row + 1}, 列={col + 1}");
                                }
                            }
                        }
                    }
                }

                // 记录检测结果
                System.Diagnostics.Debug.WriteLine("\n=== 列位置检测结果 ===\n");
                foreach (var pos in columnPositions)
                {
                    System.Diagnostics.Debug.WriteLine($"列名: {pos.Key}, 行: {pos.Value.row + 1}, 列: {pos.Value.col + 1}");
                }

                var missing = config.RequiredColumns.Where(c => !columnPositions.ContainsKey(c)).ToList();
                if (missing.Count > 0)
                {
                    // 如果有顶深度列，将其映射为顶深
                    if (columnPositions.ContainsKey("顶深度") && !columnPositions.ContainsKey("顶深"))
                    {
                        columnPositions.Add("顶深", columnPositions["顶深度"]);
                        System.Diagnostics.Debug.WriteLine($"将顶深度列映射为顶深");
                        missing.Remove("顶深");
                    }

                    // 如果有底深度列，将其映射为底深
                    if (columnPositions.ContainsKey("底深度") && !columnPositions.ContainsKey("底深"))
                    {
                        columnPositions.Add("底深", columnPositions["底深度"]);
                        System.Diagnostics.Debug.WriteLine($"将底深度列映射为底深");
                        missing.Remove("底深");
                    }

                    // 重新检查缺失的列
                    if (missing.Any())
                    {
                        string errorMsg = $"缺少必要列头：{string.Join(",", missing)}";
                        System.Diagnostics.Debug.WriteLine($"\n错误: {errorMsg}\n");
                        throw new Exception(errorMsg);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"\n发现替代列，继续处理\n");
                    }
                }

                System.Diagnostics.Debug.WriteLine("\n=== 列位置检测完成 ===\n");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"\n列位置检测异常: {ex.Message}\n{ex.StackTrace}\n");
                throw; // 重新抛出异常以便上层处理
            }
        }
    }
}
