using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace BritSystem
{
    /// <summary>
    /// 表示检测到的黏土矿物列的信息
    /// </summary>
    public class ClayMineralColumnInfo
    {
        /// <summary>
        /// 列索引
        /// </summary>
        public int ColumnIndex { get; set; }

        /// <summary>
        /// 行索引
        /// </summary>
        public int RowIndex { get; set; }

        /// <summary>
        /// 单元格值（列名）
        /// </summary>
        public string CellValue { get; set; }

        /// <summary>
        /// 匹配的模式
        /// </summary>
        public string MatchPattern { get; set; }

        /// <summary>
        /// 匹配的置信度（0-100）
        /// </summary>
        public int Confidence { get; set; }

        /// <summary>
        /// 是否是黏土矿物总量列
        /// </summary>
        public bool IsClayTotalColumn { get; set; }

        public override string ToString()
        {
            return $"列: {ColumnIndex}, 行: {RowIndex}, 值: {CellValue}, 模式: {MatchPattern}, 置信度: {Confidence}, 是总量列: {IsClayTotalColumn}";
        }
    }

    /// <summary>
    /// 黏土矿物列检测器，用于改进黏土矿物总量列的检测
    /// </summary>
    public class ClayMineralColumnDetector
    {
        private readonly DataTable _dataTable;
        private readonly int _maxHeaderSearchRows;
        private readonly List<string> _clayTotalPatterns;
        private readonly List<string> _clayMineralPatterns;
        private readonly List<string> _excludePatterns;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dataTable">数据表</param>
        /// <param name="maxHeaderSearchRows">最大搜索行数</param>
        public ClayMineralColumnDetector(DataTable dataTable, int maxHeaderSearchRows = 10)
        {
            _dataTable = dataTable;
            _maxHeaderSearchRows = maxHeaderSearchRows;

            // 黏土矿物总量的匹配模式
            _clayTotalPatterns = new List<string>
            {
                // 中文标准格式
                "黏土矿物总量", "黏土矿物总量%", "黏土矿物总量/%", "黏土矿物总量z", "黏土矿物总量%z",
                "粘土矿物总量", "粘土矿物总量%", "粘土矿物总量/%", "粘土矿物总量z", "粘土矿物总量%z",
                // 含量表述
                "黏土矿物总含量", "粘土矿物总含量", "黏土矿物含量", "粘土矿物含量",
                "黏土矿物相对含量", "粘土矿物相对含量", "黏土矿物相对含量%", "粘土矿物相对含量%",
                "黏土矿物相对含量/%", "粘土矿物相对含量/%",
                // 简化表述
                "黏土总量", "粘土总量", "黏土含量", "粘土含量", "黏土%", "粘土%",
                "黏土矿物%", "粘土矿物%", "黏土", "粘土",
                // 英文表述
                "Clay Minerals", "Clay Mineral", "Clay Total", "Total Clay", "Clay Content",
                "Clay", "Clay%", "Clay Minerals%", "Clay Mineral%", "Clay Mineral Total",
                "Clay Minerals Total", "Total Clay Minerals", "Total Clay Mineral",
                "Clay Minerals Content", "Clay Mineral Content",
                // 添加更多可能的变体
                "黏土矿物总量(%)", "粘土矿物总量(%)", "黏土矿物总量（%）", "粘土矿物总量（%）",
                "黏土矿物总量/100", "粘土矿物总量/100", "黏土矿物总量(wt%)", "粘土矿物总量(wt%)",
                "黏土矿物总量(WT%)", "粘土矿物总量(WT%)", "黏土矿物总量(Wt%)", "粘土矿物总量(Wt%)",
                "黏土矿物总量wt%", "粘土矿物总量wt%", "黏土矿物总量WT%", "粘土矿物总量WT%",
                "黏土矿物总量Wt%", "粘土矿物总量Wt%",
                // 新增更多可能的变体
                "黏土矿物总", "粘土矿物总", "黏土总", "粘土总",
                "Clay Total%", "Total Clay%", "Clay Sum", "Clay Sum%",
                "Clay Minerals Sum", "Clay Minerals Sum%", "Sum of Clay", "Sum of Clay%",
                "Sum of Clay Minerals", "Sum of Clay Minerals%",
                "黏土矿物总计", "粘土矿物总计", "黏土总计", "粘土总计",
                "黏土矿物合计", "粘土矿物合计", "黏土合计", "粘土合计",
                "黏土矿物汇总", "粘土矿物汇总", "黏土汇总", "粘土汇总"
            };

            // 黏土矿物的匹配模式（非总量）
            _clayMineralPatterns = new List<string>
            {
                "高岭石", "kaolinite", "绿泥石", "chlorite",
                "伊利石", "illite", "蒙脱石", "montmorillonite",
                "伊蒙混层", "i/s", "混层矿物", "混层粘土"
            };

            // 排除模式（不应该被识别为黏土矿物的模式）
            _excludePatterns = new List<string>
            {
                "TOC", "有机碳", "总有机碳", "Total Organic Carbon", "Organic Carbon",
                "石英", "quartz", "白云石", "dolomite", "菱铁矿", "siderite",
                "斜长石", "plagioclase", "钾长石", "k-feldspar", "正长石", "orthoclase",
                "方解石", "calcite", "碳酸盐", "carbonate"
            };
        }

        /// <summary>
        /// 检查数据表中的所有列，找出可能的黏土矿物总量列
        /// </summary>
        /// <returns>可能的黏土矿物总量列列表</returns>
        public List<ClayMineralColumnInfo> FindAllPossibleClayMineralColumns()
        {
            var result = new List<ClayMineralColumnInfo>();

            // 检查数据表是否有列和行
            if (_dataTable == null || _dataTable.Columns.Count == 0 || _dataTable.Rows.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("数据表为空或没有列/行");
                return result;
            }

            // 记录开始查找
            System.Diagnostics.Debug.WriteLine("开始查找所有可能的黏土矿物总量列");

            // 首先检查列名
            for (int colIndex = 0; colIndex < _dataTable.Columns.Count; colIndex++)
            {
                string columnName = _dataTable.Columns[colIndex].ColumnName;

                // 检查是否匹配黏土矿物总量模式
                var totalMatch = MatchPattern(columnName, _clayTotalPatterns);
                if (totalMatch.Item1)
                {
                    result.Add(new ClayMineralColumnInfo
                    {
                        ColumnIndex = colIndex,
                        RowIndex = -1, // 列名没有行索引
                        CellValue = columnName,
                        MatchPattern = totalMatch.Item2,
                        Confidence = 90, // 列名匹配置信度较高
                        IsClayTotalColumn = true
                    });

                    System.Diagnostics.Debug.WriteLine($"在列名中找到黏土矿物总量列: {columnName}, 匹配模式: {totalMatch.Item2}");
                    continue;
                }

                // 检查是否匹配黏土矿物模式（非总量）
                var mineralMatch = MatchPattern(columnName, _clayMineralPatterns);
                if (mineralMatch.Item1)
                {
                    result.Add(new ClayMineralColumnInfo
                    {
                        ColumnIndex = colIndex,
                        RowIndex = -1, // 列名没有行索引
                        CellValue = columnName,
                        MatchPattern = mineralMatch.Item2,
                        Confidence = 80, // 列名匹配置信度较高
                        IsClayTotalColumn = false
                    });

                    System.Diagnostics.Debug.WriteLine($"在列名中找到黏土矿物列: {columnName}, 匹配模式: {mineralMatch.Item2}");
                }
            }

            // 然后检查各行的单元格值
            int maxRowsToCheck = Math.Min(_maxHeaderSearchRows, _dataTable.Rows.Count);
            for (int rowIndex = 0; rowIndex < maxRowsToCheck; rowIndex++)
            {
                for (int colIndex = 0; colIndex < _dataTable.Columns.Count; colIndex++)
                {
                    // 获取单元格值
                    object cellObj = _dataTable.Rows[rowIndex][colIndex];
                    if (cellObj == null || cellObj == DBNull.Value)
                        continue;

                    string cellValue = cellObj.ToString();
                    if (string.IsNullOrWhiteSpace(cellValue))
                        continue;

                    // 检查是否匹配黏土矿物总量模式
                    var totalMatch = MatchPattern(cellValue, _clayTotalPatterns);
                    if (totalMatch.Item1)
                    {
                        // 检查是否已经添加过该列
                        bool alreadyAdded = result.Any(r => r.ColumnIndex == colIndex);
                        if (!alreadyAdded)
                        {
                            result.Add(new ClayMineralColumnInfo
                            {
                                ColumnIndex = colIndex,
                                RowIndex = rowIndex,
                                CellValue = cellValue,
                                MatchPattern = totalMatch.Item2,
                                Confidence = 85, // 单元格值匹配置信度略低于列名
                                IsClayTotalColumn = true
                            });

                            System.Diagnostics.Debug.WriteLine($"在单元格中找到黏土矿物总量列: 行={rowIndex}, 列={colIndex}, 值={cellValue}, 匹配模式: {totalMatch.Item2}");
                        }
                        continue;
                    }

                    // 检查是否匹配黏土矿物模式（非总量）
                    var mineralMatch = MatchPattern(cellValue, _clayMineralPatterns);
                    if (mineralMatch.Item1)
                    {
                        // 检查是否已经添加过该列
                        bool alreadyAdded = result.Any(r => r.ColumnIndex == colIndex);
                        if (!alreadyAdded)
                        {
                            result.Add(new ClayMineralColumnInfo
                            {
                                ColumnIndex = colIndex,
                                RowIndex = rowIndex,
                                CellValue = cellValue,
                                MatchPattern = mineralMatch.Item2,
                                Confidence = 75, // 单元格值匹配置信度略低于列名
                                IsClayTotalColumn = false
                            });

                            System.Diagnostics.Debug.WriteLine($"在单元格中找到黏土矿物列: 行={rowIndex}, 列={colIndex}, 值={cellValue}, 匹配模式: {mineralMatch.Item2}");
                        }
                    }
                }
            }

            // 如果没有找到明确的黏土矿物总量列，可以查看X-衍射分析区域附近的列
            if (!result.Any(r => r.IsClayTotalColumn))
            {
                // 查找X-衍射分析列
                int xrdColumnIndex = -1;
                for (int colIndex = 0; colIndex < _dataTable.Columns.Count; colIndex++)
                {
                    string columnName = _dataTable.Columns[colIndex].ColumnName;
                    if (columnName.Contains("X-衍射") || columnName.Contains("X衍射") || 
                        columnName.ToLower().Contains("xrd") || columnName.ToLower().Contains("x-ray diffraction"))
                    {
                        xrdColumnIndex = colIndex;
                        break;
                    }
                }

                // 如果找到X-衍射分析列，检查其附近的列
                if (xrdColumnIndex >= 0)
                {
                    int startCol = Math.Max(0, xrdColumnIndex - 5);
                    int endCol = Math.Min(_dataTable.Columns.Count - 1, xrdColumnIndex + 10);

                    for (int colIndex = startCol; colIndex <= endCol; colIndex++)
                    {
                        if (colIndex == xrdColumnIndex) continue;
                        
                        string columnName = _dataTable.Columns[colIndex].ColumnName;
                        
                        // 检查ColumnXX格式的列，可能包含黏土矿物总量
                        if (Regex.IsMatch(columnName, @"^Column\d+$", RegexOptions.IgnoreCase))
                        {
                            // 查看该列数据是否为数值，且大部分在0-100范围内
                            bool hasNumericValues = false;
                            int validValueCount = 0;
                            int inRangeCount = 0;
                            
                            for (int rowIndex = 0; rowIndex < Math.Min(20, _dataTable.Rows.Count); rowIndex++)
                            {
                                object cellObj = _dataTable.Rows[rowIndex][colIndex];
                                if (cellObj != null && cellObj != DBNull.Value)
                                {
                                    string cellValue = cellObj.ToString();
                                    if (double.TryParse(cellValue, out double value))
                                    {
                                        hasNumericValues = true;
                                        validValueCount++;
                                        
                                        if (value >= 0 && value <= 100)
                                        {
                                            inRangeCount++;
                                        }
                                    }
                                }
                            }
                            
                            // 如果该列有数值且大部分在0-100范围内，可能是黏土矿物总量百分比
                            if (hasNumericValues && validValueCount > 0 && (double)inRangeCount / validValueCount > 0.7)
                            {
                                result.Add(new ClayMineralColumnInfo
                                {
                                    ColumnIndex = colIndex,
                                    RowIndex = -1,
                                    CellValue = columnName,
                                    MatchPattern = "X-衍射分析附近的数值列",
                                    Confidence = 60, // 基于位置和数值特征的推断，置信度较低
                                    IsClayTotalColumn = true
                                });
                                
                                System.Diagnostics.Debug.WriteLine($"在X-衍射分析附近找到可能的黏土矿物总量列: {columnName}");
                            }
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 匹配字符串是否符合指定的模式列表中的任一模式
        /// </summary>
        /// <param name="text">要匹配的文本</param>
        /// <param name="patterns">模式列表</param>
        /// <returns>是否匹配成功及匹配的模式</returns>
        private Tuple<bool, string> MatchPattern(string text, List<string> patterns)
        {
            if (string.IsNullOrEmpty(text))
                return new Tuple<bool, string>(false, string.Empty);

            // 标准化文本，转为小写，移除特殊字符
            string normalizedText = NormalizeString(text);

            // 检查是否是Column格式的列名
            if (Regex.IsMatch(text, @"^Column\d+$", RegexOptions.IgnoreCase))
            {
                // 不要将Column格式的列名标准化为特定字符串
                return new Tuple<bool, string>(false, text.ToLower());
            }

            // 检查每个模式
            foreach (string pattern in patterns)
            {
                string normalizedPattern = NormalizeString(pattern);
                
                // 完全匹配
                if (normalizedText.Equals(normalizedPattern, StringComparison.OrdinalIgnoreCase))
                {
                    return new Tuple<bool, string>(true, pattern);
                }
                
                // 部分匹配（包含关系）
                if (normalizedText.Contains(normalizedPattern) || normalizedPattern.Contains(normalizedText))
                {
                    return new Tuple<bool, string>(true, pattern);
                }
                
                // 对于中文模式，检查是否包含关键部分
                if (pattern.Contains("黏土") || pattern.Contains("粘土"))
                {
                    if (normalizedText.Contains("黏土") || normalizedText.Contains("粘土"))
                    {
                        if ((pattern.Contains("总量") && normalizedText.Contains("总量")) ||
                            (pattern.Contains("含量") && normalizedText.Contains("含量")))
                        {
                            return new Tuple<bool, string>(true, pattern);
                        }
                    }
                }
                
                // 对于英文模式，检查是否包含关键部分
                if (pattern.ToLower().Contains("clay"))
                {
                    if (normalizedText.Contains("clay"))
                    {
                        if ((pattern.ToLower().Contains("total") && normalizedText.Contains("total")) ||
                            (pattern.ToLower().Contains("content") && normalizedText.Contains("content")))
                        {
                            return new Tuple<bool, string>(true, pattern);
                        }
                    }
                }
            }

            return new Tuple<bool, string>(false, string.Empty);
        }

        /// <summary>
        /// 标准化字符串，转为小写，移除特殊字符
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>标准化后的字符串</returns>
        private string NormalizeString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // 如果是 ColumnXX 格式，直接返回小写形式，不做其他处理
            if (Regex.IsMatch(input, @"^Column\d+$", RegexOptions.IgnoreCase))
            {
                return input.ToLower();
            }

            // 移除特殊字符，保留字母、数字、中文字符
            string normalized = Regex.Replace(input, @"[^\w\d\u4e00-\u9fa5]", " ");
            
            // 转为小写
            normalized = normalized.ToLower();
            
            // 移除多余空格
            normalized = Regex.Replace(normalized, @"\s+", " ").Trim();
            
            return normalized;
        }
    }
}
