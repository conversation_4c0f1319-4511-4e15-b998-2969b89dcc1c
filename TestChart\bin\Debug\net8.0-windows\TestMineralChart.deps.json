{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"TestMineralChart/1.0.0": {"dependencies": {"BritSystem": "*******"}, "runtime": {"TestMineralChart.dll": {}}}, "BritSystem/*******": {"runtime": {"BritSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Windows.Forms.DataVisualization/*******": {"runtime": {"System.Windows.Forms.DataVisualization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NPOI.OOXML/*******": {"runtime": {"NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NPOI/*******": {"runtime": {"NPOI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient/*******": {"runtime": {"Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NPOI.OpenXml4Net/*******": {"runtime": {"NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NPOI.OpenXmlFormats/*******": {"runtime": {"NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ICSharpCode.SharpZipLib/*********": {"runtime": {"ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Microsoft.Identity.Client/********": {"runtime": {"Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}}}, "libraries": {"TestMineralChart/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BritSystem/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Windows.Forms.DataVisualization/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "NPOI.OOXML/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "NPOI/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Data.SqlClient/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "NPOI.OpenXml4Net/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "NPOI.OpenXmlFormats/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "ICSharpCode.SharpZipLib/*********": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Identity.Client/********": {"type": "reference", "serviceable": false, "sha512": ""}}}