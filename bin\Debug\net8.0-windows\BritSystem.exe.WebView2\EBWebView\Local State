{"accessibility": {"captions": {"common_models_path": "", "soda_binary_path": ""}}, "autofill": {"ablation_seed": "MX2sKRsyauw="}, "breadcrumbs": {"enabled": false, "enabled_time": "13389285603755486"}, "default_browser": {"browser_name_enum": 1}, "desktop_session_duration_tracker": {"last_session_end_timestamp": "**********"}, "domain_actions_config": "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", "edge": {"manageability": {"edge_last_active_time": "13389285677971378"}, "mitigation_manager": {"renderer_app_container_compatible_count": 1, "renderer_code_integrity_compatible_count": 1}, "tab_stabs": {"closed_without_unfreeze_never_unfrozen": 0, "closed_without_unfreeze_previously_unfrozen": 0, "discard_without_unfreeze_never_unfrozen": 0, "discard_without_unfreeze_previously_unfrozen": 0}, "tab_stats": {"frozen_daily": 0, "unfrozen_daily": 0}}, "hardware_acceleration_mode_previous": true, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "network_time": {"network_time_mapping": {"local": 1744812006037.962, "network": 1744812009000.0, "ticks": ************.0, "uncertainty": 2484712.0}}, "optimization_guide": {"model_store_metadata": {}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAAAz/a1AcpvTQ7zGs6yQHi7XEAAAAB4AAABNAGkAYwByAG8AcwBvAGYAdAAgAEUAZABnAGUAAAAQZgAAAAEAACAAAADIgbiUygJ98XRw7gudNqwNEk6u2mEqEo8aX5jnzfTIpAAAAAAOgAAAAAIAACAAAACF4bV9R8gmsr6zBrCxw+DsYnPBlBN7rUxQUADYpLRkazAAAACrXJep+hoWrSYQSbxx1bEQ9F1YiWla/HHmIxUIjQX+DQPJ9zUzUiXrK/5/tbGzNWpAAAAAQQeuwcEZ/tHp8gJb1Hr18wRQbbpuwEu5I0Ur1PhNcR5IV092CSuQmDa6B//JCAJCjW2PK5K6ZknCLIGx53RHbA=="}, "policy": {"last_statistics_update": "*****************"}, "profile": {"info_cache": {"Default": {"active_time": **********.940831, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_20", "background_apps": false, "edge_account_cid": "", "edge_account_environment": 0, "edge_account_environment_string": "", "edge_account_first_name": "", "edge_account_last_name": "", "edge_account_oid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "", "edge_account_type": 0, "edge_profile_can_be_deleted": true, "edge_test_on_premises": false, "edge_wam_aad_for_app_account_type": 0, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_id": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_glic_eligible": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "用户配置 1", "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": [], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "profiles": {"edge": {"guided_switch_pref": [], "multiple_profiles_with_same_account": false}, "edge_sso_info": {"msa_first_profile_key": "<PERSON><PERSON><PERSON>", "msa_sso_algo_state": 1}, "signin_last_seen_version": "135.0.3179.73", "signin_last_updated_time": **********.803893}, "sentinel_creation_time": "0", "session_id_generator_last_value": "**********", "signin": {"active_accounts_last_emitted": "*****************"}, "startup_boost": {"last_browser_open_time": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_proactive": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_urgent": 0, "total_tab_count_max": 2, "window_count_max": 2}, "telemetry_client": {"cloned_install": {"user_data_dir_id": 6708556}, "governance": {"last_dma_change_date": "*****************", "last_known_cps": 0}, "host_telclient_path": "QzpcUHJvZ3JhbSBGaWxlcyAoeDg2KVxNaWNyb3NvZnRcRWRnZVdlYlZpZXdcQXBwbGljYXRpb25cMTM1LjAuMzE3OS43M1x0ZWxjbGllbnQuZGxs", "sample_id": 599208}, "uninstall_metrics": {"installation_date2": "**********"}, "updateclientdata": {"apps": {"ahmaebgpfccdhgidjaidaoojjcijckba": {"cohort": "rrf@0.56", "cohortname": "", "installdate": -1}, "alpjnmnfbgfkmmpcfpejmmoebdndedno": {"cohort": "rrf@0.71", "cohortname": "", "installdate": -1}, "eeobbhfgfagbclfofmgbdfoicabjdbkn": {"cohort": "rrf@0.61", "cohortname": "", "installdate": -1}, "fgbafbciocncjfbbonhocjaohoknlaco": {"cohort": "rrf@0.59", "cohortname": "", "installdate": -1}, "fppmbhmldokgmleojlplaaodlkibgikh": {"cohort": "rrf@0.72", "cohortname": "", "installdate": -1}, "hajigopbbjhghbfimgkfmpenfkclmohk": {"cohort": "rrf@0.05", "cohortname": "", "installdate": -1}, "jbfaflocpnkhbgcijpkiafdpbjkedane": {"cohort": "rrf@0.08", "cohortname": "", "installdate": -1}, "kpfehajjjbbcifeehjgfgnabifknmdad": {"cohort": "rrf@0.86", "cohortname": "", "installdate": -1}, "ldfkbgjbencjpgjfleiooeldhjdapggh": {"cohort": "rrf@0.57", "cohortname": "", "installdate": -1}, "mcfjlbnicoclaecapilmleaelokfnijm": {"cohort": "rrf@0.71", "cohortname": "", "installdate": -1}, "ndikpojcjlepofdkaaldkinkjbeeebkl": {"cohort": "rrf@0.57", "cohortname": "", "installdate": -1}, "oankkpibpaokgecfckkdkgaoafllipag": {"cohort": "rrf@0.30", "cohortname": "", "installdate": -1}, "ohckeflnhegojcjlcpbfpciadgikcohk": {"cohort": "rrf@0.56", "cohortname": "", "installdate": -1}, "ojblfafjmiikbkepnnolpgbbhejhlcim": {"cohort": "rrf@0.71", "cohortname": "", "installdate": -1}, "pghocgajpebopihickglahgebcmkcekh": {"cohort": "rrf@0.85", "cohortname": "", "installdate": -1}, "pmagihnlncbcefglppponlgakiphldeh": {"cohort": "rrf@0.32", "cohortname": "", "installdate": -1}}}, "updateclientlastupdatecheckerror": 0, "updateclientlastupdatecheckerrorcategory": 0, "updateclientlastupdatecheckerrorextracode1": 0, "user_experience_metrics": {"client_id2": "{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}C:\\Users\\<USER>", "diagnostics": {"last_data_collection_level_on_launch": 1}, "limited_entropy_randomization_source": "AE0B0AF34D70CFDADF0D467C86C6C949", "low_entropy_source3": 972, "machine_id": 2482805, "payload_counter": 1, "pseudo_low_entropy_source": 5664, "reporting_enabled": false, "reset_client_id_deterministic": true, "session_id": 0, "stability": {"browser_last_live_timestamp": "13389285678093465", "exited_cleanly": true, "stats_buildtime": "1744335183", "stats_version": "135.0.3179.73-64", "system_crash_count": 0}}, "variations_compressed_seed": "H4sIAAAAAAAAAJVWXW/bNhT9KwGfxUDUtz3swXHs1kvceP6oC6xFQEvXMheK1EjKiVHkvw+UZCfOonbNg5FLnnN47uUlqe9oNFyg/nc0ekp5lcHoyYASlA+l2LJ8kumJuJU56htVgYOa0VuZL6nKwaA+giyHe23ohgN6dtAoy6EBWc1rKBWk1MBUf2T5biiFUVQbO5UxbTljoKZSoFH/L1Roy34D/fbsoOsWyzgsDtpAMUhT0Hq4g/SBs069t/hrpiA1Uh0mBhQ1TIorLhuFWqpeaySszPqzN8gyZjGUz5S09BvGOWRL4FCAUQe7KIj/5rCGzWcGjz/nv8ps/dlbMnFYM5HJxzlQzsyhsdRVqQ640zk1ENmqzKiBJsviaVCy5U5JYzhYZ/qRmXTXvTNnhDfeh7QARVea5jCVghmpmMg7pKaQMTqWlcjqHXifauVvFlc0fciVhc5By0qlMIZuj11oq/W49xZUZBv51EGeaMmpgRYE2WSraAH6vCPmIDJQoAZladuTMgGqs2DHNmjY71Kt+Foqng2rcswlNYdjo9fBVGbQKf8KYmVuJc0WhhqmDUv1rcxzJvKZYp38t4THmWItrRZcZOmusd7V5/asWlSTBmwGldnNqNYPcNCrSd3savGjpmo5IAxL6154YTd1twvsZFk2qaQwlEVJFcxBl1LoV9fMe/aOzBUt2eipJD+UHGS3VOR3e1CKZb8m7J+EZ4rpYlFQZUZPJZfWaM6kuJU/OMZ34qPcg1oAVeluIhYsgw1V9TFuql+LtnpqUBk5k7q+VrowreJYqgHnK8V1DTyDXMuCMjFIrcw708udAriqjJHiuBV/riZDm0NJ7bEwoKz970jQAlAfpTsqBHDkoD3llR0Zo2fnNA2lTHevJn23+XuNUWAUFbpgpn5J7qW4f2QK7g0rQFbmvmCcMw2pFJl+JeVZFWvwuHWrm6nu2rjVQ/HBlkZQkcKtfTa+OR0Z1eCjZg19WXPKUiW13JpLux1bxvnlTGrN6gcKeLY8lOAQh4S/nVsb7UGYTnPN7IBz+fgzazX0p+bWsLlS8lGDupwpaeSm2l6ubqaXzYM4U9Y5OK71+T9Ihhp9eSvzhaHKOK4T/ArJHjIOBmpeU5TTKziT5YLa6fq3fTbeK9BYqhROtBPaqe+yfAnarATbSlVcM20U21S2uz8ybWSuaNFdT/tklN7Jxllzve7QDLa04uaDhb+HPgNXD0Wn5Lfn9r4YM6XNvBKvP5dsmcbzUbs9dTtANqMHLmnWeYl00NqHvjSH91f7b5UBtipFtcEF5AUIU9/LFjwdTAacoz7aUq5tSuv16HxAp+W2siNu/X9eUZUdg719KF0HTf0oHEoF50z7IoIwC1B7loJ+sw5svizPh66YyM9HbILnI6f2XGm7djv+7KCPQLO6Bb6j0ZLmqI++oj/mi7urh+TP3WQ0XCaf5rv9fnN34x6q7O/B5+U/Xx4+3PW8L9d/MJH//hXZ9Z5KVtcMrSFzLkh0MSjVhed64QUJ+67bd+OLD9NlnVsljDoM67ccDT8hB9lzUel2pL7Bjt/Vk+vGl/0gRzM8x8RNvDB2McGEODN8jQPPTxLs4Qidf2o36DAKXRJgHwdOEweExARHuNfGXuK57st8kLher4d9HLZx2EvCGPvYO8VubPVafhDEPeJhH5MW4JMwSAgmOGpiL/R7vo99nDQxidww9DDBfhv7fhDHLwYI8UOf4OC4gJt4xLWGWj03DEhg02/xbtDrkeRFzyWJ64e2PG49ECVBHODe0V9EPDfCno890sRuRGKcHMMg9GMfJzgJ0fsnEh2TisLei2k3DpMoxAGO0dtz0hCC2HdJhMmpCmGvl1iX4SnrHgmxFza7aiV7ie8HpzpGkRu/ZGmj+MSO3CSxFW0yDHw38U4bGHhxYFU89Pz8L912VFzYDQAA", "variations_country": "CN", "variations_crash_streak": 0, "variations_failed_to_fetch_seed_streak": 0, "variations_google_groups": {"Default": []}, "variations_last_fetch_time": "13389285606180154", "variations_last_runtime_fetch_time": "13389285606730409", "variations_limited_entropy_synthetic_trial_seed_v2": "5", "variations_permanent_consistency_country": ["135.0.3179.73", "CN"], "variations_runtime_compressed_seed": "H4sIAAAAAAAAAG2QS4/TQBCE/0uf3WJ6ep6WOKycQABpk31JWWEOxhmiIOyA7QiFyP8djSfJrgTH6q6a6a9OUOzbb7vth1kPOZxKmBcPJeQlrPAeSTiprUBComyFM1SSnUOJpoSshPlmG2b7ptq1N/Ww27f9q6BnqXQM+iwN2Ho2caDOA6mM58vT019GKCRkM2nrWEx+lyQbOUmbJAmpUSPxJA2T0ahQpqxmqRglJq8SwjPyZcnOKoUWfdoyOe9RelReTYxE2hlSKPGi2TG5eNmV+v7QDrsmpOpeUbOzRqBEPzkfwrYJ7VDFbl5MyrKgiJLAiLT3Llalz5rYk0apX5rxjjlWkwCMEXY656rsNW2EcxIJZSJn4STyeadkBCeUJYyQ/csB+Qnmzc/h+N9NaKuvP8K7UA2HLvSQf4YQuhq+jGMGi1BtQtdHW7E/tEN3LPabADkUt/Gjx2oLOZTw9PH70q1XtKBfv98/1/Vi2QRb/7k9Lh9X4Y0KdHf3vO7WnwL3b0uAcfwL5raZ4Z0CAAA=", "variations_seed_client_version_at_store": "135.0.3179.73", "variations_seed_date": "13389285607000000", "variations_seed_milestone": 135, "variations_seed_runtime_etag": "\"UJjO8XP1H1qwGYccHOme7czNyOTPe/4e1QQYXrXKe3s=\"", "variations_seed_signature": "", "was": {"restarted": false}}