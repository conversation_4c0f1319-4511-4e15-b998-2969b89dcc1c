/* 引入需要的字体 */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', 'Noto Sans SC', sans-serif;
}

:root {
    --primary-color: #45f3ff;
    --dark-bg: #23242a;
    --darker-bg: #1c1c1c;
    --panel-bg: #28292d;
    --text-light: #ffffff;
    --text-gray: #8f8f8f;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
}

body {
    background-color: var(--dark-bg);
    color: var(--text-light);
    min-height: 100vh;
}

.container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(69, 243, 255, 0.2);
}

header h1 {
    color: var(--primary-color);
    font-size: 2em;
    text-shadow: 0 0 10px rgba(69, 243, 255, 0.3);
}

.user-info {
    display: flex;
    align-items: center;
}

.username {
    margin-right: 15px;
    font-weight: 500;
}

.logout-btn {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 8px 15px;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: var(--primary-color);
    color: var(--darker-bg);
}

/* 导航样式 */
nav {
    margin: 20px 0;
}

nav ul {
    display: flex;
    list-style: none;
    background-color: var(--darker-bg);
    border-radius: 8px;
    overflow: hidden;
}

nav ul li {
    flex: 1;
}

nav ul li a {
    display: block;
    padding: 15px 0;
    text-align: center;
    text-decoration: none;
    color: var(--text-light);
    transition: all 0.3s ease;
}

nav ul li a:hover {
    background-color: rgba(69, 243, 255, 0.1);
}

nav ul li a.active {
    background-color: var(--primary-color);
    color: var(--darker-bg);
    font-weight: 500;
}

/* 主内容区域 */
main {
    background-color: var(--panel-bg);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.page-header {
    margin-bottom: 30px;
}

.page-header h2 {
    font-size: 1.8em;
    margin-bottom: 10px;
    color: var(--primary-color);
}

/* 内容容器 */
.content-container {
    display: flex;
    gap: 20px;
    height: calc(100vh - 300px);
    min-height: 600px;
}

.left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 工具面板 */
.tool-panel, .data-panel, .calculator-panel, .chart-controls {
    background-color: var(--darker-bg);
    border-radius: 8px;
    padding: 15px;
}

.tool-panel h3, .data-panel h3, .calculator-panel h3, .chart-controls h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2em;
}

.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.primary-btn {
    background-color: var(--primary-color);
    color: var(--darker-bg);
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.primary-btn:hover {
    background-color: rgba(69, 243, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.secondary-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background-color: rgba(69, 243, 255, 0.1);
}

/* 数据面板 */
.data-panel {
    flex: 2;
    overflow: hidden;
}

.table-container {
    overflow: auto;
    max-height: 300px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    position: sticky;
    top: 0;
    background-color: var(--darker-bg);
    z-index: 1;
}

th, td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

th {
    color: var(--primary-color);
    font-weight: 500;
}

tbody tr:hover {
    background-color: rgba(69, 243, 255, 0.05);
}

/* 计算器面板 */
.calculator-panel {
    flex: 1;
}

.calculator-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    display: flex;
    align-items: center;
}

.form-group label {
    width: 120px;
    color: var(--text-light);
}

.form-group input {
    flex: 1;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid rgba(69, 243, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

.result-container {
    margin-top: 15px;
    padding: 10px;
    background-color: rgba(69, 243, 255, 0.1);
    border-radius: 4px;
    text-align: center;
}

#brittleness-result {
    font-size: 1.5em;
    font-weight: 700;
    color: var(--primary-color);
}

/* 图表容器 */
.chart-container {
    flex: 1;
    background-color: var(--darker-bg);
    border-radius: 8px;
    padding: 15px;
    height: 100%;
}

/* 页脚 */
footer {
    text-align: center;
    padding: 20px 0;
    color: var(--text-gray);
    font-size: 0.9em;
}

/* 加载指示器 */
.loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background-color: rgba(40, 41, 45, 0.9);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(69, 243, 255, 0.3);
    border-radius: 50%;
    border-top-color: #45f3ff;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

#loading-text {
    color: #45f3ff;
    margin-top: 15px;
    font-size: 14px;
}

/* 模态对话框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--panel-bg);
    padding: 20px;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    position: relative;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-gray);
}

.close-btn:hover {
    color: var(--primary-color);
}

.file-input-container {
    display: flex;
    margin: 20px 0;
}

.file-input-container input[type="file"] {
    flex: 1;
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: var(--text-light);
}

.file-info {
    margin: 15px 0;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .content-container {
        flex-direction: column;
        height: auto;
    }
    
    .chart-container {
        height: 400px;
    }
}

@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .form-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .form-group label {
        width: 100%;
        margin-bottom: 5px;
    }
}
