# 静态岩石力学参数法脆性指数计算系统使用说明

## 功能概述

静态岩石力学参数法是基于岩石的物理参数（密度、纵波速度、横波速度）来计算脆性指数的方法。该系统实现了从动态参数到静态参数的转换，并最终计算出脆性指数。

## 计算公式

### 1. 动态杨氏模量计算
```
Ed = 10^-3 × ρ × Vs² × (3Vp² - 4Vs²) / (Vp² - Vs²)
```
其中：
- Ed：动态杨氏模量 (GPa)
- ρ：岩石密度 (kg/m³)
- Vp：纵波速度 (m/s)
- Vs：横波速度 (m/s)

### 2. 动态泊松比计算
```
μd = (Vp² - 2Vs²) / (2(Vp² - Vs²))
```
其中：
- μd：动态泊松比

### 3. 静态杨氏模量计算
```
Es = Ed × 0.5823 + 7.566
```
其中：
- Es：静态杨氏模量 (GPa)

### 4. 静态泊松比计算
```
μs = μd × 0.6648 + 0.0514
```
其中：
- μs：静态泊松比

### 5. 脆性指数计算
```
EBRIT = (Es - Emin) / (Emax - Emin) × 100%
μBRIT = (μmax - μs) / (μmax - μmin) × 100%
BRITe = (EBRIT + μBRIT) / 2
```
其中：
- EBRIT：基于杨氏模量的脆性指数
- μBRIT：基于泊松比的脆性指数
- BRITe：综合脆性指数
- 参数范围：Emin=5GPa, Emax=80GPa, μmin=0.1, μmax=0.4

## 系统功能

### 1. 参数输入
- **岩石密度**：输入岩石密度值（g/cm³）
- **纵波速度**：输入纵波速度值（m/s）
- **横波速度**：输入横波速度值（m/s）
- **计算按钮**：点击后自动计算所有参数和脆性指数

### 2. 数据管理
- **导入数据**：支持Excel文件导入，包含岩石力学参数数据
- **导出数据**：将计算结果导出为Excel文件
- **数据显示**：在数据网格中显示所有参数和计算结果

### 3. 图表功能
- **生成曲线**：根据数据生成脆性指数随深度变化的曲线图
- **重置功能**：清空所有输入和计算结果
- **保存曲线**：将生成的曲线图保存为图片文件

## 使用步骤

### 方法一：单点计算
1. 在参数输入面板中输入岩石密度、纵波速度、横波速度
2. 点击"计算脆性指数"按钮
3. 查看计算结果显示
4. 数据会自动添加到数据表中

### 方法二：批量数据处理
1. 准备Excel文件，包含以下列：
   - 顶深/m
   - 底深/m
   - 密度/(g/cm³)
   - 纵波速度/(m/s)
   - 横波速度/(m/s)
2. 点击"导入数据"按钮，选择Excel文件
3. 系统会自动计算所有行的脆性指数
4. 点击"生成曲线"查看脆性指数随深度的变化
5. 可以导出完整的计算结果

## 数据格式要求

### Excel文件格式
- 第一行为列标题
- 必需列：顶深/m, 底深/m, 密度/(g/cm³), 纵波速度/(m/s), 横波速度/(m/s)
- 可选列：动态杨氏模量/GPa, 动态泊松比, 静态杨氏模量/GPa, 静态泊松比, 脆性指数/%
- 数据类型：数值型

### 参数范围
- 密度：1.5-3.5 g/cm³
- 纵波速度：2000-6000 m/s
- 横波速度：1000-4000 m/s
- 纵波速度 > 横波速度

## 结果解释

### 脆性指数分级
- **高脆性**：BRITe > 60%
- **中等脆性**：40% < BRITe ≤ 60%
- **低脆性**：20% < BRITe ≤ 40%
- **韧性**：BRITe ≤ 20%

### 图表说明
- X轴：脆性指数 (%)
- Y轴：深度 (m)，反向显示（深度增加向下）
- 曲线：显示脆性指数随深度的变化趋势
- 标记点：每个数据点的具体位置

## 注意事项

1. **数据质量**：确保输入数据的准确性和完整性
2. **参数合理性**：检查输入参数是否在合理范围内
3. **计算精度**：系统保留3-4位小数精度
4. **文件格式**：仅支持.xls和.xlsx格式的Excel文件
5. **内存限制**：建议单次处理数据量不超过10000行

## 技术支持

如遇到问题，请检查：
1. 输入数据格式是否正确
2. Excel文件是否损坏
3. 参数值是否在合理范围内
4. 系统是否有足够的内存空间

## 更新日志

- v1.0：初始版本，实现基本的静态岩石力学参数计算功能
- 支持单点计算和批量处理
- 提供图表可视化功能
- 支持数据导入导出
