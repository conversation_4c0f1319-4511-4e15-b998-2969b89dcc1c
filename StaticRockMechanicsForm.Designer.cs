namespace BritSystem
{
    partial class StaticRockMechanicsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea1 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend1 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            this.lblTitle = new System.Windows.Forms.Label();
            this.lblWelcome = new System.Windows.Forms.Label();
            this.btnBack = new System.Windows.Forms.Button();
            this.btnLogout = new System.Windows.Forms.Button();
            this.btnEmergencyExit = new System.Windows.Forms.Button();
            this.pnlParameters = new System.Windows.Forms.Panel();
            this.lblParametersTitle = new System.Windows.Forms.Label();
            this.lblDensity = new System.Windows.Forms.Label();
            this.txtDensity = new System.Windows.Forms.TextBox();
            this.lblVp = new System.Windows.Forms.Label();
            this.txtVp = new System.Windows.Forms.TextBox();
            this.lblVs = new System.Windows.Forms.Label();
            this.txtVs = new System.Windows.Forms.TextBox();
            this.btnCalculate = new System.Windows.Forms.Button();
            this.lblCalculationResult = new System.Windows.Forms.Label();
            this.pnlData = new System.Windows.Forms.Panel();
            this.lblDataTitle = new System.Windows.Forms.Label();
            this.btnImport = new System.Windows.Forms.Button();
            this.btnExport = new System.Windows.Forms.Button();
            this.dgvMechanicsData = new System.Windows.Forms.DataGridView();
            this.pnlChart = new System.Windows.Forms.Panel();
            this.lblChartTitle = new System.Windows.Forms.Label();
            this.btnGenerateCurve = new System.Windows.Forms.Button();
            this.btnReset = new System.Windows.Forms.Button();
            this.btnSaveCurve = new System.Windows.Forms.Button();
            this.chartBrittleness = new System.Windows.Forms.DataVisualization.Charting.Chart();
            this.pnlParameters.SuspendLayout();
            this.pnlData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvMechanicsData)).BeginInit();
            this.pnlChart.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartBrittleness)).BeginInit();
            this.SuspendLayout();
            // 
            // lblTitle
            // 
            this.lblTitle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.lblTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.lblTitle.Font = new System.Drawing.Font("微软雅黑", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point);
            this.lblTitle.ForeColor = System.Drawing.Color.Cyan;
            this.lblTitle.Location = new System.Drawing.Point(0, 0);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(1430, 60);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "静态岩石力学参数法脆性指数计算系统";
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblWelcome
            // 
            this.lblWelcome.AutoSize = true;
            this.lblWelcome.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.lblWelcome.ForeColor = System.Drawing.Color.LightGray;
            this.lblWelcome.Location = new System.Drawing.Point(20, 70);
            this.lblWelcome.Name = "lblWelcome";
            this.lblWelcome.Size = new System.Drawing.Size(233, 21);
            this.lblWelcome.TabIndex = 1;
            this.lblWelcome.Text = "欢迎使用静态岩石力学参数法";
            // 
            // btnBack
            // 
            this.btnBack.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnBack.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnBack.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnBack.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.btnBack.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnBack.Location = new System.Drawing.Point(20, 110);
            this.btnBack.Name = "btnBack";
            this.btnBack.Size = new System.Drawing.Size(120, 35);
            this.btnBack.TabIndex = 2;
            this.btnBack.Text = "返回主页";
            this.btnBack.UseVisualStyleBackColor = false;
            this.btnBack.Click += new System.EventHandler(this.BtnBack_Click);
            // 
            // btnLogout
            // 
            this.btnLogout.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnLogout.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnLogout.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnLogout.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.btnLogout.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnLogout.Location = new System.Drawing.Point(160, 110);
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.Size = new System.Drawing.Size(120, 35);
            this.btnLogout.TabIndex = 3;
            this.btnLogout.Text = "退出登录";
            this.btnLogout.UseVisualStyleBackColor = false;
            this.btnLogout.Click += new System.EventHandler(this.BtnLogout_Click);
            // 
            // btnEmergencyExit
            // 
            this.btnEmergencyExit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEmergencyExit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(30)))), ((int)(((byte)(30)))));
            this.btnEmergencyExit.FlatAppearance.BorderColor = System.Drawing.Color.Red;
            this.btnEmergencyExit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEmergencyExit.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.btnEmergencyExit.ForeColor = System.Drawing.Color.White;
            this.btnEmergencyExit.Location = new System.Drawing.Point(1300, 12);
            this.btnEmergencyExit.Name = "btnEmergencyExit";
            this.btnEmergencyExit.Size = new System.Drawing.Size(115, 30);
            this.btnEmergencyExit.TabIndex = 4;
            this.btnEmergencyExit.Text = "紧急退出";
            this.btnEmergencyExit.UseVisualStyleBackColor = false;
            this.btnEmergencyExit.Click += new System.EventHandler(this.BtnEmergencyExit_Click);
            // 
            // pnlParameters
            // 
            this.pnlParameters.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnlParameters.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.pnlParameters.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.pnlParameters.Controls.Add(this.lblCalculationResult);
            this.pnlParameters.Controls.Add(this.btnCalculate);
            this.pnlParameters.Controls.Add(this.txtVs);
            this.pnlParameters.Controls.Add(this.lblVs);
            this.pnlParameters.Controls.Add(this.txtVp);
            this.pnlParameters.Controls.Add(this.lblVp);
            this.pnlParameters.Controls.Add(this.txtDensity);
            this.pnlParameters.Controls.Add(this.lblDensity);
            this.pnlParameters.Controls.Add(this.lblParametersTitle);
            this.pnlParameters.Location = new System.Drawing.Point(20, 160);
            this.pnlParameters.Name = "pnlParameters";
            this.pnlParameters.Size = new System.Drawing.Size(1390, 120);
            this.pnlParameters.TabIndex = 5;
            // 
            // lblParametersTitle
            // 
            this.lblParametersTitle.AutoSize = true;
            this.lblParametersTitle.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point);
            this.lblParametersTitle.ForeColor = System.Drawing.Color.White;
            this.lblParametersTitle.Location = new System.Drawing.Point(10, 10);
            this.lblParametersTitle.Name = "lblParametersTitle";
            this.lblParametersTitle.Size = new System.Drawing.Size(138, 22);
            this.lblParametersTitle.TabIndex = 0;
            this.lblParametersTitle.Text = "岩石力学参数输入";
            // 
            // lblDensity
            // 
            this.lblDensity.AutoSize = true;
            this.lblDensity.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.lblDensity.ForeColor = System.Drawing.Color.White;
            this.lblDensity.Location = new System.Drawing.Point(20, 50);
            this.lblDensity.Name = "lblDensity";
            this.lblDensity.Size = new System.Drawing.Size(135, 20);
            this.lblDensity.TabIndex = 1;
            this.lblDensity.Text = "岩石密度 ρ (g/cm³):";
            // 
            // txtDensity
            // 
            this.txtDensity.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.txtDensity.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtDensity.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.txtDensity.ForeColor = System.Drawing.Color.White;
            this.txtDensity.Location = new System.Drawing.Point(180, 50);
            this.txtDensity.Name = "txtDensity";
            this.txtDensity.Size = new System.Drawing.Size(100, 25);
            this.txtDensity.TabIndex = 2;
            // 
            // lblVp
            // 
            this.lblVp.AutoSize = true;
            this.lblVp.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.lblVp.ForeColor = System.Drawing.Color.White;
            this.lblVp.Location = new System.Drawing.Point(300, 50);
            this.lblVp.Name = "lblVp";
            this.lblVp.Size = new System.Drawing.Size(130, 20);
            this.lblVp.TabIndex = 3;
            this.lblVp.Text = "纵波速度 Vp (m/s):";
            // 
            // txtVp
            // 
            this.txtVp.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.txtVp.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtVp.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.txtVp.ForeColor = System.Drawing.Color.White;
            this.txtVp.Location = new System.Drawing.Point(460, 50);
            this.txtVp.Name = "txtVp";
            this.txtVp.Size = new System.Drawing.Size(100, 25);
            this.txtVp.TabIndex = 4;
            // 
            // lblVs
            // 
            this.lblVs.AutoSize = true;
            this.lblVs.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.lblVs.ForeColor = System.Drawing.Color.White;
            this.lblVs.Location = new System.Drawing.Point(580, 50);
            this.lblVs.Name = "lblVs";
            this.lblVs.Size = new System.Drawing.Size(130, 20);
            this.lblVs.TabIndex = 5;
            this.lblVs.Text = "横波速度 Vs (m/s):";
            // 
            // txtVs
            // 
            this.txtVs.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.txtVs.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.txtVs.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.txtVs.ForeColor = System.Drawing.Color.White;
            this.txtVs.Location = new System.Drawing.Point(740, 50);
            this.txtVs.Name = "txtVs";
            this.txtVs.Size = new System.Drawing.Size(100, 25);
            this.txtVs.TabIndex = 6;
            // 
            // btnCalculate
            // 
            this.btnCalculate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnCalculate.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnCalculate.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCalculate.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.btnCalculate.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnCalculate.Location = new System.Drawing.Point(20, 85);
            this.btnCalculate.Name = "btnCalculate";
            this.btnCalculate.Size = new System.Drawing.Size(120, 30);
            this.btnCalculate.TabIndex = 7;
            this.btnCalculate.Text = "计算脆性指数";
            this.btnCalculate.UseVisualStyleBackColor = false;
            this.btnCalculate.Click += new System.EventHandler(this.BtnCalculate_Click);
            // 
            // lblCalculationResult
            // 
            this.lblCalculationResult.AutoSize = true;
            this.lblCalculationResult.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.lblCalculationResult.ForeColor = System.Drawing.Color.Yellow;
            this.lblCalculationResult.Location = new System.Drawing.Point(160, 85);
            this.lblCalculationResult.Name = "lblCalculationResult";
            this.lblCalculationResult.Size = new System.Drawing.Size(135, 20);
            this.lblCalculationResult.TabIndex = 8;
            this.lblCalculationResult.Text = "计算结果将在此显示";
            this.lblCalculationResult.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblCalculationResult.AutoSize = false;
            this.lblCalculationResult.Size = new System.Drawing.Size(400, 30);
            //
            // pnlData
            //
            this.pnlData.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.pnlData.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.pnlData.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.pnlData.Controls.Add(this.dgvMechanicsData);
            this.pnlData.Controls.Add(this.btnExport);
            this.pnlData.Controls.Add(this.btnImport);
            this.pnlData.Controls.Add(this.lblDataTitle);
            this.pnlData.Location = new System.Drawing.Point(20, 300);
            this.pnlData.Name = "pnlData";
            this.pnlData.Size = new System.Drawing.Size(600, 600);
            this.pnlData.TabIndex = 6;
            //
            // lblDataTitle
            //
            this.lblDataTitle.AutoSize = true;
            this.lblDataTitle.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point);
            this.lblDataTitle.ForeColor = System.Drawing.Color.White;
            this.lblDataTitle.Location = new System.Drawing.Point(10, 10);
            this.lblDataTitle.Name = "lblDataTitle";
            this.lblDataTitle.Size = new System.Drawing.Size(138, 22);
            this.lblDataTitle.TabIndex = 0;
            this.lblDataTitle.Text = "岩石力学参数数据";
            //
            // btnImport
            //
            this.btnImport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnImport.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnImport.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnImport.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnImport.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.btnImport.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnImport.Location = new System.Drawing.Point(360, 10);
            this.btnImport.Name = "btnImport";
            this.btnImport.Size = new System.Drawing.Size(100, 30);
            this.btnImport.TabIndex = 1;
            this.btnImport.Text = "导入数据";
            this.btnImport.UseVisualStyleBackColor = false;
            this.btnImport.Click += new System.EventHandler(this.BtnImport_Click);
            //
            // btnExport
            //
            this.btnExport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExport.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(50)))), ((int)(((byte)(50)))), ((int)(((byte)(50)))));
            this.btnExport.FlatAppearance.BorderColor = System.Drawing.Color.Cyan;
            this.btnExport.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnExport.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point);
            this.btnExport.ForeColor = System.Drawing.Color.LightSkyBlue;
            this.btnExport.Location = new System.Drawing.Point(470, 10);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(100, 30);
            this.btnExport.TabIndex = 2;
            this.btnExport.Text = "导出数据";
            this.btnExport.UseVisualStyleBackColor = false;
            this.btnExport.Click += new System.EventHandler(this.BtnExport_Click);
            //
            // dgvMechanicsData
            //
            this.dgvMechanicsData.AllowUserToAddRows = false;
            this.dgvMechanicsData.AllowUserToDeleteRows = false;
            this.dgvMechanicsData.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvMechanicsData.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvMechanicsData.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.dgvMechanicsData.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvMechanicsData.ColumnHeadersDefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(80)))), ((int)(((byte)(80)))));
            this.dgvMechanicsData.ColumnHeadersDefaultCellStyle.ForeColor = System.Drawing.Color.White;
            this.dgvMechanicsData.DefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.dgvMechanicsData.DefaultCellStyle.ForeColor = System.Drawing.Color.White;
            this.dgvMechanicsData.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(100)))), ((int)(((byte)(100)))), ((int)(((byte)(100)))));
            this.dgvMechanicsData.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.White;
            this.dgvMechanicsData.EnableHeadersVisualStyles = false;
            this.dgvMechanicsData.ForeColor = System.Drawing.Color.White;
            this.dgvMechanicsData.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(80)))), ((int)(((byte)(80)))));
            this.dgvMechanicsData.Location = new System.Drawing.Point(10, 50);
            this.dgvMechanicsData.MultiSelect = true;
            this.dgvMechanicsData.Name = "dgvMechanicsData";
            this.dgvMechanicsData.ReadOnly = true;
            this.dgvMechanicsData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvMechanicsData.Size = new System.Drawing.Size(580, 540);
            this.dgvMechanicsData.TabIndex = 3;
            
            this.SuspendLayout();
        }

        #endregion
    }
}
