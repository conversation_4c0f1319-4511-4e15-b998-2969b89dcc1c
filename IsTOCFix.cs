using System;

namespace BritSystem.Core
{
    /// <summary>
    /// 列检测辅助类，用于检测列类型
    /// </summary>
    public static class ColumnDetectionHelper
    {
        /// <summary>
        /// 检查列名是否为TOC列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>是否为TOC列</returns>
        public static bool IsTOC(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return false;
                
            // 检查列名是否包含黏土矿物关键词，如果包含则不应该识别为TOC
            if (columnName.Contains("黏土", StringComparison.OrdinalIgnoreCase) ||
                columnName.Contains("粘土", StringComparison.OrdinalIgnoreCase) ||
                columnName.Contains("clay", StringComparison.OrdinalIgnoreCase))
            {
                System.Diagnostics.Debug.WriteLine($"列名 '{columnName}' 包含黏土矿物关键词，不应识别为TOC");
                return false;
            }
            
            // 使用明确的TOC关键词列表进行精确匹配
            string[] tocKeywords = {
                "TOC", "TOC%", "TOC/%", "TOC含量",
                "总有机碳", "总有机碳%", "有机碳", "有机碳%",
                "Total Organic Carbon", "Total Organic Carbon%", "Organic Carbon", "Organic Carbon%"
            };
            
            // 精确匹配
            foreach (string keyword in tocKeywords)
            {
                if (columnName.Equals(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    System.Diagnostics.Debug.WriteLine($"列名 '{columnName}' 精确匹配TOC关键词");
                    return true;
                }
            }
            
            // 部分匹配检查，确保不包含黏土矿物关键词
            if ((columnName.Contains("TOC", StringComparison.OrdinalIgnoreCase) ||
                (columnName.Contains("有机", StringComparison.OrdinalIgnoreCase) && 
                 columnName.Contains("碳", StringComparison.OrdinalIgnoreCase)) ||
                (columnName.Contains("organic", StringComparison.OrdinalIgnoreCase) && 
                 columnName.Contains("carbon", StringComparison.OrdinalIgnoreCase))))
            {
                System.Diagnostics.Debug.WriteLine($"列名 '{columnName}' 部分匹配TOC关键词");
                return true;
            }
            
            return false;
        }
    }
} 