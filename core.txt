// 脆性指数计算服务核心代码

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace BritSystem.Services
{
    /// <summary>
    /// 脆性指数计算服务
    /// </summary>
    public class BrittlenessCalculationService
    {
        /// <summary>
        /// 计算脆性指数
        /// </summary>
        /// <param name="sourceData">源数据表</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        /// <param name="topDepthColumnName">顶深列名</param>
        /// <param name="bottomDepthColumnName">底深列名</param>
        /// <returns>计算结果数据点列表</returns>
        public List<BrittlenessDataPoint> CalculateBrittlenessIndex(
            DataTable sourceData,
            List<string> brittleColumns,
            List<string> ductileColumns,
            string topDepthColumnName,
            string bottomDepthColumnName)
        {
            if (sourceData == null || sourceData.Rows.Count == 0)
            {
                throw new ArgumentException("源数据表为空");
            }

            if (brittleColumns == null || brittleColumns.Count == 0)
            {
                throw new ArgumentException("未指定脆性矿物列");
            }

            if (ductileColumns == null || ductileColumns.Count == 0)
            {
                throw new ArgumentException("未指定塑性矿物列");
            }

            var dataPoints = new List<BrittlenessDataPoint>();

            // 创建列名映射字典
            var brittleMappings = brittleColumns.ToDictionary(col => col, col => col);
            var ductileMappings = ductileColumns.ToDictionary(col => col, col => col);

            // 查找顶深和底深列的实际列名
            string actualTopDepthColumn = FindActualColumnName(sourceData, topDepthColumnName);
            string actualBottomDepthColumn = FindActualColumnName(sourceData, bottomDepthColumnName);

            System.Diagnostics.Debug.WriteLine($"顶深列映射: {topDepthColumnName} -> {actualTopDepthColumn}");
            System.Diagnostics.Debug.WriteLine($"底深列映射: {bottomDepthColumnName} -> {actualBottomDepthColumn}");

            // 遍历数据行
            for (int i = 0; i < sourceData.Rows.Count; i++)
            {
                DataRow row = sourceData.Rows[i];

                // 创建数据点
                var dataPoint = new BrittlenessDataPoint
                {
                    RowIndex = i
                };

                // 设置顶深和底深
                if (!string.IsNullOrEmpty(actualTopDepthColumn) && sourceData.Columns.Contains(actualTopDepthColumn))
                {
                    if (row[actualTopDepthColumn] != DBNull.Value && double.TryParse(row[actualTopDepthColumn].ToString(), out double topDepth))
                    {
                        dataPoint.TopDepth = topDepth;
                        System.Diagnostics.Debug.WriteLine($"行 {i}: 顶深 = {topDepth}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"行 {i}: 顶深值无法解析: {row[actualTopDepthColumn]}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"行 {i}: 找不到顶深列 {actualTopDepthColumn}");
                }

                if (!string.IsNullOrEmpty(actualBottomDepthColumn) && sourceData.Columns.Contains(actualBottomDepthColumn))
                {
                    if (row[actualBottomDepthColumn] != DBNull.Value && double.TryParse(row[actualBottomDepthColumn].ToString(), out double bottomDepth))
                    {
                        dataPoint.BottomDepth = bottomDepth;
                        System.Diagnostics.Debug.WriteLine($"行 {i}: 底深 = {bottomDepth}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"行 {i}: 底深值无法解析: {row[actualBottomDepthColumn]}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"行 {i}: 找不到底深列 {actualBottomDepthColumn}");
                }

                // 计算脆性矿物总和
                double brittleSum = 0;
                foreach (string colName in brittleColumns)
                {
                    string actualColName = FindActualColumnName(sourceData, colName);
                    if (!string.IsNullOrEmpty(actualColName) && sourceData.Columns.Contains(actualColName))
                    {
                        if (row[actualColName] != DBNull.Value && double.TryParse(row[actualColName].ToString(), out double value))
                        {
                            brittleSum += value;
                            dataPoint.BrittleMinerals.Add($"{colName}: {value}");
                            System.Diagnostics.Debug.WriteLine($"行 {i}: 脆性矿物 {colName} ({actualColName}) = {value}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"行 {i}: 找不到脆性矿物列 {colName} ({actualColName})");
                    }
                }

                // 计算塑性矿物总和
                double ductileSum = 0;
                foreach (string colName in ductileColumns)
                {
                    string actualColName = FindActualColumnName(sourceData, colName);
                    if (!string.IsNullOrEmpty(actualColName) && sourceData.Columns.Contains(actualColName))
                    {
                        if (row[actualColName] != DBNull.Value && double.TryParse(row[actualColName].ToString(), out double value))
                        {
                            ductileSum += value;
                            dataPoint.DuctileMinerals.Add($"{colName}: {value}");
                            System.Diagnostics.Debug.WriteLine($"行 {i}: 塑性矿物 {colName} ({actualColName}) = {value}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"行 {i}: 找不到塑性矿物列 {colName} ({actualColName})");
                    }
                }

                // 计算脆性指数
                if (brittleSum + ductileSum > 0)
                {
                    dataPoint.BrittleIndex = (brittleSum / (brittleSum + ductileSum)) * 100;
                    System.Diagnostics.Debug.WriteLine($"行 {i}: 脆性指数 = {dataPoint.BrittleIndex:F2}%");
                }
                else
                {
                    dataPoint.BrittleIndex = 0;
                    System.Diagnostics.Debug.WriteLine($"行 {i}: 脆性指数 = 0% (脆性矿物总量和塑性矿物总量均为0)");
                }

                // 生成GeoID
                dataPoint.GenerateGeoID();
                System.Diagnostics.Debug.WriteLine($"行 {i}: GeoID = {dataPoint.GeoID}");

                // 添加到数据点列表
                dataPoints.Add(dataPoint);
            }

            return dataPoints;
        }

        /// <summary>
        /// 查找列的实际名称
        /// </summary>
        /// <param name="sourceData">源数据表</param>
        /// <param name="columnName">列名</param>
        /// <returns>实际列名</returns>
        private string FindActualColumnName(DataTable sourceData, string columnName)
        {
            // 1. 直接匹配
            if (sourceData.Columns.Contains(columnName))
            {
                return columnName;
            }

            // 2. 尝试模糊匹配
            string cleanColumnName = columnName.Replace("%", "").Trim().ToLower();
            foreach (DataColumn col in sourceData.Columns)
            {
                string cleanColName = col.ColumnName.Replace("%", "").Trim().ToLower();
                if (cleanColName.Contains(cleanColumnName) || cleanColumnName.Contains(cleanColName))
                {
                    System.Diagnostics.Debug.WriteLine($"模糊匹配成功: {columnName} -> {col.ColumnName}");
                    return col.ColumnName;
                }
            }

            // 3. 特殊匹配 - 顶深和底深
            if (columnName.Contains("顶深"))
            {
                foreach (DataColumn col in sourceData.Columns)
                {
                    if (col.ColumnName.Contains("顶深") || col.ColumnName.Contains("顶深度") ||
                        col.ColumnName.ToLower().Contains("top") || col.ColumnName.ToLower().Contains("depth"))
                    {
                        System.Diagnostics.Debug.WriteLine($"特殊匹配顶深成功: {columnName} -> {col.ColumnName}");
                        return col.ColumnName;
                    }
                }
            }
            else if (columnName.Contains("底深"))
            {
                foreach (DataColumn col in sourceData.Columns)
                {
                    if (col.ColumnName.Contains("底深") || col.ColumnName.Contains("底深度") ||
                        col.ColumnName.ToLower().Contains("bottom") || col.ColumnName.ToLower().Contains("depth"))
                    {
                        System.Diagnostics.Debug.WriteLine($"特殊匹配底深成功: {columnName} -> {col.ColumnName}");
                        return col.ColumnName;
                    }
                }
            }

            // 4. 特殊匹配 - 矿物名称
            Dictionary<string, List<string>> brittleMappings = new Dictionary<string, List<string>>
            {
                { "石英%", new List<string> { "石英", "quartz", "SiO2" } },
                { "斜长石%", new List<string> { "斜长石", "长石", "plagioclase", "feldspar" } },
                { "钾长石%", new List<string> { "钾长石", "正长石", "k-feldspar", "k-spar" } },
                { "白云石%", new List<string> { "白云石", "dolomite" } },
                { "菱铁矿%", new List<string> { "菱铁矿", "siderite" } }
            };

            Dictionary<string, List<string>> ductileMappings = new Dictionary<string, List<string>>
            {
                { "黏土矿物总量%", new List<string> { "黏土", "黏土矿物", "clay", "clay minerals" } },
                { "高岭石%", new List<string> { "高岭石", "kaolinite" } },
                { "绿泥石%", new List<string> { "绿泥石", "chlorite" } }
            };

            // 检查脆性矿物映射
            foreach (var mapping in brittleMappings)
            {
                if (cleanColumnName.Contains(mapping.Key.ToLower().Replace("%", "")) ||
                    mapping.Value.Any(v => cleanColumnName.Contains(v.ToLower())))
                {
                    foreach (DataColumn col in sourceData.Columns)
                    {
                        string cleanColName = col.ColumnName.Replace("%", "").Trim().ToLower();
                        if (mapping.Value.Any(v => cleanColName.Contains(v.ToLower())))
                        {
                            System.Diagnostics.Debug.WriteLine($"脆性矿物映射成功: {columnName} -> {col.ColumnName}");
                            return col.ColumnName;
                        }
                    }
                }
            }

            // 检查塑性矿物映射
            foreach (var mapping in ductileMappings)
            {
                if (cleanColumnName.Contains(mapping.Key.ToLower().Replace("%", "")) ||
                    mapping.Value.Any(v => cleanColumnName.Contains(v.ToLower())))
                {
                    foreach (DataColumn col in sourceData.Columns)
                    {
                        string cleanColName = col.ColumnName.Replace("%", "").Trim().ToLower();
                        if (mapping.Value.Any(v => cleanColName.Contains(v.ToLower())))
                        {
                            System.Diagnostics.Debug.WriteLine($"塑性矿物映射成功: {columnName} -> {col.ColumnName}");
                            return col.ColumnName;
                        }
                    }
                }
            }

            // 5. 尝试使用列标题进行匹配
            foreach (DataColumn col in sourceData.Columns)
            {
                if (col.Caption.Contains(columnName) || columnName.Contains(col.Caption))
                {
                    System.Diagnostics.Debug.WriteLine($"列标题匹配成功: {columnName} -> {col.ColumnName}");
                    return col.ColumnName;
                }
            }

            System.Diagnostics.Debug.WriteLine($"警告: 找不到列 '{columnName}' 的匹配");
            return columnName; // 如果找不到匹配，返回原始列名
        }

        /// <summary>
        /// 将计算结果转换为DataTable
        /// </summary>
        /// <param name="dataPoints">数据点列表</param>
        /// <param name="sourceData">源数据表</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        /// <returns>结果数据表</returns>
        public DataTable ConvertToDataTable(
            List<BrittlenessDataPoint> dataPoints,
            DataTable sourceData,
            List<string> brittleColumns,
            List<string> ductileColumns)
        {
            // 创建结果数据表
            DataTable resultData = new DataTable();
            resultData.Columns.Add("GeoID", typeof(string));
            resultData.Columns.Add("顶深/m", typeof(double));
            resultData.Columns.Add("底深/m", typeof(double));
            resultData.Columns.Add("脆性指数", typeof(double));

            // 添加脆性矿物列
            foreach (string colName in brittleColumns)
            {
                if (!resultData.Columns.Contains(colName))
                {
                    resultData.Columns.Add(colName, typeof(double));
                }
            }

            // 添加塑性矿物列
            foreach (string colName in ductileColumns)
            {
                if (!resultData.Columns.Contains(colName))
                {
                    resultData.Columns.Add(colName, typeof(double));
                }
            }

            // 添加数据行
            foreach (var dataPoint in dataPoints)
            {
                DataRow resultRow = resultData.NewRow();
                resultRow["GeoID"] = dataPoint.GeoID;
                resultRow["顶深/m"] = dataPoint.TopDepth;
                resultRow["底深/m"] = dataPoint.BottomDepth;
                resultRow["脆性指数"] = dataPoint.BrittleIndex;

                // 添加脆性和塑性矿物值
                if (dataPoint.RowIndex >= 0 && dataPoint.RowIndex < sourceData.Rows.Count)
                {
                    DataRow sourceRow = sourceData.Rows[dataPoint.RowIndex];

                    foreach (string colName in brittleColumns)
                    {
                        string actualColName = FindActualColumnName(sourceData, colName);
                        if (resultData.Columns.Contains(colName) && sourceData.Columns.Contains(actualColName) && sourceRow[actualColName] != DBNull.Value)
                        {
                            resultRow[colName] = sourceRow[actualColName];
                        }
                    }

                    foreach (string colName in ductileColumns)
                    {
                        string actualColName = FindActualColumnName(sourceData, colName);
                        if (resultData.Columns.Contains(colName) && sourceData.Columns.Contains(actualColName) && sourceRow[actualColName] != DBNull.Value)
                        {
                            resultRow[colName] = sourceRow[actualColName];
                        }
                    }
                }

                resultData.Rows.Add(resultRow);
            }

            return resultData;
        }
    }

    /// <summary>
    /// 脆性指数数据点模型
    /// </summary>
    public class BrittlenessDataPoint
    {
        /// <summary>
        /// 地质点唯一标识
        /// </summary>
        public string GeoID { get; set; }

        /// <summary>
        /// 顶深
        /// </summary>
        public double TopDepth { get; set; }

        /// <summary>
        /// 底深
        /// </summary>
        public double BottomDepth { get; set; }

        /// <summary>
        /// 脆性指数
        /// </summary>
        public double BrittleIndex { get; set; }

        /// <summary>
        /// 脆性矿物列表
        /// </summary>
        public List<string> BrittleMinerals { get; set; } = new List<string>();

        /// <summary>
        /// 塑性矿物列表
        /// </summary>
        public List<string> DuctileMinerals { get; set; } = new List<string>();

        /// <summary>
        /// 原始数据行索引
        /// </summary>
        public int RowIndex { get; set; } = -1;

        /// <summary>
        /// 列名映射字典
        /// </summary>
        public Dictionary<string, string> ColumnMappings { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 生成唯一的GeoID
        /// </summary>
        public void GenerateGeoID()
        {
            // 使用深度和脆性指数生成唯一的GeoID
            GeoID = $"GEO_{TopDepth:F2}_{BottomDepth:F2}_{BrittleIndex:F4}";
            // 添加哈希校验保证唯一性
            GeoID += $"_{Math.Abs(GeoID.GetHashCode()):X8}";
        }

        /// <summary>
        /// 生成包含映射信息的GeoID
        /// </summary>
        public void GenerateGeoIDWithMappings(Dictionary<string, string> brittleMappings, Dictionary<string, string> ductileMappings)
        {
            // 基本GeoID生成
            GenerateGeoID();

            // 保存列名映射
            ColumnMappings.Clear();

            // 添加脆性矿物映射
            foreach (var mapping in brittleMappings)
            {
                ColumnMappings[mapping.Key] = mapping.Value;
            }

            // 添加塑性矿物映射
            foreach (var mapping in ductileMappings)
            {
                ColumnMappings[mapping.Key] = mapping.Value;
            }
        }
    }

    /// <summary>
    /// 高亮显示选中列的修复方法
    /// </summary>
    public void HighlightSelectedColumns()
    {
        try
        {
            // 重置所有列的样式
            foreach (DataGridViewColumn col in dgvResult.Columns)
            {
                col.DefaultCellStyle.BackColor = Color.White;
                col.HeaderCell.Style.BackColor = Color.White;
            }

            // 输出调试信息
            System.Diagnostics.Debug.WriteLine("开始高亮显示选中列");
            System.Diagnostics.Debug.WriteLine($"脆性矿物列数量: {_brittleColumns.Count}");
            System.Diagnostics.Debug.WriteLine($"塑性矿物列数量: {_ductileColumns.Count}");
            System.Diagnostics.Debug.WriteLine($"顶深列名: {topDepthColumnName}, 底深列名: {bottomDepthColumnName}");

            // 高亮显示脆性矿物列
            System.Diagnostics.Debug.WriteLine($"尝试高亮 {_brittleColumns.Count} 个脆性矿物列");
            foreach (string columnName in _brittleColumns)
            {
                // 使用精确匹配查找列索引
                int colIndex = -1;
                for (int i = 0; i < dgvResult.Columns.Count; i++)
                {
                    if (string.Equals(dgvResult.Columns[i].HeaderText, columnName, StringComparison.OrdinalIgnoreCase))
                    {
                        colIndex = i;
                        break;
                    }
                }

                if (colIndex >= 0)
                {
                    dgvResult.Columns[colIndex].DefaultCellStyle.BackColor = Color.FromArgb(80, 100, 120);
                    dgvResult.Columns[colIndex].HeaderCell.Style.BackColor = Color.FromArgb(80, 100, 120);
                    System.Diagnostics.Debug.WriteLine($"通过映射成功高亮脆性矿物列: {columnName} -> {dgvResult.Columns[colIndex].Name}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告: 找不到脆性矿物列 '{columnName}' 的匹配");
                }
            }

            // 高亮显示塑性矿物列
            System.Diagnostics.Debug.WriteLine($"尝试高亮 {_ductileColumns.Count} 个塑性矿物列");
            foreach (string columnName in _ductileColumns)
            {
                // 使用精确匹配查找列索引
                int colIndex = -1;
                for (int i = 0; i < dgvResult.Columns.Count; i++)
                {
                    if (string.Equals(dgvResult.Columns[i].HeaderText, columnName, StringComparison.OrdinalIgnoreCase))
                    {
                        colIndex = i;
                        break;
                    }
                }

                if (colIndex >= 0)
                {
                    dgvResult.Columns[colIndex].DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                    dgvResult.Columns[colIndex].HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                    System.Diagnostics.Debug.WriteLine($"通过映射成功高亮塑性矿物列: {columnName} -> {dgvResult.Columns[colIndex].Name}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告: 找不到塑性矿物列 '{columnName}' 的匹配");
                }
            }

            // 高亮显示顶深列
            System.Diagnostics.Debug.WriteLine($"尝试高亮顶深列: 顶深/m, 存在: {dgvResult.Columns.Contains("顶深/m")}");
            int topDepthIndex = -1;
            for (int i = 0; i < dgvResult.Columns.Count; i++)
            {
                if (dgvResult.Columns[i].HeaderText == "顶深/m" || 
                    (topDepthColumnName != null && dgvResult.Columns[i].HeaderText == topDepthColumnName))
                {
                    topDepthIndex = i;
                    break;
                }
            }

            if (topDepthIndex >= 0)
            {
                dgvResult.Columns[topDepthIndex].DefaultCellStyle.BackColor = Color.SeaGreen;
                dgvResult.Columns[topDepthIndex].HeaderCell.Style.BackColor = Color.SeaGreen;
                System.Diagnostics.Debug.WriteLine($"成功高亮顶深列: {dgvResult.Columns[topDepthIndex].HeaderText}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"警告: 找不到列 '顶深/m' 的匹配");
            }

            // 高亮显示底深列
            System.Diagnostics.Debug.WriteLine($"尝试高亮底深列: 底深/m, 存在: {dgvResult.Columns.Contains("底深/m")}");
            int bottomDepthIndex = -1;
            for (int i = 0; i < dgvResult.Columns.Count; i++)
            {
                if (dgvResult.Columns[i].HeaderText == "底深/m" || 
                    (bottomDepthColumnName != null && dgvResult.Columns[i].HeaderText == bottomDepthColumnName))
                {
                    bottomDepthIndex = i;
                    break;
                }
            }

            if (bottomDepthIndex >= 0)
            {
                dgvResult.Columns[bottomDepthIndex].DefaultCellStyle.BackColor = Color.SeaGreen;
                dgvResult.Columns[bottomDepthIndex].HeaderCell.Style.BackColor = Color.SeaGreen;
                System.Diagnostics.Debug.WriteLine($"成功高亮底深列: {dgvResult.Columns[bottomDepthIndex].HeaderText}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"警告: 找不到列 '底深/m' 的匹配");
            }

            System.Diagnostics.Debug.WriteLine("完成高亮显示选中列");
            
            // 刷新数据网格视图
            dgvResult.Refresh();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"高亮显示选中列时出错: {ex.Message}\n{ex.StackTrace}");
        }
    }
}
