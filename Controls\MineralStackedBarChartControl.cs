using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using BritSystem.Models;
using System.Diagnostics;

namespace BritSystem.Controls
{
    /// <summary>
    /// 矿物堆叠柱状图控件
    /// </summary>
    public class MineralStackedBarChartControl : UserControl
    {
        #region 私有字段

        private Chart _chart;
        private DataTable _resultData;
        private List<string> _brittleMinerals;
        private List<string> _ductileMinerals;
        private Dictionary<string, Color> _mineralColors;
        private TableLayoutPanel mainLayout;
        private TableLayoutPanel _legendPanel;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化矿物堆叠柱状图控件
        /// </summary>
        public MineralStackedBarChartControl()
        {
            InitializeComponent();
            InitializeDefaultColors();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取或设置结果数据
        /// </summary>
        public DataTable ResultData
        {
            get { return _resultData; }
            set
            {
                _resultData = value;
                if (_resultData != null)
                {
                    LogMessage($"结果数据已设置，行数: {_resultData.Rows.Count}");
                    UpdateChart();
                }
            }
        }

        /// <summary>
        /// 获取或设置脆性矿物列表
        /// </summary>
        public List<string> BrittleMinerals
        {
            get { return _brittleMinerals; }
            set
            {
                _brittleMinerals = value ?? new List<string>(); // 防止null
                LogMessage($"脆性矿物列表已设置: {string.Join(", ", _brittleMinerals)}");
                if (_resultData != null && _ductileMinerals != null) // 双列表检查
                {
                    UpdateChart();
                }
            }
        }

        /// <summary>
        /// 获取或设置塑性矿物列表
        /// </summary>
        public List<string> DuctileMinerals
        {
            get { return _ductileMinerals; }
            set
            {
                _ductileMinerals = value;
                LogMessage($"塑性矿物列表已设置: {string.Join(", ", _ductileMinerals)}");
                if (_resultData != null)
                {
                    UpdateChart();
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            mainLayout = new TableLayoutPanel();
            _legendPanel = new TableLayoutPanel();
            mainLayout.SuspendLayout();
            SuspendLayout();
            // 
            // mainLayout
            // 
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            mainLayout.Controls.Add(_legendPanel, 0, 1);
            mainLayout.Location = new Point(0, 0);
            mainLayout.Name = "mainLayout";
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 90F));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 10F));
            mainLayout.Size = new Size(200, 100);
            mainLayout.TabIndex = 0;
            // 
            // _legendPanel
            // 
            _legendPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            _legendPanel.Location = new Point(3, 93);
            _legendPanel.Name = "_legendPanel";
            _legendPanel.Size = new Size(194, 4);
            _legendPanel.TabIndex = 0;
            // 
            // MineralStackedBarChartControl
            // 
            Controls.Add(mainLayout);
            MinimumSize = new Size(800, 600);
            Name = "MineralStackedBarChartControl";
            Size = new Size(2040, 681);
            mainLayout.ResumeLayout(false);
            ResumeLayout(false);
        }

        /// <summary>
        /// 初始化默认颜色
        /// </summary>
        private void InitializeDefaultColors()
        {
            _mineralColors = new Dictionary<string, Color>(StringComparer.OrdinalIgnoreCase)
            {
                { "黏土矿物总量", Color.FromArgb(153, 102, 51) },
                { "黏土矿物总量%", Color.FromArgb(153, 102, 51) }, // 添加带%的键
                { "黏土", Color.FromArgb(153, 102, 51) }, // 添加黏土键
                { "石英", Color.FromArgb(255, 51, 204) },
                { "石英%", Color.FromArgb(255, 51, 204) }, // 添加带%的键
                { "长石", Color.FromArgb(0, 204, 255) },
                { "长石%", Color.FromArgb(0, 204, 255) },
                { "方解石", Color.FromArgb(102, 255, 102) },
                { "方解石%", Color.FromArgb(102, 255, 102) },
                { "白云石", Color.FromArgb(102, 255, 102) },
                { "白云石%", Color.FromArgb(102, 255, 102) },
                { "碳酸盐矿物", Color.FromArgb(50, 180, 50) }, // 添加碳酸盐矿物键
                { "碳酸盐矿物%", Color.FromArgb(50, 180, 50) }, // 添加碳酸盐矿物%键
                { "铁矿", Color.FromArgb(128, 128, 128) },
                { "铁矿%", Color.FromArgb(128, 128, 128) },
                { "黑云母", Color.FromArgb(255, 255, 102) },
                { "黑云母%", Color.FromArgb(255, 255, 102) },
                { "角闪石", Color.FromArgb(102, 0, 102) },
                { "角闪石%", Color.FromArgb(102, 0, 102) }
            };

            LogMessage("默认矿物颜色已初始化");
        }

        /// <summary>
        /// 日志输出
        /// </summary>
        /// <param name="message">消息内容</param>
        private void LogMessage(string message)
        {
            // 仅输出到调试窗口
            Debug.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss.fff")}] {message}");
        }

        /// <summary>
        /// 图例颜色方块点击事件
        /// </summary>
        private void ColorBox_Click(object sender, EventArgs e)
        {
            Panel colorBox = sender as Panel;
            if (colorBox != null && colorBox.Tag != null)
            {
                string mineral = colorBox.Tag.ToString();
                LogMessage($"点击了矿物颜色方块: {mineral}");

                // 显示颜色选择对话框
                ColorDialog colorDialog = new ColorDialog
                {
                    Color = colorBox.BackColor,
                    FullOpen = true
                };

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    // 更新颜色
                    colorBox.BackColor = colorDialog.Color;
                    _mineralColors[mineral] = colorDialog.Color;
                    LogMessage($"更新矿物 '{mineral}' 颜色为: {colorDialog.Color}");

                    // 更新图表
                    UpdateChart();
                }
            }
        }

        /// <summary>
        /// 更新图表
        /// </summary>
        private void UpdateChart()
        {
            LogMessage("===== 开始更新图表 =====");

            if (_resultData == null || _resultData.Rows.Count == 0)
            {
                LogMessage("错误: 结果数据为空或没有行");
                return;
            }

            if (_brittleMinerals == null || _ductileMinerals == null)
            {
                LogMessage("错误: 脆性或塑性矿物列表未设置");
                return;
            }

            // 清除现有元素
            _chart.Series.Clear();
            _chart.Titles.Clear();
            _chart.ChartAreas.Clear();
            _chart.Legends.Clear();
            LogMessage("已清除现有图表元素");

            // 添加标题
            _chart.Titles.Add(new Title("矿物含量分布", Docking.Top,
                new Font("Microsoft YaHei", 14, FontStyle.Bold), Color.Black));
            LogMessage("已添加图表标题");

            // 创建图表区域
            ChartArea chartArea = new ChartArea("MainArea");

            // 设置X轴为矿物含量百分比
            chartArea.AxisX.Title = "矿物含量/%";
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisX.Interval = 25;  // 每25%一个刻度（与图2一致）
            chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
            chartArea.AxisX.LabelStyle.Format = "F0";
            chartArea.AxisX.IsMarginVisible = true;

            // 设置Y轴为深度
            chartArea.AxisY.Title = "深度/m";
            chartArea.AxisY.IsReversed = true;  // 深度轴反转，使小值在顶部
            chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
            chartArea.AxisY.LabelStyle.Format = "F0";
            chartArea.AxisY.IsMarginVisible = true;

            // 设置坐标轴位置
            chartArea.Position = new ElementPosition(5, 10, 90, 85);
            chartArea.AxisY.IsMarksNextToAxis = true;
            chartArea.AxisY.ArrowStyle = AxisArrowStyle.Triangle;
            chartArea.AxisX.ArrowStyle = AxisArrowStyle.Triangle;
            chartArea.AxisX.IsMarginVisible = true;  // 确保X轴边距可见

            // 添加图例（参考图2中的底部图例）
            Legend legend = new Legend("MainLegend");
            legend.Docking = Docking.Bottom;
            legend.Alignment = StringAlignment.Center;
            legend.LegendStyle = LegendStyle.Row;
            legend.IsTextAutoFit = true;
            _chart.Legends.Add(legend);

            _chart.ChartAreas.Add(chartArea);
            LogMessage("已创建图表区域");

            // 获取所有矿物列（使用原始列名）
            List<string> allMinerals = new List<string>();
            if (_brittleMinerals != null) allMinerals.AddRange(_brittleMinerals);
            if (_ductileMinerals != null) allMinerals.AddRange(_ductileMinerals);
            LogMessage($"所有矿物列表: {string.Join(", ", allMinerals)}");

            // 新增：获取数据表所有实际列名
            List<string> actualColumns = _resultData.Columns.Cast<DataColumn>()
                .Select(c => c.ColumnName)
                .ToList();
            LogMessage($"数据表实际列名: {string.Join(", ", actualColumns)}");

            // 处理深度数据
            List<double> depths = _resultData.Rows.Cast<DataRow>()
                .Where(row => row["顶深/m"] != DBNull.Value)
                .Select(row => Convert.ToDouble(row["顶深/m"]))
                .Distinct()
                .OrderBy(d => d)
                .ToList();

            LogMessage($"找到 {depths.Count} 个深度点: {string.Join(", ", depths)}");

            // 只显示有数据的深度点
            if (depths.Count == 0)
            {
                LogMessage("警告: 未找到深度数据，添加默认深度1000");
                depths.Add(1000);
            }

            // 设置柱子宽度
            double pointWidth = 0.9; // 增加到0.9，使数据条更宽
            LogMessage($"柱子宽度设置为: {pointWidth}");

            // 遍历所有矿物（使用原始列名）
            foreach (string mineral in allMinerals)
            {
                // 提取纯净矿物名称用于显示（移除冒号及后续内容）
                string pureMineral = mineral.Contains(":")
                    ? mineral.Split(':')[0].Trim()
                    : mineral;

                LogMessage($"创建矿物系列 (显示名: {pureMineral}, 原始列名: {mineral})");

                // 增强：查找匹配的列名（多种匹配策略）
                string actualColumnName = actualColumns.FirstOrDefault(
                    col => col.Equals(mineral, StringComparison.OrdinalIgnoreCase) ||
                           col.Equals(pureMineral, StringComparison.OrdinalIgnoreCase) ||
                           col.Replace("%", "").Equals(mineral.Replace("%", ""), StringComparison.OrdinalIgnoreCase) ||
                           col.ToLower().Contains(pureMineral.ToLower().Replace("%", "")) ||
                           pureMineral.ToLower().Replace("%", "").Contains(col.ToLower().Replace("%", ""))
                );

                // 如果找不到对应列名，尝试使用原始列名（假设数据表使用了与属性不同的命名）
                if (string.IsNullOrEmpty(actualColumnName))
                {
                    LogMessage($"警告: 未找到与'{mineral}'匹配的列，尝试使用列名'{mineral}'本身");
                    // 判断是否需要继续使用该矿物
                    if (!actualColumns.Contains(mineral) && !actualColumns.Contains(pureMineral))
                    {
                        LogMessage($"错误: 数据表中找不到与'{mineral}'或'{pureMineral}'匹配的列，且列不存在");
                        continue; // 跳过这个矿物
                    }
                    actualColumnName = mineral; // 使用原始列名
                }

                LogMessage($"匹配到实际列名: {actualColumnName}");

                // 创建新系列
                Series series = new Series(pureMineral) // 使用纯矿物名作为系列名
                {
                    ChartType = SeriesChartType.StackedBar100,  // 100%堆叠柱状图
                    IsVisibleInLegend = true, // 在图例中显示
                    BorderWidth = 1,
                    BorderColor = Color.Black,
                    Legend = "MainLegend" // 指定使用的图例
                };

                // 设置数据条宽度
                series["PointWidth"] = "0.9";  // 更宽的数据条

                // 设置颜色
                Color mineralColor = _mineralColors.ContainsKey(pureMineral)
                    ? _mineralColors[pureMineral]
                    : Color.FromArgb(
                        new Random(mineral.GetHashCode()).Next(256),
                        new Random(mineral.GetHashCode()).Next(256),
                        new Random(mineral.GetHashCode()).Next(256));

                series.Color = mineralColor;
                LogMessage($"设置矿物 '{pureMineral}' 颜色为: {mineralColor}");

                // 添加数据点 - 适合图2的样式
                LogMessage($"为矿物 '{pureMineral}' (实际列名: {actualColumnName}) 添加数据点...");
                foreach (double depth in depths)
                {
                    // 查找匹配深度的行
                    DataRow[] depthRows = _resultData.Select($"[顶深/m] = {depth}");
                    LogMessage($"在深度 {depth:F1}m 找到 {depthRows.Length} 行数据");

                    double value = 0;
                    if (depthRows.Length > 0)
                    {
                        // 增强：多种方式匹配列名
                        bool columnFound = false;

                        // 方法1：直接尝试通过列名查询
                        if (depthRows[0].Table.Columns.Contains(actualColumnName) &&
                            depthRows[0][actualColumnName] != DBNull.Value)
                        {
                            try
                            {
                                value = Convert.ToDouble(depthRows[0][actualColumnName]);
                                LogMessage($"深度 {depth:F1}m 的矿物列 '{actualColumnName}' 含量: {value:F2}%");
                                columnFound = true;
                            }
                            catch (FormatException ex)
                            {
                                LogMessage($"警告: 列 '{actualColumnName}' 包含非数字值 '{depthRows[0][actualColumnName]}': {ex.Message}");
                                // 继续使用其他方法
                            }
                        }

                        // 方法2：如果方法1失败，尝试多种方式匹配列名
                        if (!columnFound)
                        {
                            // 尝试多种方式匹配列名
                            foreach (DataColumn column in depthRows[0].Table.Columns)
                            {
                                string colName = column.ColumnName.ToLower();
                                string pureMineralLower = pureMineral.ToLower().Replace("%", "");
                                string mineralLower = mineral.ToLower().Replace("%", "");

                                // 方式1: 部分包含匹配
                                if (colName.Contains(pureMineralLower) || colName.Contains(mineralLower) ||
                                    pureMineralLower.Contains(colName) || mineralLower.Contains(colName))
                                {
                                    if (depthRows[0][column] != DBNull.Value)
                                    {
                                        try
                                        {
                                            value = Convert.ToDouble(depthRows[0][column]);
                                            LogMessage($"通过部分包含匹配找到列: {column.ColumnName}, 深度 {depth:F1}m 含量: {value:F2}%");
                                            columnFound = true;
                                            break;
                                        }
                                        catch (FormatException ex)
                                        {
                                            LogMessage($"警告: 列 '{column.ColumnName}' 包含非数字值 '{depthRows[0][column]}': {ex.Message}");
                                            // 继续检查其他列
                                        }
                                    }
                                }

                                // 方式2: 关键词匹配（针对特定矿物名称）
                                if ((pureMineralLower.Contains("碳酸盐") && colName.Contains("碳酸")) ||
                                    (pureMineralLower.Contains("石英") && colName.Contains("石英")) ||
                                    (pureMineralLower.Contains("黏土") && colName.Contains("黏土")) ||
                                    (pureMineralLower.Contains("长石") && colName.Contains("长石")))
                                {
                                    if (depthRows[0][column] != DBNull.Value)
                                    {
                                        try
                                        {
                                            value = Convert.ToDouble(depthRows[0][column]);
                                            LogMessage($"通过关键词匹配找到列: {column.ColumnName}, 深度 {depth:F1}m 含量: {value:F2}%");
                                            columnFound = true;
                                            break;
                                        }
                                        catch (FormatException ex)
                                        {
                                            LogMessage($"警告: 列 '{column.ColumnName}' 包含非数字值 '{depthRows[0][column]}': {ex.Message}");
                                            // 继续检查其他列
                                        }
                                    }
                                }

                                // 方式3: 尝试按列索引匹配
                                if (actualColumns.IndexOf(column.ColumnName) >= 0 &&
                                    actualColumns.IndexOf(column.ColumnName) < allMinerals.Count)
                                {
                                    int idx = actualColumns.IndexOf(column.ColumnName);
                                    if (idx < allMinerals.Count &&
                                        (allMinerals[idx].ToLower().Contains(pureMineralLower) ||
                                         pureMineralLower.Contains(allMinerals[idx].ToLower())))
                                    {
                                        if (depthRows[0][column] != DBNull.Value)
                                        {
                                            try
                                            {
                                                value = Convert.ToDouble(depthRows[0][column]);
                                                LogMessage($"通过列索引匹配找到列: {column.ColumnName}, 深度 {depth:F1}m 含量: {value:F2}%");
                                                columnFound = true;
                                                break;
                                            }
                                            catch (FormatException ex)
                                            {
                                                LogMessage($"警告: 列 '{column.ColumnName}' 包含非数字值 '{depthRows[0][column]}': {ex.Message}");
                                                // 继续检查其他列
                                            }
                                        }
                                    }
                                }
                            }

                            if (!columnFound)
                            {
                                LogMessage($"警告: 所有方法均无法找到矿物列 '{actualColumnName}'");
                            }
                        }

                        if (!columnFound)
                        {
                            LogMessage($"警告: 所有方法均无法找到矿物列 '{actualColumnName}'");
                        }
                    }
                    else
                    {
                        LogMessage($"警告: 深度 {depth:F1}m 没有匹配的数据行");
                    }

                    // 创建数据点
                    DataPoint point = new DataPoint();

                    int depthIndex = depths.IndexOf(depth);
                    point.XValue = depthIndex; // X值表示深度索引（对应Y轴标签显示深度值）
                    point.YValues = new double[] { value }; // Y值表示矿物含量（对应X轴刻度显示百分比）

                    // 不显示数据标签
                    point.Label = "";
                    point.LabelForeColor = Color.Transparent;

                    // 添加悬停提示
                    point.ToolTip = $"{depth:F0}m: {pureMineral} {value:F1}%";

                    // 添加数据点
                    LogMessage($"添加数据点: 深度={depth:F1}m, 含量={value:F2}% (坐标 X={depthIndex}, Y={value})");
                    series.Points.Add(point);
                }

                _chart.Series.Add(series);
                LogMessage($"完成矿物系列 '{pureMineral}' 的创建");
            }

            LogMessage($"X轴范围: {chartArea.AxisX.Minimum} 到 {chartArea.AxisX.Maximum}");

            // 使用Y轴作为深度轴，不需要添加额外的标签系列

            // 添加深度标签到Y轴，但不在每个数据点都显示标签
            LogMessage("设置Y轴深度标签...");

            // 设置Y轴深度刻度（参考图2的样式）
            chartArea.AxisY.CustomLabels.Clear();

            // 计算合适的深度刻度间隔
            int maxLabels = Math.Min(depths.Count, 20); // 最多显示20个刻度
            int interval = depths.Count <= maxLabels ? 1 : depths.Count / maxLabels;

            LogMessage($"设置Y轴深度刻度，总深度点: {depths.Count}, 间隔: {interval}");

            for (int i = 0; i < depths.Count; i += interval)
            {
                CustomLabel label = new CustomLabel();
                label.FromPosition = i - 0.5;
                label.ToPosition = i + 0.5;
                label.Text = $"{depths[i]:F0}"; // 使用整数形式，与图2一致
                chartArea.AxisY.CustomLabels.Add(label);
            }

            // 确保Y轴刻度清晰可见
            chartArea.AxisY.LabelStyle.Angle = 0;
            chartArea.AxisY.MajorTickMark.Enabled = true;

            LogMessage("===== 图表更新完成 =====");
        }

        /// <summary>
        /// 更新图例面板
        /// </summary>
        private void UpdateLegendPanel(List<string> minerals)
        {
            LogMessage($"更新图例面板，矿物数量: {minerals.Count}");

            _legendPanel.Controls.Clear();
            _legendPanel.ColumnStyles.Clear();
            _legendPanel.RowStyles.Clear();

            // 设置列数和行数
            int columns = Math.Min(minerals.Count, 5);
            _legendPanel.ColumnCount = columns;
            _legendPanel.RowCount = (int)Math.Ceiling((double)minerals.Count / columns);

            LogMessage($"图例布局: {columns}列 × {_legendPanel.RowCount}行");

            // 设置列宽
            for (int i = 0; i < columns; i++)
            {
                _legendPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f / columns));
            }

            // 设置行高
            for (int i = 0; i < _legendPanel.RowCount; i++)
            {
                _legendPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            }

            // 添加图例项
            LogMessage("添加图例项...");
            for (int i = 0; i < minerals.Count; i++)
            {
                string mineral = minerals[i];
                LogMessage($"处理矿物图例: {mineral}");

                // 提取纯矿物列名（移除可能存在的数值部分）
                string pureMineral = mineral.Contains(":")
                    ? mineral.Split(':')[0].Trim()
                    : mineral;

                // 计算平均值
                double avgValue = 0;
                try
                {
                    var values = new List<double>();

                    foreach (DataRow dataRow in _resultData.Rows)
                    {
                        if (dataRow.Table.Columns.Contains(pureMineral) && dataRow[pureMineral] != DBNull.Value)
                        {
                            try
                            {
                                double val = Convert.ToDouble(dataRow[pureMineral]);
                                values.Add(val);
                            }
                            catch (FormatException ex)
                            {
                                LogMessage($"警告: 计算平均值时跳过非数字值 '{dataRow[pureMineral]}': {ex.Message}");
                            }
                        }
                    }

                    if (values.Count > 0)
                    {
                        avgValue = values.Average();
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"计算矿物 '{pureMineral}' 平均值时出错: {ex.Message}");
                }

                LogMessage($"矿物 '{pureMineral}' 平均含量: {avgValue:F2}%");

                Panel legendItem = new Panel
                {
                    Height = 30,
                    Margin = new Padding(5),
                    AutoSize = true
                };

                Color mineralColor = Color.Black;
                if (_mineralColors.ContainsKey(pureMineral))
                {
                    mineralColor = _mineralColors[pureMineral];
                }
                else
                {
                    LogMessage($"警告: 矿物 '{pureMineral}' 未定义颜色，使用随机颜色");
                    mineralColor = Color.FromArgb(
                        new Random(mineral.GetHashCode()).Next(256),
                        new Random(mineral.GetHashCode()).Next(256),
                        new Random(mineral.GetHashCode()).Next(256));
                }

                Panel colorBox = new Panel
                {
                    BackColor = mineralColor,
                    Size = new Size(25, 25),
                    Location = new Point(0, 2),
                    Tag = pureMineral,
                    Cursor = Cursors.Hand
                };
                colorBox.Click += ColorBox_Click;

                Label label = new Label
                {
                    Text = $"{pureMineral}: {avgValue:F0}%", // 使用纯净名称
                    AutoSize = true,
                    Location = new Point(30, 5),
                    Font = new Font("Microsoft YaHei", 9)
                };

                legendItem.Controls.Add(colorBox);
                legendItem.Controls.Add(label);

                int row = i / columns;
                int col = i % columns;
                _legendPanel.Controls.Add(legendItem, col, row);

                LogMessage($"添加图例项: {mineral} 到位置 ({row}, {col})");
            }

            // 调整图例面板高度
            int preferredHeight = Math.Min(_legendPanel.PreferredSize.Height + 20, 150);
            _legendPanel.Height = preferredHeight;

            LogMessage($"图例面板高度设置为: {preferredHeight}px");
        }
        #endregion
    }
}