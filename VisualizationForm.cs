using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using BritSystem.Models;

namespace BritSystem
{
    public partial class VisualizationForm : Form
    {
        private DataTable _resultData;
        private List<BrittlenessDataPoint> _dataPoints;
        private Chart _chart;

        public VisualizationForm(DataTable resultData, List<BrittlenessDataPoint> dataPoints)
        {
            InitializeComponent();

            _resultData = resultData;
            _dataPoints = dataPoints;

            // 初始化图表
            InitializeChart();

            // 加载数据
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // VisualizationForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(45)))), ((int)(((byte)(45)))), ((int)(((byte)(45)))));
            this.ClientSize = new System.Drawing.Size(1200, 800);
            this.ForeColor = System.Drawing.Color.White;
            this.Name = "VisualizationForm";
            this.Text = "脆性指数可视化";
            this.Load += new System.EventHandler(this.VisualizationForm_Load);
            this.ResumeLayout(false);
        }

        private void VisualizationForm_Load(object sender, EventArgs e)
        {
            // 窗体加载时的初始化
        }

        private void InitializeChart()
        {
            // 创建图表控件
            _chart = new Chart();
            _chart.Dock = DockStyle.Fill;
            _chart.BackColor = Color.FromArgb(50, 50, 50);
            _chart.ForeColor = Color.White;
            _chart.BorderlineColor = Color.Gray;
            _chart.BorderlineDashStyle = ChartDashStyle.Solid;
            _chart.BorderlineWidth = 1;
            _chart.Palette = ChartColorPalette.BrightPastel;

            // 添加图表区域
            ChartArea chartArea = new ChartArea("MainChartArea");
            chartArea.BackColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineColor = Color.DarkGray;
            chartArea.AxisY.MajorGrid.LineColor = Color.DarkGray;
            chartArea.AxisX.MajorGrid.LineDashStyle = ChartDashStyle.Dash;
            chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;
            chartArea.AxisX.Title = "深度 (m)";
            chartArea.AxisY.Title = "脆性指数 (%)";
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisX.IsReversed = true; // 深度轴反向显示
            _chart.ChartAreas.Add(chartArea);

            // 添加图例
            Legend legend = new Legend("MainLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            legend.BorderColor = Color.Gray;
            _chart.Legends.Add(legend);

            // 添加到窗体
            this.Controls.Add(_chart);
        }

        private void LoadData()
        {
            if (_dataPoints == null || _dataPoints.Count == 0)
            {
                MessageBox.Show("没有数据可供可视化", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 创建脆性指数数据系列
            Series brittlenessSeries = new Series("脆性指数");
            brittlenessSeries.ChartType = SeriesChartType.Line;
            brittlenessSeries.Color = Color.Cyan;
            brittlenessSeries.BorderWidth = 2;
            brittlenessSeries.MarkerStyle = MarkerStyle.Circle;
            brittlenessSeries.MarkerSize = 8;
            brittlenessSeries.MarkerColor = Color.White;
            brittlenessSeries.XValueType = ChartValueType.Double;
            brittlenessSeries.YValueType = ChartValueType.Double;

            // 添加数据点
            foreach (BrittlenessDataPoint point in _dataPoints)
            {
                // 使用顶深作为X值
                double depth = (point.TopDepth + point.BottomDepth) / 2; // 使用中点深度
                double brittleIndex = point.BrittleIndex;

                // 确保脆性指数值已乘以100
                if (brittleIndex < 1.0)
                {
                    brittleIndex *= 100;
                }

                // 添加数据点
                DataPoint dataPoint = new DataPoint(depth, brittleIndex);
                dataPoint.ToolTip = $"深度: {depth:F2}m\n脆性指数: {brittleIndex:F2}%";
                dataPoint.Tag = point.GeoID; // 存储GeoID用于后续查找

                brittlenessSeries.Points.Add(dataPoint);
            }

            // 添加系列到图表
            _chart.Series.Add(brittlenessSeries);

            // 设置图表标题
            Title title = new Title("脆性指数随深度变化曲线");
            title.Font = new Font("Microsoft YaHei UI", 14, FontStyle.Bold);
            title.ForeColor = Color.White;
            _chart.Titles.Add(title);

            // 添加点击事件
            _chart.MouseClick += Chart_MouseClick;
            _chart.MouseMove += Chart_MouseMove;
        }

        private void Chart_MouseClick(object sender, MouseEventArgs e)
        {
            // 获取点击位置的数据点
            HitTestResult result = _chart.HitTest(e.X, e.Y);
            if (result.ChartElementType == ChartElementType.DataPoint)
            {
                // 获取数据点
                DataPoint dataPoint = _chart.Series[0].Points[result.PointIndex];
                string geoID = dataPoint.Tag as string;

                // 查找对应的数据点
                BrittlenessDataPoint point = _dataPoints.FirstOrDefault(p => p.GeoID == geoID);
                if (point != null)
                {
                    // 计算显示用的脆性指数值（确保已乘以100）
                    double displayBrittleIndex = point.BrittleIndex;
                    if (displayBrittleIndex < 1.0)
                    {
                        displayBrittleIndex *= 100;
                    }

                    // 显示详细信息
                    MessageBox.Show($"GeoID: {point.GeoID}\n" +
                                    $"顶深: {point.TopDepth:F2}m\n" +
                                    $"底深: {point.BottomDepth:F2}m\n" +
                                    $"脆性指数: {displayBrittleIndex:F2}%\n" +
                                    $"脆性矿物: {string.Join(", ", point.BrittleMinerals)}\n" +
                                    $"塑性矿物: {string.Join(", ", point.DuctileMinerals)}",
                                    "数据点详情", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void Chart_MouseMove(object sender, MouseEventArgs e)
        {
            // 获取鼠标位置的数据点
            HitTestResult result = _chart.HitTest(e.X, e.Y);
            if (result.ChartElementType == ChartElementType.DataPoint)
            {
                // 获取数据点
                DataPoint dataPoint = _chart.Series[0].Points[result.PointIndex];

                // 高亮显示
                dataPoint.MarkerSize = 12;
                dataPoint.MarkerColor = Color.Purple;

                // 恢复其他点的样式
                foreach (DataPoint point in _chart.Series[0].Points)
                {
                    if (point != dataPoint)
                    {
                        point.MarkerSize = 8;
                        point.MarkerColor = Color.White;
                    }
                }
            }
            else
            {
                // 恢复所有点的样式
                foreach (DataPoint point in _chart.Series[0].Points)
                {
                    point.MarkerSize = 8;
                    point.MarkerColor = Color.White;
                }
            }
        }
    }
}
