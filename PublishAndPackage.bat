@echo off
setlocal enabledelayedexpansion

echo 矿物脆性指数分析系统 - 发布和打包工具
echo =======================================
echo.

REM 设置颜色
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

REM 创建输出目录
if not exist "Output" (
    echo %BLUE%创建输出目录...%RESET%
    mkdir "Output"
)

REM 询问用户选择发布模式
echo %BLUE%请选择发布模式:%RESET%
echo 1. 框架依赖 (较小的安装包，但需要目标计算机安装 .NET 8.0)
echo 2. 自包含 (较大的安装包，但不需要目标计算机安装 .NET 8.0)
echo.

set /p PUBLISH_MODE=请输入选项 (1/2，默认=2):

if not defined PUBLISH_MODE set "PUBLISH_MODE=2"

if "%PUBLISH_MODE%"=="1" (
    set "PUBLISH_ARGS=-c Release --self-contained false"
    echo %YELLOW%已选择: 框架依赖模式%RESET%
) else (
    set "PUBLISH_ARGS=-c Release --self-contained true -p:PublishSingleFile=false -p:PublishTrimmed=false"
    echo %GREEN%已选择: 自包含模式%RESET%
)

REM 询问用户选择目标平台
echo.
echo %BLUE%请选择目标平台:%RESET%
echo 1. x64 (64位系统)
echo 2. x86 (32位系统)
echo 3. 两者都包含
echo.

set /p TARGET_PLATFORM=请输入选项 (1/2/3，默认=1):

if not defined TARGET_PLATFORM set "TARGET_PLATFORM=1"

if "%TARGET_PLATFORM%"=="1" (
    set "RUNTIME_ID=win-x64"
    echo %GREEN%已选择: x64 平台%RESET%
) else if "%TARGET_PLATFORM%"=="2" (
    set "RUNTIME_ID=win-x86"
    echo %GREEN%已选择: x86 平台%RESET%
) else (
    set "RUNTIME_ID=win-x64 win-x86"
    echo %GREEN%已选择: x64 和 x86 平台%RESET%
)

REM 清理旧的发布文件
echo.
echo %BLUE%清理旧的发布文件...%RESET%
if exist "bin\Release\net8.0-windows\publish" (
    rmdir /s /q "bin\Release\net8.0-windows\publish"
)

REM 发布应用程序
echo.
echo %BLUE%正在发布应用程序...%RESET%

for %%r in (%RUNTIME_ID%) do (
    echo %BLUE%为 %%r 平台发布...%RESET%
    dotnet publish %PUBLISH_ARGS% -r %%r

    if !ERRORLEVEL! NEQ 0 (
        echo %RED%发布失败。请检查错误信息。%RESET%
        pause
        exit /b 1
    )
)

echo %GREEN%发布成功。%RESET%

REM 查找 Inno Setup 编译器
echo.
echo %BLUE%查找 Inno Setup 编译器...%RESET%
set "ISCC_PATH="

REM 尝试查找 Inno Setup 6
if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
    echo %GREEN%找到 Inno Setup 6 (32位)%RESET%
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
    echo %GREEN%找到 Inno Setup 6 (64位)%RESET%
)

REM 尝试查找 Inno Setup 5
if "%ISCC_PATH%"=="" (
    if exist "%ProgramFiles(x86)%\Inno Setup 5\ISCC.exe" (
        set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 5\ISCC.exe"
        echo %YELLOW%找到 Inno Setup 5 (32位) - 建议升级到 Inno Setup 6%RESET%
    ) else if exist "%ProgramFiles%\Inno Setup 5\ISCC.exe" (
        set "ISCC_PATH=%ProgramFiles%\Inno Setup 5\ISCC.exe"
        echo %YELLOW%找到 Inno Setup 5 (64位) - 建议升级到 Inno Setup 6%RESET%
    )
)

REM 如果找不到 Inno Setup，提示用户手动输入路径
if "%ISCC_PATH%"=="" (
    echo %RED%未找到 Inno Setup。%RESET%
    echo 请输入 ISCC.exe 的完整路径:
    set /p ISCC_PATH=

    if not exist "!ISCC_PATH!" (
        echo %RED%错误: 指定的路径不存在。%RESET%
        echo 请安装 Inno Setup 6 后再试。
        echo 下载地址: https://jrsoftware.org/isdl.php
        pause
        exit /b 1
    )
)

REM 选择 ISS 文件
set "ISS_FILE=BritSystemSetup.iss"
if exist "BritSystemSetup_Simple.iss" (
    set "ISS_FILE=BritSystemSetup_Simple.iss"
    echo %GREEN%使用简化版 ISS 文件: !ISS_FILE!%RESET%
) else if exist "BritSystemSetup_Fixed.iss" (
    set "ISS_FILE=BritSystemSetup_Fixed.iss"
    echo %GREEN%使用修复版 ISS 文件: !ISS_FILE!%RESET%
) else (
    echo %YELLOW%使用标准 ISS 文件: !ISS_FILE!%RESET%
)

REM 修改 ISS 文件中的源路径
echo.
echo %BLUE%修改 ISS 文件中的源路径...%RESET%
set "TEMP_ISS=Output\temp_setup.iss"
set "SOURCE_PATH=bin\Release\net8.0-windows\publish"

if "%TARGET_PLATFORM%"=="1" (
    set "SOURCE_PATH=bin\Release\net8.0-windows\win-x64\publish"
) else if "%TARGET_PLATFORM%"=="2" (
    set "SOURCE_PATH=bin\Release\net8.0-windows\win-x86\publish"
)

REM 创建临时 ISS 文件
type "!ISS_FILE!" > "!TEMP_ISS!"

REM 编译安装程序
echo.
echo %BLUE%正在编译安装程序...%RESET%
echo 输出目录: %CD%\Output
echo 使用 ISS 文件: !TEMP_ISS!
echo 源路径: !SOURCE_PATH!

REM 运行 Inno Setup 编译器
echo.
echo %BLUE%运行 Inno Setup 编译器...%RESET%
"%ISCC_PATH%" "/dSourcePath=!SOURCE_PATH!" "!TEMP_ISS!" /O"Output"

REM 检查结果
if %ERRORLEVEL% EQU 0 (
    echo %GREEN%构建成功!%RESET%
    if exist "Output\BritSystemSetup.exe" (
        echo %GREEN%安装程序位置: %CD%\Output\BritSystemSetup.exe%RESET%

        REM 打开输出目录
        start "" "Output"
    ) else (
        echo %YELLOW%警告: 安装程序未在预期位置找到。%RESET%
        echo 预期路径: %CD%\Output\BritSystemSetup.exe
    )
) else (
    echo %RED%构建失败。请检查错误信息。%RESET%
    echo.
    echo %YELLOW%常见问题:%RESET%
    echo 1. 源文件未找到 - 确保发布路径正确
    echo 2. 权限问题 - 确保您有写入输出目录的权限
    echo 3. 路径问题 - 避免路径中包含特殊字符或空格
    echo 4. 中文路径问题 - 避免路径中包含中文字符
    echo 5. ISS 文件语法错误 - 检查 !ISS_FILE! 文件
)

REM 清理临时文件
if exist "!TEMP_ISS!" del "!TEMP_ISS!"

echo.
echo %BLUE%发布和打包过程完成。%RESET%
pause
