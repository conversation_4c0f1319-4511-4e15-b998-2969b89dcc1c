using System;
using System.Windows.Forms;
using System.Diagnostics;
using BritSystem; // 确保能够引用FormAdapter类

namespace BritSystem
{
    /// <summary>
    /// 窗体适配器类，用于将新的原生窗体集成到现有项目中
    /// </summary>
    public static class FormAdapter
    {
        /// <summary>
        /// 显示登录窗体
        /// </summary>
        /// <param name="success">登录是否成功</param>
        /// <param name="username">用户名</param>
        /// <returns>登录结果</returns>
        public static DialogResult ShowLoginForm(out bool success, out string username)
        {
            success = false;
            username = string.Empty;

            try
            {
                // 创建登录窗体
                var loginForm = new LoginForm();
                var result = loginForm.ShowDialog();

                if (result == DialogResult.OK)
                {
                    success = true;
                    username = loginForm.Username;
                }

                return result;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示登录窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 显示仪表盘窗体
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>窗体结果</returns>
        public static DialogResult ShowDashboardForm(string username)
        {
            try
            {
                // 创建仪表盘窗体
                var dashboardForm = new DashboardForm(username);
                return dashboardForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示仪表盘窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 显示矿物组分法窗体
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>窗体结果</returns>
        public static DialogResult ShowMineralogicalForm(string username)
        {
            try
            {
                // 创建矿物组分法窗体
                var mineralogicalForm = new MineralogicalForm(username);
                return mineralogicalForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示矿物组分法窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 显示脆性指数分析窗体
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>窗体结果</returns>
        public static DialogResult ShowBritIndexAnalysisForm(string username)
        {
            try
            {
                // 创建脆性指数分析窗体
                var britIndexAnalysisForm = new BritIndexAnalysisForm(username);
                return britIndexAnalysisForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示脆性指数分析窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 显示矿物脆性指数分析窗体（专业版）
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>窗体结果</returns>
        public static DialogResult ShowMineralogicalAnalysisForm(string username)
        {
            try
            {
                // 创建矿物脆性指数分析窗体
                var mineralogicalAnalysisForm = new MineralogicalAnalysisForm();
                return mineralogicalAnalysisForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示矿物脆性指数分析窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }

        /// <summary>
        /// 显示脆性指数计算器窗体
        /// </summary>
        /// <param name="sourceData">源数据表</param>
        /// <returns>窗体结果</returns>
        public static DialogResult ShowAlgorithmFormulaCalForm(System.Data.DataTable sourceData = null)
        {
            try
            {
                // 创建脆性指数计算器窗体
                var algorithmFormulaCal = sourceData != null ?
                    new AlgorithmFormulaCal(sourceData) :
                    new AlgorithmFormulaCal();

                // 显示窗体并获取结果
                DialogResult result = algorithmFormulaCal.ShowDialog();

                // 如果用户点击了可视化按钮，则返回结果为OK
                if (result == DialogResult.OK)
                {
                    // 获取计算结果数据表
                    System.Data.DataTable resultData = algorithmFormulaCal.GetResultData();

                    // 将结果数据表传递给MineralogicalForm
                    if (resultData != null && resultData.Rows.Count > 0)
                    {
                        MineralogicalForm.LoadBrittlenessData(resultData);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示脆性指数计算器窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return DialogResult.Cancel;
            }
        }
    }

    public class BritSystemApp
    {
        // 不再直接引用窗体类，而是通过适配器使用
        private string username;

        // 单例模式
        private static BritSystemApp instance;
        public static BritSystemApp Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new BritSystemApp();
                }
                return instance;
            }
        }

        private BritSystemApp()
        {
            // 私有构造函数，防止外部实例化
        }

        public void Start()
        {
            try
            {
                Debug.WriteLine("[BritSystemApp.Start] Starting application");
                ShowLoginForm();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[BritSystemApp.Start] Error: {ex.Message}\nStackTrace: {ex.StackTrace}");
                MessageBox.Show($"应用程序启动时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowLoginForm()
        {
            try
            {
                Debug.WriteLine("[BritSystemApp.ShowLoginForm] Creating login form");
                // 使用适配器显示登录窗体
                bool loginSuccess;
                string loginUsername;
                var result = FormAdapter.ShowLoginForm(out loginSuccess, out loginUsername);
                Debug.WriteLine("[BritSystemApp.ShowLoginForm] Login form closed");

                if (loginSuccess)
                {
                    // 登录成功
                    username = loginUsername;
                    Debug.WriteLine($"[BritSystemApp.ShowLoginForm] Login successful for user: {username}");

                    // 直接显示仪表盘
                    ShowDashboardForm();
                }
                else
                {
                    Debug.WriteLine("[BritSystemApp.ShowLoginForm] Login cancelled or failed");
                    Application.Exit();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[BritSystemApp.ShowLoginForm] Error: {ex.Message}\nStackTrace: {ex.StackTrace}");
                MessageBox.Show($"显示登录窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowDashboardForm()
        {
            try
            {
                Debug.WriteLine("[BritSystemApp.ShowDashboardForm] Creating dashboard form");
                // 使用适配器显示仪表盘窗体
                DialogResult result = FormAdapter.ShowDashboardForm(username);
                Debug.WriteLine("[BritSystemApp.ShowDashboardForm] Dashboard form closed");

                if (result == DialogResult.OK)
                {
                    // 用户退出登录
                    Debug.WriteLine("[BritSystemApp.ShowDashboardForm] User logged out");
                    ShowLoginForm();
                }
                else
                {
                    // 用户关闭窗体
                    Debug.WriteLine("[BritSystemApp.ShowDashboardForm] User closed the form");
                    Application.Exit();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[BritSystemApp.ShowDashboardForm] Error: {ex.Message}\nStackTrace: {ex.StackTrace}");
                MessageBox.Show($"显示仪表盘窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowMineralogicalForm()
        {
            try
            {
                Debug.WriteLine("[BritSystemApp.ShowMineralogicalForm] Creating mineralogical form");
                // 使用适配器显示矿物组分法窗体
                DialogResult result = FormAdapter.ShowMineralogicalForm(username);
                Debug.WriteLine("[BritSystemApp.ShowMineralogicalForm] Mineralogical form closed");

                if (result == DialogResult.OK)
                {
                    // 用户返回仪表盘
                    Debug.WriteLine("[BritSystemApp.ShowMineralogicalForm] User returned to dashboard");
                    ShowDashboardForm();
                }
                else
                {
                    // 用户退出登录
                    Debug.WriteLine("[BritSystemApp.ShowMineralogicalForm] User logged out");
                    ShowLoginForm();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[BritSystemApp.ShowMineralogicalForm] Error: {ex.Message}\nStackTrace: {ex.StackTrace}");
                MessageBox.Show($"显示矿物组分法窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
