; -- ������Ϣ���� --
#define MyAppName "���Կ���ָ��ϵͳ"
#define MyAppVersion "1.1"
#define MyAppPublisher "������"
#define MyAppExeName "BritSystem.exe"
#define SourcePath "bin\Release\net8.0-windows"  ; �������·��

; -- ��װ������ --
[Setup]
AppId={{A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
DefaultDirName={autopf}\{#MyAppName}
DisableProgramGroupPage=yes
OutputDir=.\Output
OutputBaseFilename=BritSystemSetup
Compression=lzma
SolidCompression=yes
WizardStyle=modern
DefaultDialogFontName=Microsoft YaHei UI
; ��װ��ͼ�꣨����·����
SetupIconFile="C:\Users\<USER>\Desktop\Brittleness index system\repos\BritSystem\Images\Ikun.ico"

; -- ������֧�� --
[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

; -- ���Ľ����ı����� --
[Messages]
SetupAppTitle=��װ {#MyAppName}
SetupWindowTitle=��װ - {#MyAppName}
BeveledLabel=���İ�
WelcomeLabel1=��ӭʹ�� {#MyAppName} ��װ��
WelcomeLabel2=�⽫�����ļ�����ϰ�װ {#MyAppName} {#MyAppVersion}��%n%n�����ڼ���֮ǰ�ر���������Ӧ�ó���
ClickNext=�������һ����������������ȡ�����˳���װ����
ButtonNext=��һ��(&N)
ButtonBack=��һ��(&B)
ButtonInstall=��װ(&I)
ButtonCancel=ȡ��(&C)
ButtonFinish=���(&F)
SelectDirLabel3=��װ���򽫰�װ {#MyAppName} �������ļ��С�
SelectDirBrowseLabel=�������һ�������������Ҫѡ�������ļ��У��������������
DiskSpaceMBLabel=������Ҫ [mb] MB �Ŀ��ô��̿ռ䡣
ReadyLabel1=��װ������׼���ÿ�ʼ��װ {#MyAppName} �����ļ������
ExitSetupMessage=��װ��δ��ɡ�����������˳������򽫲��ᱻ��װ��%n%n�������Ժ������а�װ����%n%n�˳���װ��
FinishedHeadingLabel=��� {#MyAppName} ��װ
FinishedLabel=��װ�����������ļ��������ɰ�װ {#MyAppName}��

; -- �Զ�����Ϣ --
[CustomMessages]
CreateDesktopIcon=��������ͼ��
LaunchProgram=���� {#MyAppName}
AdditionalIcons=����ͼ��:
UninstallProgram=ж�� {#MyAppName}

; -- ��װ����ѡ�� --
[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

; -- �ļ�������� --
[Files]
; �������ļ�
Source: "bin\Release\net8.0-windows\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
; ͼ���ļ�
Source: "C:\Users\<USER>\Desktop\Brittleness index system\repos\BritSystem\Images\Mi.ico"; DestDir: "{app}\Images"; Flags: ignoreversion

; -- ��ݷ�ʽ���� --
[Icons]
; ��ʼ�˵���ݷ�ʽ
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"

; 桌面图标
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon
; -- ��װ�����г��� --
[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

; -- ж������ --
[UninstallDelete]
Type: files; Name: "{app}\*.*"
Type: dirifempty; Name: "{app}"

; -- ע������� --
[Registry]
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "Version"; ValueData: "{#MyAppVersion}"; Flags: uninsdeletekey

; -- �Զ�����루��� WebView2 ����ʱ��--
[Code]
function InitializeSetup(): Boolean;
var
  WebView2Installed: Boolean;
  ResultCode: Integer;
begin
  Result := True;

  WebView2Installed := RegKeyExists(HKLM, 'SOFTWARE\WOW6432Node\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}') or
                       RegKeyExists(HKCU, 'SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}');

  if not WebView2Installed then
  begin
    if MsgBox('Microsoft Edge WebView2 ����ʱδ��װ��������ǳ�������������ġ�' + #13#10 + '�Ƿ��������ز���װ��', mbConfirmation, MB_YESNO) = IDYES then
    begin
      if not Exec('powershell.exe', '-Command "& { Invoke-WebRequest -Uri ''https://go.microsoft.com/fwlink/p/?LinkId=2124703'' -OutFile ''WebView2Setup.exe''; Start-Process -FilePath ''WebView2Setup.exe'' -ArgumentList ''/silent /install'' -Wait }"', '', SW_SHOW, ewWaitUntilTerminated, ResultCode) then
      begin
        MsgBox('�޷����� WebView2 ����ʱ�����ֶ���װ�������б���װ����', mbError, MB_OK);
        Result := False;
      end;
    end
    else
    begin
      if MsgBox('û�� WebView2 ����ʱ����������޷��������С�' + #13#10 + '�Ƿ���Ҫ������װ��', mbConfirmation, MB_YESNO) = IDNO then
        Result := False;
    end;
  end;
end;