namespace BritSystem
{
    partial class StaticRockMechanicsFormR
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            lblTitle = new Label();
            lblWelcome = new Label();
            btnBack = new Button();
            btnLogout = new Button();
            btnEmergencyExit = new Button();
            pnlParameters = new Panel();
            lblCalculationResult = new Label();
            btnCalculate = new Button();
            txtVs = new TextBox();
            lblVs = new Label();
            txtVp = new TextBox();
            lblVp = new Label();
            txtDensity = new TextBox();
            lblDensity = new Label();
            lblParametersTitle = new Label();
            pnlData = new Panel();
            btnExport = new Button();
            btnImport = new Button();
            dgvMechanicsData = new DataGridView();
            lblDataTitle = new Label();
            pnlChart = new Panel();
            btnSaveCurve = new Button();
            btnReset = new Button();
            btnGenerateCurve = new Button();
            lblChartTitle = new Label();
            pnlParameters.SuspendLayout();
            pnlData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMechanicsData).BeginInit();
            pnlChart.SuspendLayout();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.AutoSize = true;
            lblTitle.Font = new Font("微软雅黑", 16F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblTitle.ForeColor = Color.White;
            lblTitle.Location = new Point(20, 20);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(498, 42);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "静态岩石力学参数法脆性指数计算";
            // 
            // lblWelcome
            // 
            lblWelcome.AutoSize = true;
            lblWelcome.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblWelcome.ForeColor = Color.White;
            lblWelcome.Location = new Point(22, 60);
            lblWelcome.Name = "lblWelcome";
            lblWelcome.Size = new Size(432, 27);
            lblWelcome.TabIndex = 1;
            lblWelcome.Text = "欢迎使用静态岩石力学参数法脆性指数计算系统";
            // 
            // btnBack
            // 
            btnBack.BackColor = Color.FromArgb(50, 50, 50);
            btnBack.FlatStyle = FlatStyle.Flat;
            btnBack.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnBack.ForeColor = Color.White;
            btnBack.Location = new Point(700, 20);
            btnBack.Name = "btnBack";
            btnBack.Size = new Size(100, 30);
            btnBack.TabIndex = 2;
            btnBack.Text = "返回主界面";
            btnBack.UseVisualStyleBackColor = false;
            btnBack.Click += btnBack_Click;
            // 
            // btnLogout
            // 
            btnLogout.BackColor = Color.FromArgb(50, 50, 50);
            btnLogout.FlatStyle = FlatStyle.Flat;
            btnLogout.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnLogout.ForeColor = Color.White;
            btnLogout.Location = new Point(810, 20);
            btnLogout.Name = "btnLogout";
            btnLogout.Size = new Size(100, 30);
            btnLogout.TabIndex = 3;
            btnLogout.Text = "退出登录";
            btnLogout.UseVisualStyleBackColor = false;
            btnLogout.Click += btnLogout_Click;
            // 
            // btnEmergencyExit
            // 
            btnEmergencyExit.BackColor = Color.FromArgb(192, 0, 0);
            btnEmergencyExit.FlatStyle = FlatStyle.Flat;
            btnEmergencyExit.Font = new Font("微软雅黑", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            btnEmergencyExit.ForeColor = Color.White;
            btnEmergencyExit.Location = new Point(920, 20);
            btnEmergencyExit.Name = "btnEmergencyExit";
            btnEmergencyExit.Size = new Size(100, 30);
            btnEmergencyExit.TabIndex = 4;
            btnEmergencyExit.Text = "紧急退出";
            btnEmergencyExit.UseVisualStyleBackColor = false;
            btnEmergencyExit.Click += btnEmergencyExit_Click;
            // 
            // pnlParameters
            // 
            pnlParameters.BackColor = Color.FromArgb(45, 45, 45);
            pnlParameters.BorderStyle = BorderStyle.FixedSingle;
            pnlParameters.Controls.Add(lblCalculationResult);
            pnlParameters.Controls.Add(btnCalculate);
            pnlParameters.Controls.Add(txtVs);
            pnlParameters.Controls.Add(lblVs);
            pnlParameters.Controls.Add(txtVp);
            pnlParameters.Controls.Add(lblVp);
            pnlParameters.Controls.Add(txtDensity);
            pnlParameters.Controls.Add(lblDensity);
            pnlParameters.Controls.Add(lblParametersTitle);
            pnlParameters.Location = new Point(20, 100);
            pnlParameters.Name = "pnlParameters";
            pnlParameters.Size = new Size(1047, 120);
            pnlParameters.TabIndex = 5;
            // 
            // lblCalculationResult
            // 
            lblCalculationResult.AutoSize = true;
            lblCalculationResult.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblCalculationResult.ForeColor = Color.LightGreen;
            lblCalculationResult.Location = new Point(20, 85);
            lblCalculationResult.Name = "lblCalculationResult";
            lblCalculationResult.Size = new Size(212, 27);
            lblCalculationResult.TabIndex = 8;
            lblCalculationResult.Text = "计算结果将在这里显示";
            // 
            // btnCalculate
            // 
            btnCalculate.BackColor = Color.FromArgb(50, 50, 50);
            btnCalculate.FlatStyle = FlatStyle.Flat;
            btnCalculate.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnCalculate.ForeColor = Color.White;
            btnCalculate.Location = new Point(860, 50);
            btnCalculate.Name = "btnCalculate";
            btnCalculate.Size = new Size(120, 30);
            btnCalculate.TabIndex = 7;
            btnCalculate.Text = "计算脆性指数";
            btnCalculate.UseVisualStyleBackColor = false;
            btnCalculate.Click += btnCalculate_Click;
            // 
            // txtVs
            // 
            txtVs.BackColor = Color.FromArgb(60, 60, 60);
            txtVs.BorderStyle = BorderStyle.FixedSingle;
            txtVs.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtVs.ForeColor = Color.White;
            txtVs.Location = new Point(740, 50);
            txtVs.Name = "txtVs";
            txtVs.Size = new Size(100, 34);
            txtVs.TabIndex = 6;
            // 
            // lblVs
            // 
            lblVs.AutoSize = true;
            lblVs.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblVs.ForeColor = Color.White;
            lblVs.Location = new Point(580, 50);
            lblVs.Name = "lblVs";
            lblVs.Size = new Size(183, 27);
            lblVs.TabIndex = 5;
            lblVs.Text = "横波速度 Vs (m/s):";
            // 
            // txtVp
            // 
            txtVp.BackColor = Color.FromArgb(60, 60, 60);
            txtVp.BorderStyle = BorderStyle.FixedSingle;
            txtVp.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtVp.ForeColor = Color.White;
            txtVp.Location = new Point(460, 50);
            txtVp.Name = "txtVp";
            txtVp.Size = new Size(100, 34);
            txtVp.TabIndex = 4;
            // 
            // lblVp
            // 
            lblVp.AutoSize = true;
            lblVp.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblVp.ForeColor = Color.White;
            lblVp.Location = new Point(300, 50);
            lblVp.Name = "lblVp";
            lblVp.Size = new Size(187, 27);
            lblVp.TabIndex = 3;
            lblVp.Text = "纵波速度 Vp (m/s):";
            // 
            // txtDensity
            // 
            txtDensity.BackColor = Color.FromArgb(60, 60, 60);
            txtDensity.BorderStyle = BorderStyle.FixedSingle;
            txtDensity.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            txtDensity.ForeColor = Color.White;
            txtDensity.Location = new Point(180, 50);
            txtDensity.Name = "txtDensity";
            txtDensity.Size = new Size(100, 34);
            txtDensity.TabIndex = 2;
            // 
            // lblDensity
            // 
            lblDensity.AutoSize = true;
            lblDensity.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            lblDensity.ForeColor = Color.White;
            lblDensity.Location = new Point(20, 50);
            lblDensity.Name = "lblDensity";
            lblDensity.Size = new Size(195, 27);
            lblDensity.TabIndex = 1;
            lblDensity.Text = "岩石密度 ρ (g/cm³):";
            // 
            // lblParametersTitle
            // 
            lblParametersTitle.AutoSize = true;
            lblParametersTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblParametersTitle.ForeColor = Color.White;
            lblParametersTitle.Location = new Point(10, 10);
            lblParametersTitle.Name = "lblParametersTitle";
            lblParametersTitle.Size = new Size(206, 31);
            lblParametersTitle.TabIndex = 0;
            lblParametersTitle.Text = "岩石力学参数输入";
            // 
            // pnlData
            // 
            pnlData.BackColor = Color.FromArgb(45, 45, 45);
            pnlData.BorderStyle = BorderStyle.FixedSingle;
            pnlData.Controls.Add(btnExport);
            pnlData.Controls.Add(btnImport);
            pnlData.Controls.Add(dgvMechanicsData);
            pnlData.Controls.Add(lblDataTitle);
            pnlData.Location = new Point(20, 240);
            pnlData.Name = "pnlData";
            pnlData.Size = new Size(480, 435);
            pnlData.TabIndex = 6;
            // 
            // btnExport
            // 
            btnExport.BackColor = Color.FromArgb(50, 50, 50);
            btnExport.FlatStyle = FlatStyle.Flat;
            btnExport.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnExport.ForeColor = Color.White;
            btnExport.Location = new Point(240, 350);
            btnExport.Name = "btnExport";
            btnExport.Size = new Size(120, 30);
            btnExport.TabIndex = 3;
            btnExport.Text = "导出Excel";
            btnExport.UseVisualStyleBackColor = false;
            btnExport.Click += btnExport_Click;
            // 
            // btnImport
            // 
            btnImport.BackColor = Color.FromArgb(50, 50, 50);
            btnImport.FlatStyle = FlatStyle.Flat;
            btnImport.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnImport.ForeColor = Color.White;
            btnImport.Location = new Point(100, 350);
            btnImport.Name = "btnImport";
            btnImport.Size = new Size(120, 30);
            btnImport.TabIndex = 2;
            btnImport.Text = "导入Excel";
            btnImport.UseVisualStyleBackColor = false;
            btnImport.Click += btnImport_Click;
            // 
            // dgvMechanicsData
            // 
            dgvMechanicsData.AllowUserToAddRows = false;
            dgvMechanicsData.AllowUserToDeleteRows = false;
            dgvMechanicsData.BackgroundColor = Color.FromArgb(50, 50, 50);
            dgvMechanicsData.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvMechanicsData.GridColor = Color.FromArgb(80, 80, 80);
            dgvMechanicsData.Location = new Point(10, 50);
            dgvMechanicsData.Name = "dgvMechanicsData";
            dgvMechanicsData.ReadOnly = true;
            dgvMechanicsData.RowHeadersWidth = 51;
            dgvMechanicsData.RowTemplate.Height = 24;
            dgvMechanicsData.Size = new Size(460, 280);
            dgvMechanicsData.TabIndex = 1;
            // 
            // lblDataTitle
            // 
            lblDataTitle.AutoSize = true;
            lblDataTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblDataTitle.ForeColor = Color.White;
            lblDataTitle.Location = new Point(10, 10);
            lblDataTitle.Name = "lblDataTitle";
            lblDataTitle.Size = new Size(158, 31);
            lblDataTitle.TabIndex = 0;
            lblDataTitle.Text = "力学参数数据";
            // 
            // pnlChart
            // 
            pnlChart.BackColor = Color.FromArgb(45, 45, 45);
            pnlChart.BorderStyle = BorderStyle.FixedSingle;
            pnlChart.Controls.Add(btnSaveCurve);
            pnlChart.Controls.Add(btnReset);
            pnlChart.Controls.Add(btnGenerateCurve);
            pnlChart.Controls.Add(lblChartTitle);
            pnlChart.Location = new Point(520, 240);
            pnlChart.Name = "pnlChart";
            pnlChart.Size = new Size(500, 435);
            pnlChart.TabIndex = 7;
            // 
            // btnSaveCurve
            // 
            btnSaveCurve.BackColor = Color.FromArgb(50, 50, 50);
            btnSaveCurve.FlatStyle = FlatStyle.Flat;
            btnSaveCurve.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnSaveCurve.ForeColor = Color.White;
            btnSaveCurve.Location = new Point(350, 350);
            btnSaveCurve.Name = "btnSaveCurve";
            btnSaveCurve.Size = new Size(120, 30);
            btnSaveCurve.TabIndex = 4;
            btnSaveCurve.Text = "保存曲线";
            btnSaveCurve.UseVisualStyleBackColor = false;
            btnSaveCurve.Click += btnSaveCurve_Click;
            // 
            // btnReset
            // 
            btnReset.BackColor = Color.FromArgb(50, 50, 50);
            btnReset.FlatStyle = FlatStyle.Flat;
            btnReset.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnReset.ForeColor = Color.White;
            btnReset.Location = new Point(190, 350);
            btnReset.Name = "btnReset";
            btnReset.Size = new Size(120, 30);
            btnReset.TabIndex = 3;
            btnReset.Text = "重置曲线";
            btnReset.UseVisualStyleBackColor = false;
            btnReset.Click += btnReset_Click;
            // 
            // btnGenerateCurve
            // 
            btnGenerateCurve.BackColor = Color.FromArgb(50, 50, 50);
            btnGenerateCurve.FlatStyle = FlatStyle.Flat;
            btnGenerateCurve.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            btnGenerateCurve.ForeColor = Color.White;
            btnGenerateCurve.Location = new Point(30, 350);
            btnGenerateCurve.Name = "btnGenerateCurve";
            btnGenerateCurve.Size = new Size(120, 30);
            btnGenerateCurve.TabIndex = 2;
            btnGenerateCurve.Text = "生成曲线";
            btnGenerateCurve.UseVisualStyleBackColor = false;
            btnGenerateCurve.Click += btnGenerateCurve_Click;
            // 
            // lblChartTitle
            // 
            lblChartTitle.AutoSize = true;
            lblChartTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lblChartTitle.ForeColor = Color.White;
            lblChartTitle.Location = new Point(10, 10);
            lblChartTitle.Name = "lblChartTitle";
            lblChartTitle.Size = new Size(158, 31);
            lblChartTitle.TabIndex = 0;
            lblChartTitle.Text = "脆性指数曲线";
            // 
            // StaticRockMechanicsFormR
            // 
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(35, 35, 35);
            ClientSize = new Size(1079, 687);
            Controls.Add(pnlChart);
            Controls.Add(pnlData);
            Controls.Add(pnlParameters);
            Controls.Add(btnEmergencyExit);
            Controls.Add(btnLogout);
            Controls.Add(btnBack);
            Controls.Add(lblWelcome);
            Controls.Add(lblTitle);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            MaximizeBox = false;
            Name = "StaticRockMechanicsFormR";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "静态岩石力学参数法 - 脆性指数计算";
            FormClosing += StaticRockMechanicsFormR_FormClosing;
            Load += StaticRockMechanicsFormR_Load;
            pnlParameters.ResumeLayout(false);
            pnlParameters.PerformLayout();
            pnlData.ResumeLayout(false);
            pnlData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvMechanicsData).EndInit();
            pnlChart.ResumeLayout(false);
            pnlChart.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Label lblWelcome;
        private System.Windows.Forms.Button btnBack;
        private System.Windows.Forms.Button btnLogout;
        private System.Windows.Forms.Button btnEmergencyExit;
        private System.Windows.Forms.Panel pnlParameters;
        private System.Windows.Forms.Label lblCalculationResult;
        private System.Windows.Forms.Button btnCalculate;
        private System.Windows.Forms.TextBox txtVs;
        private System.Windows.Forms.Label lblVs;
        private System.Windows.Forms.TextBox txtVp;
        private System.Windows.Forms.Label lblVp;
        private System.Windows.Forms.TextBox txtDensity;
        private System.Windows.Forms.Label lblDensity;
        private System.Windows.Forms.Label lblParametersTitle;
        private System.Windows.Forms.Panel pnlData;
        private System.Windows.Forms.DataGridView dgvMechanicsData;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnImport;
        private System.Windows.Forms.Label lblDataTitle;
        private System.Windows.Forms.Panel pnlChart;
        private System.Windows.Forms.DataVisualization.Charting.Chart chartBrittleness;
        private System.Windows.Forms.Button btnSaveCurve;
        private System.Windows.Forms.Button btnReset;
        private System.Windows.Forms.Button btnGenerateCurve;
        private System.Windows.Forms.Label lblChartTitle;
    }
}