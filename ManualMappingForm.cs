using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace BritSystem
{
    public partial class ManualMappingForm : Form
    {
        private DataTable _sourceData;
        private List<string> _brittleColumns;
        private List<string> _ductileColumns;
        private Dictionary<string, string> _mappings;

        // 特殊目标类别常量
        private const string TARGET_BRITTLE_MINERAL = "脆性矿物";
        private const string TARGET_DUCTILE_MINERAL = "塑性矿物";
        private DataGridView dgvMappings;
        private Button btnAdd;
        private Button btnRemove;
        private Button btnSave;
        private Button btnCancel;
        private ComboBox cboSourceColumns;
        private ComboBox cboTargetColumns;
        private Label lblSource;
        private Label lblTarget;

        public ManualMappingForm(DataTable sourceData, List<string> brittleColumns, List<string> ductileColumns, Dictionary<string, string> existingMappings)
        {
            InitializeComponent();

            _sourceData = sourceData;
            _brittleColumns = brittleColumns;
            _ductileColumns = ductileColumns;
            _mappings = new Dictionary<string, string>(existingMappings);

            // 设置窗体属性
            this.Text = "手动列名映射";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;

            // 添加窗体大小改变事件
            this.Resize += ManualMappingForm_Resize;

            // 初始化控件
            InitializeControls();

            // 加载现有映射
            LoadMappings();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            //
            // ManualMappingForm
            //
            this.ClientSize = new System.Drawing.Size(584, 461);
            this.Name = "ManualMappingForm";
            this.ResumeLayout(false);
        }

        private void InitializeControls()
        {
            // 创建标签
            lblSource = new Label();
            lblSource.Text = "源列名:";
            lblSource.Location = new Point(20, 20);
            lblSource.Size = new Size(80, 20);
            this.Controls.Add(lblSource);

            lblTarget = new Label();
            lblTarget.Text = "目标列名:";
            lblTarget.Location = new Point(220, 20);
            lblTarget.Size = new Size(80, 20);
            this.Controls.Add(lblTarget);

            // 创建下拉框
            cboSourceColumns = new ComboBox();
            cboSourceColumns.Location = new Point(20, 50);
            cboSourceColumns.Size = new Size(180, 25);
            cboSourceColumns.DropDownStyle = ComboBoxStyle.DropDownList;
            this.Controls.Add(cboSourceColumns);

            cboTargetColumns = new ComboBox();
            cboTargetColumns.Location = new Point(220, 50);
            cboTargetColumns.Size = new Size(180, 25);
            cboTargetColumns.DropDownStyle = ComboBoxStyle.DropDownList;
            this.Controls.Add(cboTargetColumns);

            // 加载源列名
            foreach (DataColumn col in _sourceData.Columns)
            {
                cboSourceColumns.Items.Add(col.ColumnName);
            }

            // 加载目标列名
            cboTargetColumns.Items.Add("顶深/m");
            cboTargetColumns.Items.Add("底深/m");
            cboTargetColumns.Items.Add(TARGET_BRITTLE_MINERAL);
            cboTargetColumns.Items.Add(TARGET_DUCTILE_MINERAL);

            // 创建按钮
            btnAdd = new Button();
            btnAdd.Text = "添加映射";
            btnAdd.Size = new Size(120, 30);
            btnAdd.BackColor = Color.SteelBlue;
            btnAdd.ForeColor = Color.White;
            btnAdd.FlatStyle = FlatStyle.Flat;
            btnAdd.Font = new Font("微软雅黑", 9F, FontStyle.Bold);
            btnAdd.Click += BtnAdd_Click;
            this.Controls.Add(btnAdd);

            btnRemove = new Button();
            btnRemove.Text = "移除映射";
            btnRemove.Size = new Size(120, 30);
            btnRemove.BackColor = Color.IndianRed;
            btnRemove.ForeColor = Color.White;
            btnRemove.FlatStyle = FlatStyle.Flat;
            btnRemove.Font = new Font("微软雅黑", 9F, FontStyle.Bold);
            btnRemove.Click += BtnRemove_Click;
            this.Controls.Add(btnRemove);

            // 创建数据网格视图
            dgvMappings = new DataGridView();
            dgvMappings.Location = new Point(20, 90);
            dgvMappings.Size = new Size(380, 300);
            dgvMappings.AllowUserToAddRows = false;
            dgvMappings.AllowUserToDeleteRows = false;
            dgvMappings.ReadOnly = true;
            dgvMappings.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvMappings.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvMappings.BackgroundColor = Color.FromArgb(60, 60, 60);
            dgvMappings.BorderStyle = BorderStyle.Fixed3D;
            dgvMappings.GridColor = Color.FromArgb(80, 80, 80);
            dgvMappings.RowHeadersVisible = false;
            dgvMappings.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(50, 50, 50);
            dgvMappings.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvMappings.ColumnHeadersDefaultCellStyle.Font = new Font("微软雅黑", 9F, FontStyle.Bold);
            dgvMappings.DefaultCellStyle.BackColor = Color.FromArgb(60, 60, 60);
            dgvMappings.DefaultCellStyle.ForeColor = Color.White;
            dgvMappings.DefaultCellStyle.SelectionBackColor = Color.SteelBlue;
            dgvMappings.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvMappings.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(55, 55, 55);
            dgvMappings.CellFormatting += DgvMappings_CellFormatting;
            this.Controls.Add(dgvMappings);

            // 添加列
            dgvMappings.Columns.Add("SourceColumn", "源列名");
            dgvMappings.Columns.Add("TargetColumn", "目标列名");

            // 创建保存和取消按钮
            btnSave = new Button();
            btnSave.Text = "保存";
            btnSave.Size = new Size(120, 40);
            btnSave.BackColor = Color.SeaGreen;
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnSave.Click += BtnSave_Click;
            this.Controls.Add(btnSave);

            btnCancel = new Button();
            btnCancel.Text = "取消";
            btnCancel.Size = new Size(120, 40);
            btnCancel.BackColor = Color.DarkGray;
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnCancel.Click += BtnCancel_Click;
            this.Controls.Add(btnCancel);

            // 调整控件位置和大小
            AdjustControlPositions();
        }

        private void LoadMappings()
        {
            // 清空数据网格
            dgvMappings.Rows.Clear();

            // 加载现有映射
            foreach (var mapping in _mappings)
            {
                dgvMappings.Rows.Add(mapping.Key, mapping.Value);
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否选择了源列和目标列
                if (cboSourceColumns.SelectedItem == null || cboTargetColumns.SelectedItem == null)
                {
                    MessageBox.Show("请选择源列和目标列！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                string sourceColumn = cboSourceColumns.SelectedItem.ToString();
                string targetColumn = cboTargetColumns.SelectedItem.ToString();

                // 检查是否已存在相同的映射
                bool mappingExists = false;
                foreach (DataGridViewRow row in dgvMappings.Rows)
                {
                    if (row.Cells[0].Value.ToString() == sourceColumn)
                    {
                        // 更新现有映射
                        row.Cells[1].Value = targetColumn;
                        _mappings[sourceColumn] = targetColumn;
                        mappingExists = true;
                        break;
                    }
                }

                if (!mappingExists)
                {
                    // 添加新映射
                    dgvMappings.Rows.Add(sourceColumn, targetColumn);
                    _mappings[sourceColumn] = targetColumn;
                }

                // 如果是脆性矿物或塑性矿物，更新相应的列表
                if (targetColumn == TARGET_BRITTLE_MINERAL && !_brittleColumns.Contains(sourceColumn))
                {
                    _brittleColumns.Add(sourceColumn);
                }
                else if (targetColumn == TARGET_DUCTILE_MINERAL && !_ductileColumns.Contains(sourceColumn))
                {
                    _ductileColumns.Add(sourceColumn);
                }

                // 如果之前是脆性矿物，但现在不是，则从脆性矿物列表中移除
                if (targetColumn != TARGET_BRITTLE_MINERAL && _brittleColumns.Contains(sourceColumn))
                {
                    _brittleColumns.Remove(sourceColumn);
                }

                // 如果之前是塑性矿物，但现在不是，则从塑性矿物列表中移除
                if (targetColumn != TARGET_DUCTILE_MINERAL && _ductileColumns.Contains(sourceColumn))
                {
                    _ductileColumns.Remove(sourceColumn);
                }

                MessageBox.Show($"已添加映射: {sourceColumn} -> {targetColumn}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加映射时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRemove_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否选择了行
                if (dgvMappings.SelectedRows.Count == 0)
                {
                    MessageBox.Show("请选择要移除的映射！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取选中的行
                DataGridViewRow row = dgvMappings.SelectedRows[0];
                string sourceColumn = row.Cells[0].Value.ToString();
                string targetColumn = row.Cells[1].Value.ToString();

                // 移除映射
                dgvMappings.Rows.Remove(row);
                _mappings.Remove(sourceColumn);

                // 如果是脆性矿物，从脆性矿物列表中移除
                if (targetColumn == TARGET_BRITTLE_MINERAL && _brittleColumns.Contains(sourceColumn))
                {
                    _brittleColumns.Remove(sourceColumn);
                }

                // 如果是塑性矿物，从塑性矿物列表中移除
                if (targetColumn == TARGET_DUCTILE_MINERAL && _ductileColumns.Contains(sourceColumn))
                {
                    _ductileColumns.Remove(sourceColumn);
                }

                MessageBox.Show($"已移除映射: {sourceColumn} -> {targetColumn}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"移除映射时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            // 检查是否有映射
            if (dgvMappings.Rows.Count == 0)
            {
                MessageBox.Show("请至少添加一个映射！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 检查是否至少有一个脆性矿物和一个塑性矿物的映射
            bool hasBrittleMineral = false;
            bool hasDuctileMineral = false;

            foreach (DataGridViewRow row in dgvMappings.Rows)
            {
                if (row.Cells[1].Value.ToString() == TARGET_BRITTLE_MINERAL)
                {
                    hasBrittleMineral = true;
                }
                else if (row.Cells[1].Value.ToString() == TARGET_DUCTILE_MINERAL)
                {
                    hasDuctileMineral = true;
                }
            }

            // 如果没有脆性矿物或塑性矿物的映射，提示用户
            if (!hasBrittleMineral || !hasDuctileMineral)
            {
                string missingTypes = "";
                if (!hasBrittleMineral) missingTypes += "脆性矿物 ";
                if (!hasDuctileMineral) missingTypes += "塑性矿物 ";

                DialogResult result = MessageBox.Show($"您尚未映射 {missingTypes}类型的列。是否继续？",
                    "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    return;
                }
            }

            // 所有检查通过，设置对话框结果并关闭
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        public Dictionary<string, string> GetMappings()
        {
            return _mappings;
        }

        /// <summary>
        /// 获取脆性矿物列表
        /// </summary>
        public List<string> GetBrittleMinerals()
        {
            return _brittleColumns;
        }

        /// <summary>
        /// 获取塑性矿物列表
        /// </summary>
        public List<string> GetDuctileMinerals()
        {
            return _ductileColumns;
        }

        /// <summary>
        /// 数据网格单元格格式化事件处理程序
        /// </summary>
        private void DgvMappings_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
                {
                    // 获取目标列的值
                    if (e.ColumnIndex == 1 && e.Value != null)
                    {
                        string targetValue = e.Value.ToString();

                        // 根据目标列的值设置不同的颜色
                        if (targetValue == TARGET_BRITTLE_MINERAL)
                        {
                            // 脆性矿物使用蓝色系
                            e.CellStyle.BackColor = Color.FromArgb(80, 100, 120);
                            e.CellStyle.ForeColor = Color.White;
                        }
                        else if (targetValue == TARGET_DUCTILE_MINERAL)
                        {
                            // 塑性矿物使用红色系
                            e.CellStyle.BackColor = Color.FromArgb(120, 80, 100);
                            e.CellStyle.ForeColor = Color.White;
                        }
                        else if (targetValue == "顶深/m" || targetValue == "底深/m")
                        {
                            // 深度列使用绿色系
                            e.CellStyle.BackColor = Color.SeaGreen;
                            e.CellStyle.ForeColor = Color.White;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"单元格格式化时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗体大小改变事件处理程序
        /// </summary>
        private void ManualMappingForm_Resize(object sender, EventArgs e)
        {
            try
            {
                // 调整控件位置和大小
                AdjustControlPositions();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"调整控件位置时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 调整控件位置和大小
        /// </summary>
        private void AdjustControlPositions()
        {
            // 计算边距和间距
            int margin = 20;
            int spacing = 10;

            // 调整标签位置
            lblSource.Location = new Point(margin, margin);
            lblTarget.Location = new Point(this.ClientSize.Width / 2 - 80, margin);

            // 调整下拉框位置和大小
            cboSourceColumns.Location = new Point(margin, lblSource.Bottom + spacing);
            cboSourceColumns.Width = this.ClientSize.Width / 3 - margin - spacing;

            cboTargetColumns.Location = new Point(cboSourceColumns.Right + spacing, lblTarget.Bottom + spacing);
            cboTargetColumns.Width = this.ClientSize.Width / 3 - spacing;

            // 调整按钮位置 - 确保按钮不会被下拉框挡住
            btnAdd.Location = new Point(cboTargetColumns.Right + spacing, cboSourceColumns.Top);
            btnRemove.Location = new Point(cboTargetColumns.Right + spacing, btnAdd.Bottom + spacing);

            // 调整数据网格视图位置和大小
            dgvMappings.Location = new Point(margin, cboSourceColumns.Bottom + spacing * 2);
            dgvMappings.Width = this.ClientSize.Width - margin * 2;
            dgvMappings.Height = this.ClientSize.Height - dgvMappings.Top - btnSave.Height - spacing * 3;

            // 调整保存和取消按钮位置
            btnSave.Location = new Point(this.ClientSize.Width - margin - btnCancel.Width - btnSave.Width - spacing, this.ClientSize.Height - margin - btnSave.Height);
            btnCancel.Location = new Point(this.ClientSize.Width - margin - btnCancel.Width, this.ClientSize.Height - margin - btnCancel.Height);
        }
    }
}
