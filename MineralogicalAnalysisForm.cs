using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace BritSystem
{
    public partial class MineralogicalAnalysisForm : Form
    {
        // 配置
        private class Config
        {
            public int MaxHeaderSearchRows { get; set; } = 10;
            public List<string> RequiredColumns { get; set; } = new List<string> { "顶深", "底深", "脆性" };
        }

        private Config config = new Config();
        private Dictionary<string, (int row, int col)> columnPositions = new Dictionary<string, (int row, int col)>();
        private DataTable mineralData;
        private string currentExcelFile;
        private SaveFileDialog saveFileDialog = new SaveFileDialog();

        // UI控件
        private Panel mainPanel;
        private DataGridView dataGridView;
        private Chart mineralChart;
        private Button btnSelectFile;
        private Button btnReadExcel;
        private Button btnExportExcel;
        private Button btnGenerateCurve;
        private Button btnSaveCurve;

        public MineralogicalAnalysisForm()
        {
            InitializeComponent();
            InitializeUI();
            SetupSaveFileDialog();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 窗体设置
            this.Text = "矿物学分析";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;

            this.ResumeLayout(false);
        }

        private void InitializeUI()
        {
            // 主面板
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill
            };
            this.Controls.Add(mainPanel);

            // 数据网格
            dataGridView = new DataGridView
            {
                Dock = DockStyle.Top,
                Height = 300,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            mainPanel.Controls.Add(dataGridView);

            // 图表
            mineralChart = new Chart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240)
            };
            mineralChart.ChartAreas.Add(new ChartArea("MainArea"));
            mineralChart.ChartAreas[0].AxisX.Title = "脆性指数 (%)";
            mineralChart.ChartAreas[0].AxisY.Title = "深度 (m)";
            mineralChart.ChartAreas[0].AxisY.IsReversed = true; // 深度从上到下增加
            mainPanel.Controls.Add(mineralChart);

            // 按钮面板
            Panel buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 40
            };
            mainPanel.Controls.Add(buttonPanel);

            // 选择文件按钮
            btnSelectFile = new Button
            {
                Text = "选择文件",
                Size = new Size(100, 30),
                Location = new Point(10, 5)
            };
            btnSelectFile.Click += BtnSelectFile_Click;
            buttonPanel.Controls.Add(btnSelectFile);

            // 自动识别按钮
            btnReadExcel = new Button
            {
                Text = "自动识别",
                Size = new Size(100, 30),
                Location = new Point(120, 5)
            };
            btnReadExcel.Click += BtnReadExcel_Click;
            buttonPanel.Controls.Add(btnReadExcel);

            // 导出数据按钮
            btnExportExcel = new Button
            {
                Text = "导出数据",
                Size = new Size(100, 30),
                Location = new Point(230, 5)
            };
            btnExportExcel.Click += BtnExportExcel_Click;
            buttonPanel.Controls.Add(btnExportExcel);

            // 生成曲线按钮
            btnGenerateCurve = new Button
            {
                Text = "生成曲线",
                Size = new Size(150, 35),
                Dock = DockStyle.Bottom
            };
            btnGenerateCurve.Click += BtnGenerateCurve_Click;
            mainPanel.Controls.Add(btnGenerateCurve);

            // 保存图像按钮
            btnSaveCurve = new Button
            {
                Text = "保存图像",
                Size = new Size(150, 35),
                Dock = DockStyle.Bottom
            };
            btnSaveCurve.Click += (s, e) => MessageBox.Show("请调用现有的保存图像功能", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            mainPanel.Controls.Add(btnSaveCurve);
            mainPanel.Controls.Add(mineralChart);
        }

        private void SetupSaveFileDialog()
        {
            saveFileDialog.Filter = "Excel文件|*.xlsx|CSV文件|*.csv";
            saveFileDialog.Title = "保存数据";
            saveFileDialog.DefaultExt = "xlsx";
        }
        // 导出到Excel
        private void ExportToExcel(string filePath)
        {
            // 使用NPOI导出到Excel
            using (var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                var workbook = new XSSFWorkbook();
                var sheet = workbook.CreateSheet("矿物数据");

                // 创建表头
                var headerRow = sheet.CreateRow(0);
                for (int i = 0; i < mineralData.Columns.Count; i++)
                {
                    var cell = headerRow.CreateCell(i);
                    cell.SetCellValue(mineralData.Columns[i].ColumnName);
                }

                // 写入数据
                for (int i = 0; i < mineralData.Rows.Count; i++)
                {
                    var row = sheet.CreateRow(i + 1);
                    for (int j = 0; j < mineralData.Columns.Count; j++)
                    {
                        var cell = row.CreateCell(j);
                        var value = mineralData.Rows[i][j];
                        if (value != DBNull.Value)
                        {
                            if (value is double doubleValue)
                            {
                                cell.SetCellValue(doubleValue);
                            }
                            else
                            {
                                cell.SetCellValue(value.ToString());
                            }
                        }
                    }
                }

                workbook.Write(fs);
            }
        }

        // 导出到CSV
        private void ExportToCsv(string filePath)
        {
            using (var sw = new StreamWriter(filePath, false, Encoding.UTF8))
            {
                // 写入表头
                sw.WriteLine(string.Join(",", mineralData.Columns.Cast<DataColumn>().Select(c => c.ColumnName)));

                // 写入数据
                foreach (DataRow row in mineralData.Rows)
                {
                    sw.WriteLine(string.Join(",", row.ItemArray.Select(item => item.ToString().Replace(",", "\",\""))));
                }
            }
        }

        // 导入数据按钮事件处理
        private void BtnSelectFile_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            mineralData = ds.Tables[0];
                            MessageBox.Show("文件加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // 自动识别列头
                            AutoDetectColumns();
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        // 自动识别按钮事件处理
        private void BtnReadExcel_Click(object sender, EventArgs e)
        {
            if (mineralData == null)
            {
                MessageBox.Show("请先选择文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            AutoDetectColumns();
        }

        // 自动检测列头和处理数据
        private void AutoDetectColumns()
        {
            try
            {
                if (mineralData == null)
                {
                    MessageBox.Show("数据为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 开始自动检测列
                // 直接假设第一行是顶深，第二行是底深，第三行是脆性指数
                columnPositions.Clear();
                if (mineralData.Columns.Count >= 3)
                {
                    columnPositions.Add("顶深", (0, 0));
                    columnPositions.Add("底深", (0, 1));
                    columnPositions.Add("脆性", (0, 2));

                    var cleanData = ProcessRawData(mineralData);
                    dataGridView.DataSource = cleanData;
                    MessageBox.Show($"成功处理 {cleanData.Rows.Count} 行数据", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("数据列数不足，需要至少3列数据", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"自动检测失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 导出数据按钮事件处理
        private void BtnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                if (mineralData == null || mineralData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可供保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;
                    string extension = Path.GetExtension(filePath).ToLower();

                    switch (extension)
                    {
                        case ".xlsx":
                            ExportToExcel(filePath);
                            break;
                        case ".csv":
                            ExportToCsv(filePath);
                            break;
                        default:
                            MessageBox.Show("不支持的文件格式", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                    }

                    MessageBox.Show("数据保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 生成曲线按钮事件处理
        private void BtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView.DataSource == null || !(dataGridView.DataSource is DataTable) || dataGridView.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                mineralChart.Series.Clear();

                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.FastLine,
                    Color = Color.Blue,
                    BorderWidth = 2
                };
                var pointSeries = new Series("矿物点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Red,
                    MarkerSize = 8
                };

                var data = (DataTable)dataGridView.DataSource;
                List<double> topDepths = new List<double>();

                // 收集数据点
                for (int i = 0; i < data.Rows.Count; i++)
                {
                    var row = data.Rows[i];
                    double topDepth = Convert.ToDouble(row["顶深/m"]);
                    double brittleIndex = Convert.ToDouble(row["脆性指数"]);

                    if (!double.IsNaN(topDepth) && !double.IsInfinity(topDepth) &&
                        !double.IsNaN(brittleIndex) && !double.IsInfinity(brittleIndex))
                    {
                        series.Points.AddXY(brittleIndex, topDepth);
                        pointSeries.Points.AddXY(brittleIndex, topDepth);
                        topDepths.Add(topDepth);
                    }
                }

                if (topDepths.Count > 0)
                {
                    double minTopDepth = topDepths.Min();
                    double maxTopDepth = topDepths.Max();

                    var chartArea = mineralChart.ChartAreas[0];
                    chartArea.AxisY.CustomLabels.Clear();

                    // 设置Y轴范围
                    chartArea.AxisY.Minimum = minTopDepth;
                    chartArea.AxisY.Maximum = maxTopDepth;

                    // 设置Y轴属性
                    chartArea.AxisY.LabelStyle.Format = "0.00";
                    chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
                    chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;
                }

                mineralChart.Series.Add(series);
                mineralChart.Series.Add(pointSeries);

                // 强制刷新图表
                mineralChart.Invalidate();
                MessageBox.Show("曲线生成成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 检测列位置
        private void DetectColumnsPosition(DataTable table)
        {
            columnPositions.Clear();

            for (int row = 0; row < Math.Min(config.MaxHeaderSearchRows, table.Rows.Count); row++)
            {
                for (int col = 0; col < table.Columns.Count; col++)
                {
                    string cellValue = table.Rows[row][col].ToString().CleanString();

                    foreach (var keyword in config.RequiredColumns)
                    {
                        if (cellValue.Contains(keyword.CleanString()))
                        {
                            if (!columnPositions.ContainsKey(keyword))
                            {
                                columnPositions.Add(keyword, (row, col));
                            }
                        }
                    }
                }
            }

            var missing = config.RequiredColumns.Where(c => !columnPositions.ContainsKey(c)).ToList();
            if (missing.Any())
                throw new Exception($"缺少必要列头：{string.Join(",", missing)}");
        }

        // 处理原始数据
        private DataTable ProcessRawData(DataTable rawData)
        {
            var cleanData = new DataTable();
            cleanData.Columns.Add("顶深/m", typeof(double));
            cleanData.Columns.Add("底深/m", typeof(double));
            cleanData.Columns.Add("脆性指数", typeof(double));

            int startRow = columnPositions.Values.Max(p => p.row) + 1;
            List<double> topDepths = new List<double>();
            int validRows = 0;
            int invalidRows = 0;

            for (int i = startRow; i < rawData.Rows.Count; i++)
            {
                try
                {
                    var row = rawData.Rows[i];

                    double top = ParseCell(row[columnPositions["顶深"].col]);
                    double bottom = ParseCell(row[columnPositions["底深"].col]);
                    double brittle = ParseCell(row[columnPositions["脆性"].col]);

                    if (IsValidData(top, bottom, brittle))
                    {
                        cleanData.Rows.Add(top, bottom, brittle);
                        topDepths.Add(top);
                        validRows++;
                    }
                    else
                    {
                        invalidRows++;
                    }
                }
                catch (Exception ex)
                {
                    // 处理行时出错
                    invalidRows++;
                }
            }

            return cleanData;
        }

        // 验证数据有效性
        private bool IsValidData(double top, double bottom, double brittle)
        {
            bool isValid = !double.IsNaN(top) &&
                          !double.IsNaN(bottom) &&
                          !double.IsNaN(brittle) &&
                          top < bottom &&
                          brittle >= 0 &&
                          brittle <= 100;

            if (!isValid)
            {
                string reason = "";
                if (double.IsNaN(top)) reason += "顶深无效 ";
                if (double.IsNaN(bottom)) reason += "底深无效 ";
                if (double.IsNaN(brittle)) reason += "脆性指数无效 ";
                if (top >= bottom) reason += "顶深不小于底深 ";
                if (brittle < 0 || brittle > 100) reason += "脆性指数超出范围 ";

                // 数据验证失败
                System.Diagnostics.Debug.WriteLine($"数据验证失败: {reason}");
            }

            return isValid;
        }

        // 解析单元格值
        private double ParseCell(object cellValue)
        {
            if (cellValue == DBNull.Value || string.IsNullOrWhiteSpace(cellValue.ToString()))
            {
                return double.NaN;
            }

            string strValue = cellValue.ToString().Trim();

            // 尝试直接解析为数字
            if (double.TryParse(strValue, out double result))
            {
                if (double.IsInfinity(result) || double.IsNaN(result))
                {
                    return double.NaN;
                }
                return result;
            }

            // 检查是否是Excel公式（如AU5+AT5+AS5+AR5+AQ5）
            if (IsExcelFormula(strValue))
            {
                // 尝试计算公式结果
                double formulaResult = EvaluateExcelFormula(strValue);
                if (!double.IsNaN(formulaResult))
                {
                    return formulaResult;
                }
            }

            return double.NaN;
        }

        // 检查字符串是否是Excel公式
        private bool IsExcelFormula(string value)
        {
            // 检查是否包含Excel单元格引用模式（如A1、B2等）和运算符
            return Regex.IsMatch(value, @"[A-Z]+\d+[\+\-\*\/][A-Z]+\d+");
        }

        // 计算Excel公式的值
        private double EvaluateExcelFormula(string formula)
        {
            try
            {
                // 分解公式（例如：AU5+AT5+AS5+AR5+AQ5）
                string[] parts = Regex.Split(formula, @"(\+|\-|\*|\/)");
                if (parts.Length < 3) // 至少需要两个操作数和一个运算符
                {
                    return double.NaN;
                }

                // 提取所有数值
                List<double> values = new List<double>();
                List<string> operators = new List<string>();

                for (int i = 0; i < parts.Length; i++)
                {
                    string part = parts[i].Trim();
                    if (string.IsNullOrEmpty(part)) continue;

                    // 如果是运算符，添加到运算符列表
                    if (part == "+" || part == "-" || part == "*" || part == "/")
                    {
                        operators.Add(part);
                    }
                    // 否则尝试获取单元格引用的值
                    else if (Regex.IsMatch(part, @"^[A-Z]+\d+$"))
                    {
                        // 这里我们假设单元格引用的值已经在Excel中计算好了
                        // 由于我们无法直接访问Excel单元格的值，我们假设每个单元格引用的值为0
                        // 在实际应用中，这里应该从Excel文件中获取对应单元格的值
                        values.Add(0);
                    }
                    // 尝试直接解析为数字
                    else if (double.TryParse(part, out double value))
                    {
                        values.Add(value);
                    }
                    else
                    {
                        // 无法解析的部分，返回NaN
                        return double.NaN;
                    }
                }

                // 如果值的数量不等于运算符数量+1，则公式无效
                if (values.Count != operators.Count + 1)
                {
                    return double.NaN;
                }

                // 计算公式结果
                double result = values[0];
                for (int i = 0; i < operators.Count; i++)
                {
                    switch (operators[i])
                    {
                        case "+":
                            result += values[i + 1];
                            break;
                        case "-":
                            result -= values[i + 1];
                            break;
                        case "*":
                            result *= values[i + 1];
                            break;
                        case "/":
                            if (values[i + 1] == 0) return double.NaN; // 避免除以零
                            result /= values[i + 1];
                            break;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算公式时出错: {ex.Message}", "调试", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return double.NaN;
            }
        }

        // 读取Excel文件
        private DataSet ReadExcelSheets(string path)
        {
            var dataSet = new DataSet();

            using (var fs = new FileStream(path, FileMode.Open, FileAccess.Read))
            {
                var workbook = new XSSFWorkbook(fs);
                var evaluator = workbook.GetCreationHelper().CreateFormulaEvaluator();

                for (int i = 0; i < workbook.NumberOfSheets; i++)
                {
                    var sheet = workbook.GetSheetAt(i);
                    var dataTable = new DataTable(sheet.SheetName);

                    // 读取表头
                    var headerRow = sheet.GetRow(0);
                    if (headerRow != null)
                    {
                        for (int col = 0; col < headerRow.LastCellNum; col++)
                        {
                            var cell = headerRow.GetCell(col);
                            dataTable.Columns.Add(cell?.ToString() ?? $"Column{col + 1}");
                        }
                    }

                    // 读取数据
                    for (int row = 1; row <= sheet.LastRowNum; row++)
                    {
                        var dataRow = sheet.GetRow(row);
                        if (dataRow != null)
                        {
                            var newRow = dataTable.NewRow();
                            for (int col = 0; col < dataTable.Columns.Count; col++)
                            {
                                var cell = dataRow.GetCell(col);
                                if (cell != null)
                                {
                                    // 根据单元格类型处理
                                    switch (cell.CellType)
                                    {
                                        case CellType.Formula:
                                            try
                                            {
                                                // 尝试计算公式
                                                var evaluatedCell = evaluator.Evaluate(cell);
                                                if (evaluatedCell != null)
                                                {
                                                    switch (evaluatedCell.CellType)
                                                    {
                                                        case CellType.Numeric:
                                                            newRow[col] = evaluatedCell.NumberValue.ToString();
                                                            break;
                                                        case CellType.String:
                                                            newRow[col] = evaluatedCell.StringValue;
                                                            break;
                                                        case CellType.Boolean:
                                                            newRow[col] = evaluatedCell.BooleanValue.ToString();
                                                            break;
                                                        default:
                                                            // 如果无法计算，则保留原始公式
                                                            newRow[col] = cell.CellFormula;
                                                            break;
                                                    }
                                                }
                                                else
                                                {
                                                    // 如果无法计算，则保留原始公式
                                                    newRow[col] = cell.CellFormula;
                                                }
                                            }
                                            catch
                                            {
                                                // 如果计算出错，则保留原始公式
                                                newRow[col] = cell.CellFormula;
                                            }
                                            break;
                                        case CellType.Numeric:
                                            newRow[col] = cell.NumericCellValue.ToString();
                                            break;
                                        case CellType.String:
                                            newRow[col] = cell.StringCellValue;
                                            break;
                                        case CellType.Boolean:
                                            newRow[col] = cell.BooleanCellValue.ToString();
                                            break;
                                        default:
                                            newRow[col] = cell.ToString() ?? "";
                                            break;
                                    }
                                }
                                else
                                {
                                    newRow[col] = "";
                                }
                            }
                            dataTable.Rows.Add(newRow);
                        }
                    }

                    dataSet.Tables.Add(dataTable);
                }
            }

            return dataSet;
        }
    }

    // 扩展方法
    public static class StringExtensions
    {
        public static string CleanString(this string str)
        {
            if (string.IsNullOrEmpty(str))
                return string.Empty;

            // 移除空格、标点符号等
            return Regex.Replace(str.ToLower(), @"[\s\p{P}]", "");
        }
    }
}