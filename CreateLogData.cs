using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BritSystem.Logging;

namespace BritSystem.Logging
{
    /// <summary>
    /// 日志数据创建类，用于生成和管理脆性指数系统的日志数据
    /// </summary>
    public class CreateLogData
    {
        // 日志管理器
        private LogDataManager _logManager;
        
        // 日志文件路径
        private string _logFilePath;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        public CreateLogData(string logFilePath = "BritSystemLog.txt")
        {
            _logFilePath = logFilePath;
            _logManager = new LogDataManager(_logFilePath);
        }
        
        /// <summary>
        /// 记录系统启动日志
        /// </summary>
        /// <param name="version">系统版本</param>
        public void LogSystemStart(string version)
        {
            _logManager.LogInfo($"脆性指数分析系统 v{version} 启动");
        }
        
        /// <summary>
        /// 记录用户登录日志
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="success">是否登录成功</param>
        public void LogUserLogin(string username, bool success)
        {
            if (success)
            {
                _logManager.LogInfo($"用户 {username} 登录成功");
            }
            else
            {
                _logManager.LogWarning($"用户 {username} 登录失败");
            }
        }
        
        /// <summary>
        /// 记录数据加载日志
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="rowCount">行数</param>
        /// <param name="columnCount">列数</param>
        /// <param name="success">是否加载成功</param>
        public void LogDataLoading(string filePath, int rowCount, int columnCount, bool success)
        {
            if (success)
            {
                _logManager.LogInfo($"成功加载数据文件: {filePath}, 共 {rowCount} 行, {columnCount} 列");
            }
            else
            {
                _logManager.LogError($"加载数据文件失败: {filePath}");
            }
        }
        
        /// <summary>
        /// 记录列检测日志
        /// </summary>
        /// <param name="topDepthColumn">顶深列</param>
        /// <param name="bottomDepthColumn">底深列</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        public void LogColumnDetection(string topDepthColumn, string bottomDepthColumn, 
            List<string> brittleColumns, List<string> ductileColumns)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"列检测结果:");
            sb.AppendLine($"  顶深列: {topDepthColumn}");
            sb.AppendLine($"  底深列: {bottomDepthColumn}");
            
            sb.AppendLine($"  脆性矿物列 ({brittleColumns.Count}):");
            foreach (string column in brittleColumns)
            {
                sb.AppendLine($"    - {column}");
            }
            
            sb.AppendLine($"  塑性矿物列 ({ductileColumns.Count}):");
            foreach (string column in ductileColumns)
            {
                sb.AppendLine($"    - {column}");
            }
            
            _logManager.LogInfo(sb.ToString());
        }
        
        /// <summary>
        /// 记录脆性指数计算日志
        /// </summary>
        /// <param name="resultCount">结果数量</param>
        /// <param name="minIndex">最小脆性指数</param>
        /// <param name="maxIndex">最大脆性指数</param>
        /// <param name="avgIndex">平均脆性指数</param>
        /// <param name="success">是否计算成功</param>
        public void LogBrittlenessCalculation(int resultCount, double minIndex, double maxIndex, 
            double avgIndex, bool success)
        {
            if (success)
            {
                _logManager.LogInfo($"脆性指数计算完成: 共 {resultCount} 个结果, " +
                    $"最小值: {minIndex:F2}%, 最大值: {maxIndex:F2}%, 平均值: {avgIndex:F2}%");
            }
            else
            {
                _logManager.LogError("脆性指数计算失败");
            }
        }
        
        /// <summary>
        /// 记录数据保存日志
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="rowCount">行数</param>
        /// <param name="success">是否保存成功</param>
        public void LogDataSaving(string filePath, int rowCount, bool success)
        {
            if (success)
            {
                _logManager.LogInfo($"成功保存数据到文件: {filePath}, 共 {rowCount} 行");
            }
            else
            {
                _logManager.LogError($"保存数据到文件失败: {filePath}");
            }
        }
        
        /// <summary>
        /// 记录异常日志
        /// </summary>
        /// <param name="ex">异常</param>
        /// <param name="context">上下文信息</param>
        public void LogException(Exception ex, string context)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"异常发生在 {context}:");
            sb.AppendLine($"  消息: {ex.Message}");
            sb.AppendLine($"  类型: {ex.GetType().Name}");
            sb.AppendLine($"  堆栈跟踪: {ex.StackTrace}");
            
            if (ex.InnerException != null)
            {
                sb.AppendLine($"  内部异常: {ex.InnerException.Message}");
            }
            
            _logManager.LogError(sb.ToString());
        }
        
        /// <summary>
        /// 记录用户操作日志
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="action">操作</param>
        /// <param name="details">详细信息</param>
        public void LogUserAction(string username, string action, string details = "")
        {
            string message = $"用户 {username} 执行操作: {action}";
            if (!string.IsNullOrEmpty(details))
            {
                message += $", 详情: {details}";
            }
            
            _logManager.LogInfo(message);
        }
        
        /// <summary>
        /// 获取所有日志
        /// </summary>
        /// <returns>日志条目列表</returns>
        public List<LogEntry> GetAllLogs()
        {
            return _logManager.GetAllLogEntries();
        }
        
        /// <summary>
        /// 获取今天的日志
        /// </summary>
        /// <returns>日志条目列表</returns>
        public List<LogEntry> GetTodayLogs()
        {
            DateTime today = DateTime.Today;
            DateTime tomorrow = today.AddDays(1);
            
            return _logManager.GetLogEntriesByTimeRange(today, tomorrow);
        }
        
        /// <summary>
        /// 获取错误日志
        /// </summary>
        /// <returns>日志条目列表</returns>
        public List<LogEntry> GetErrorLogs()
        {
            return _logManager.GetLogEntriesByLevel(LogLevel.Error);
        }
        
        /// <summary>
        /// 清除所有日志
        /// </summary>
        public void ClearAllLogs()
        {
            _logManager.ClearLogs();
        }
    }
}
