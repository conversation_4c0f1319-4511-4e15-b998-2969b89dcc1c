using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using System.IO;
using System.Diagnostics;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace BritSystem
{
    /// <summary>
    /// 静态岩石力学参数法脆性指数计算窗体（设计器版本）
    /// </summary>
    public partial class StaticRockMechanicsFormR : Form
    {
        #region 字段和属性

        private string username;
        private DataTable mechanicsData;
        private DataTable originalMechanicsData;
        private string currentExcelFile;

        #endregion

        #region 构造函数

        public StaticRockMechanicsFormR()
        {
            InitializeComponent();
            InitializeForm();
        }

        public StaticRockMechanicsFormR(string username)
        {
            this.username = username;
            InitializeComponent();
            InitializeForm();

            if (lblWelcome != null)
            {
                lblWelcome.Text = $"欢迎使用静态岩石力学参数法, {username}";
            }
        }

        #endregion

        #region 初始化方法

        private void InitializeForm()
        {
            // 初始化数据表
            InitializeDataTable();

            // 绑定事件
            this.Load += StaticRockMechanicsFormR_Load;
            this.Resize += StaticRockMechanicsFormR_Resize;
            this.FormClosing += StaticRockMechanicsFormR_FormClosing;
        }

        private void InitializeDataTable()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("顶深/m", typeof(double));
            mechanicsData.Columns.Add("底深/m", typeof(double));
            mechanicsData.Columns.Add("密度/(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("动态泊松比", typeof(double));
            mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("静态泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            dgvMechanicsData.DataSource = mechanicsData;
        }

        #endregion

        #region 事件处理方法

        private void StaticRockMechanicsFormR_Load(object sender, EventArgs e)
        {
            // 窗体加载时的初始化
        }

        private void StaticRockMechanicsFormR_Resize(object sender, EventArgs e)
        {
            // 窗体大小改变时自动调整
            // 由于使用了设计器，大部分控件会自动调整大小，这里只需要处理特殊情况
        }

        private void StaticRockMechanicsFormR_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (chartBrittleness != null)
                {
                    chartBrittleness.Series.Clear();
                    chartBrittleness.ChartAreas.Clear();
                    chartBrittleness.Legends.Clear();
                    chartBrittleness.Dispose();
                }
                e.Cancel = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗体时出错: {ex.Message}");
                e.Cancel = false;
            }
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void btnEmergencyExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void btnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取输入参数
                if (!double.TryParse(txtDensity.Text, out double density) ||
                    !double.TryParse(txtVp.Text, out double vp) ||
                    !double.TryParse(txtVs.Text, out double vs))
                {
                    MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 根据公式计算静态岩石力学参数
                var result = CalculateStaticRockMechanics(density, vp, vs);

                // 显示计算结果
                lblCalculationResult.Text = $"Ed={result.Ed:F3}GPa, μd={result.MuD:F4}, Es={result.Es:F3}GPa, μs={result.MuS:F4}, BRIT={result.BrittlenessIndex:F2}%";

                // 添加到数据表
                DataRow newRow = mechanicsData.NewRow();
                newRow["顶深/m"] = 0.0; // 默认值，用户可以在导入数据时修改
                newRow["底深/m"] = 0.0; // 默认值
                newRow["密度/(g/cm³)"] = density;
                newRow["纵波速度/(m/s)"] = vp;
                newRow["横波速度/(m/s)"] = vs;
                newRow["动态杨氏模量/GPa"] = result.Ed;
                newRow["动态泊松比"] = result.MuD;
                newRow["静态杨氏模量/GPa"] = result.Es;
                newRow["静态泊松比"] = result.MuS;
                newRow["脆性指数/%"] = result.BrittlenessIndex;

                mechanicsData.Rows.Add(newRow);

                MessageBox.Show($"计算成功！脆性指数为: {result.BrittlenessIndex:F2}%", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnImport_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            mechanicsData = ds.Tables[0];
                            originalMechanicsData = mechanicsData.Copy();
                            dgvMechanicsData.DataSource = mechanicsData;

                            MessageBox.Show("文件加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件 (*.xlsx)|*.xlsx";
                saveFileDialog.Title = "保存Excel文件";
                saveFileDialog.FileName = $"静态岩石力学参数数据_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportExcelFile(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 清除现有系列
                chartBrittleness.Series.Clear();

                // 创建脆性指数曲线系列
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.Spline,
                    Color = Color.Cyan,
                    BorderWidth = 2,
                    MarkerStyle = MarkerStyle.Circle,
                    MarkerSize = 6,
                    MarkerColor = Color.Yellow
                };

                // 添加数据点
                foreach (DataRow row in mechanicsData.Rows)
                {
                    if (row["脆性指数/%"] != DBNull.Value && row["顶深/m"] != DBNull.Value)
                    {
                        double brittleness = Convert.ToDouble(row["脆性指数/%"]);
                        double depth = Convert.ToDouble(row["顶深/m"]);

                        series.Points.AddXY(brittleness, depth);
                    }
                }

                chartBrittleness.Series.Add(series);

                // 设置轴范围
                if (series.Points.Count > 0)
                {
                    var depths = series.Points.Select(p => p.YValues[0]).ToList();
                    var brittlenessValues = series.Points.Select(p => p.XValue).ToList();

                    chartBrittleness.ChartAreas[0].AxisY.Minimum = depths.Min();
                    chartBrittleness.ChartAreas[0].AxisY.Maximum = depths.Max();
                    chartBrittleness.ChartAreas[0].AxisX.Minimum = Math.Max(0, brittlenessValues.Min() - 5);
                    chartBrittleness.ChartAreas[0].AxisX.Maximum = brittlenessValues.Max() + 5;
                }

                MessageBox.Show("曲线生成成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输入框
                txtDensity.Text = "";
                txtVp.Text = "";
                txtVs.Text = "";
                lblCalculationResult.Text = "计算结果将在此显示";

                // 清空图表
                chartBrittleness.Series.Clear();

                // 重置数据表
                if (originalMechanicsData != null)
                {
                    mechanicsData = originalMechanicsData.Copy();
                    dgvMechanicsData.DataSource = mechanicsData;
                }
                else
                {
                    InitializeDataTable();
                }

                MessageBox.Show("已重置所有数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSaveCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartBrittleness.Series.Count == 0)
                {
                    MessageBox.Show("没有曲线可以保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "PNG图片 (*.png)|*.png|JPEG图片 (*.jpg)|*.jpg";
                saveFileDialog.Title = "保存曲线图片";
                saveFileDialog.FileName = $"静态岩石力学参数脆性指数曲线_{DateTime.Now:yyyyMMdd}.png";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    chartBrittleness.SaveImage(saveFileDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show("曲线图片保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 计算方法

        /// <summary>
        /// 计算结果结构体
        /// </summary>
        public struct RockMechanicsResult
        {
            public double Ed;              // 动态杨氏模量 (GPa)
            public double MuD;             // 动态泊松比
            public double Es;              // 静态杨氏模量 (GPa)
            public double MuS;             // 静态泊松比
            public double BrittlenessIndex; // 脆性指数 (%)
        }

        /// <summary>
        /// 根据公式计算静态岩石力学参数
        /// </summary>
        /// <param name="density">岩石密度 (g/cm³)</param>
        /// <param name="vp">纵波速度 (m/s)</param>
        /// <param name="vs">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        private RockMechanicsResult CalculateStaticRockMechanics(double density, double vp, double vs)
        {
            var result = new RockMechanicsResult();

            // 转换密度单位：g/cm³ -> kg/m³
            double rho = density * 1000;

            // 计算动态杨氏模量 Ed (GPa)
            // Ed = 10^-3 * ρ * Vs^2 * (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)
            double vp2 = vp * vp;
            double vs2 = vs * vs;
            result.Ed = 1e-3 * rho * vs2 * (3 * vp2 - 4 * vs2) / (vp2 - vs2) / 1e9; // 转换为GPa

            // 计算动态泊松比 μd
            // μd = (Vp^2 - 2Vs^2) / (2(Vp^2 - Vs^2))
            result.MuD = (vp2 - 2 * vs2) / (2 * (vp2 - vs2));

            // 计算静态杨氏模量 Es (GPa)
            // Es = Ed × 0.5823 + 7.566
            result.Es = result.Ed * 0.5823 + 7.566;

            // 计算静态泊松比 μs
            // μs = μd × 0.6648 + 0.0514
            result.MuS = result.MuD * 0.6648 + 0.0514;

            // 计算脆性指数
            // 需要先计算归一化的杨氏模量和泊松比
            // 这里使用常见的岩石参数范围进行归一化
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;

            // 归一化杨氏模量脆性指数
            // EBRIT = (Es - Emin) / (Emax - Emin) × 100%
            double EBRIT = (result.Es - EsMin) / (EsMax - EsMin) * 100;
            EBRIT = Math.Max(0, Math.Min(100, EBRIT)); // 限制在0-100%范围内

            // 归一化泊松比脆性指数
            // μBRIT = (μmax - μs) / (μmax - μmin) × 100%
            double MuBRIT = (MuSMax - result.MuS) / (MuSMax - MuSMin) * 100;
            MuBRIT = Math.Max(0, Math.Min(100, MuBRIT)); // 限制在0-100%范围内

            // 综合脆性指数
            // BRITe = (EBRIT + μBRIT) / 2
            result.BrittlenessIndex = (EBRIT + MuBRIT) / 2;

            return result;
        }

        #endregion

        #region Excel读写方法

        /// <summary>
        /// 读取Excel文件
        /// </summary>
        private DataSet ReadExcelSheets(string filePath)
        {
            try
            {
                DataSet dataSet = new DataSet();

                using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook = null;

                    if (Path.GetExtension(filePath).ToLower() == ".xls")
                    {
                        workbook = new HSSFWorkbook(stream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(stream);
                    }

                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        ISheet sheet = workbook.GetSheetAt(i);
                        DataTable dataTable = new DataTable(sheet.SheetName);

                        // 读取表头
                        IRow headerRow = sheet.GetRow(0);
                        if (headerRow != null)
                        {
                            for (int j = 0; j < headerRow.LastCellNum; j++)
                            {
                                ICell cell = headerRow.GetCell(j);
                                string columnName = cell?.ToString() ?? $"Column{j + 1}";
                                dataTable.Columns.Add(columnName, typeof(object));
                            }
                        }

                        // 读取数据行
                        for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                        {
                            IRow row = sheet.GetRow(rowIndex);
                            if (row != null)
                            {
                                DataRow dataRow = dataTable.NewRow();
                                for (int cellIndex = 0; cellIndex < dataTable.Columns.Count; cellIndex++)
                                {
                                    ICell cell = row.GetCell(cellIndex);
                                    if (cell != null)
                                    {
                                        switch (cell.CellType)
                                        {
                                            case CellType.Numeric:
                                                dataRow[cellIndex] = cell.NumericCellValue;
                                                break;
                                            case CellType.String:
                                                dataRow[cellIndex] = cell.StringCellValue;
                                                break;
                                            case CellType.Boolean:
                                                dataRow[cellIndex] = cell.BooleanCellValue;
                                                break;
                                            case CellType.Formula:
                                                try
                                                {
                                                    dataRow[cellIndex] = cell.NumericCellValue;
                                                }
                                                catch
                                                {
                                                    dataRow[cellIndex] = cell.StringCellValue;
                                                }
                                                break;
                                            default:
                                                dataRow[cellIndex] = cell.ToString();
                                                break;
                                        }
                                    }
                                }
                                dataTable.Rows.Add(dataRow);
                            }
                        }

                        dataSet.Tables.Add(dataTable);
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取Excel文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        private void InitializeComponent()
        {

        }

        /// <summary>
        /// 导出Excel文件
        /// </summary>
        private void ExportExcelFile(string filePath)
        {
            try
            {
                IWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("静态岩石力学参数数据");

                // 创建表头样式
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerStyle.SetFont(headerFont);

                // 创建表头
                IRow headerRow = sheet.CreateRow(0);
                for (int i = 0; i < mechanicsData.Columns.Count; i++)
                {
                    headerRow.CreateCell(i).SetCellValue(mechanicsData.Columns[i].ColumnName);
                    headerRow.GetCell(i).CellStyle = headerStyle;
                }

                // 写入数据
                for (int i = 0; i < mechanicsData.Rows.Count; i++)
                {
                    IRow dataRow = sheet.CreateRow(i + 1);
                    for (int j = 0; j < mechanicsData.Columns.Count; j++)
                    {
                        if (mechanicsData.Rows[i][j] != DBNull.Value)
                        {
                            dataRow.CreateCell(j).SetCellValue(Convert.ToDouble(mechanicsData.Rows[i][j]));
                        }
                    }
                }

                // 自动调整列宽
                for (int i = 0; i < mechanicsData.Columns.Count; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (FileStream stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(stream);
                }

                MessageBox.Show("数据导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出Excel文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
