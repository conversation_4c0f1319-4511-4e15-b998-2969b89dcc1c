        private void BtnReset_Click(object sender, EventArgs e)
        {
            ResetChartView();
        }

        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            // 移除此事件处理程序，因为我们现在使用自定义的选择逻辑
        }

        private void DataGridView_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                DataGridView.HitTestInfo hit = dgvMineralData.HitTest(e.X, e.Y);
                if (hit.RowIndex >= 0)
                {
                    if ((ModifierKeys & Keys.Control) != Keys.Control)
                    {
                        // 如果没有按住Ctrl，清除之前的选择
                        selectedRows.Clear();
                    }
                    selectedRows.Add(hit.RowIndex);
                    UpdateHighlights();
                }
            }
        }

        private void DataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            // 只处理列标题点击
            if (e.RowIndex >= 0 || e.ColumnIndex < 0)
                return;

            // 获取列标题
            string columnHeader = dgvMineralData.Columns[e.ColumnIndex].HeaderText;

            // 显示列信息
            var result = MessageBox.Show($"是否将列 '{columnHeader}' 设置为顶深列？", "列选择",
                MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                columnPositions["顶深"] = (0, e.ColumnIndex);
                MessageBox.Show($"已将列 '{columnHeader}' 设置为顶深列", "设置成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else if (result == DialogResult.No)
            {
                var result2 = MessageBox.Show($"是否将列 '{columnHeader}' 设置为底深列？", "列选择",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result2 == DialogResult.Yes)
                {
                    columnPositions["底深"] = (0, e.ColumnIndex);
                    MessageBox.Show($"已将列 '{columnHeader}' 设置为底深列", "设置成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    var result3 = MessageBox.Show($"是否将列 '{columnHeader}' 设置为脆性指数列？", "列选择",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result3 == DialogResult.Yes)
                    {
                        columnPositions["脆性"] = (0, e.ColumnIndex);
                        MessageBox.Show($"已将列 '{columnHeader}' 设置为脆性指数列", "设置成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
        }

        private void UpdateHighlights()
        {
            // 清除所有行的高亮
            foreach (DataGridViewRow row in dgvMineralData.Rows)
            {
                row.DefaultCellStyle.BackColor = Color.White;
            }

            // 高亮选中的行
            foreach (int rowIndex in selectedRows)
            {
                if (rowIndex < dgvMineralData.Rows.Count)
                {
                    dgvMineralData.Rows[rowIndex].DefaultCellStyle.BackColor = Color.LightGoldenrodYellow;
                }
            }

            // 更新图表点的高亮
            if (chartBrittleness.Series.Count >= 2)
            {
                var pointSeries = chartBrittleness.Series[1];
                for (int i = 0; i < pointSeries.Points.Count; i++)
                {
                    pointSeries.Points[i].Color = Color.Cyan;
                    pointSeries.Points[i].MarkerSize = 8;
                }

                foreach (int rowIndex in selectedRows)
                {
                    if (rowIndex < pointSeries.Points.Count)
                    {
                        pointSeries.Points[rowIndex].Color = Color.FromArgb(0, 200, 255);
                        pointSeries.Points[rowIndex].MarkerSize = 12;
                    }
                }
            }
        }

        private void BtnDeletePoint_Click(object sender, EventArgs e)
        {
            if (selectedRows.Count == 0)
            {
                MessageBox.Show("请先选择要删除的脆性点！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show(
                $"确定要删除选中的 {selectedRows.Count} 个脆性点吗？",
                "确认删除",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // 记录要删除的行
                deletedRows.AddRange(selectedRows);

                // 清除选择
                selectedRows.Clear();

                // 重新生成曲线
                RegenerateCurveWithoutDeletedPoints();
            }
        }

        private void RegenerateCurveWithoutDeletedPoints()
        {
            if (dgvMineralData.DataSource is DataTable dataTable)
            {
                // 创建新的DataTable
                DataTable newTable = dataTable.Clone();

                // 复制未删除的行
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    if (!deletedRows.Contains(i))
                    {
                        newTable.ImportRow(dataTable.Rows[i]);
                    }
                }

                // 更新数据源
                dgvMineralData.DataSource = newTable;

                // 重新生成曲线
                if (newTable.Rows.Count > 0)
                {
                    BtnGenerateCurve_Click(null, EventArgs.Empty);
                }
                else
                {
                    chartBrittleness.Series.Clear();
                }
            }
        }

        private void BtnSaveCurve_Click(object sender, EventArgs e)
        {
            using (var sfd = new SaveFileDialog { Filter = "PNG图像|*.png|JPEG图像|*.jpg" })
            {
                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var format = sfd.FileName.EndsWith(".png") ?
                            ChartImageFormat.Png : ChartImageFormat.Jpeg;

                        // 保存原始视图
                        var chartArea = chartBrittleness.ChartAreas[0];

                        // 保存当前视图状态
                        double currentMin = chartArea.AxisY.ScaleView.ViewMinimum;
                        double currentMax = chartArea.AxisY.ScaleView.ViewMaximum;
                        double currentZoomLevel = currentZoom;

                        // 重置视图
                        chartArea.AxisY.ScaleView.ZoomReset();
                        currentZoom = 1.0;

                        // 确保显示完整的Y轴刻度
                        UpdateYAxisLabels();

                        // 强制刷新图表
                        chartBrittleness.Invalidate();
                        Application.DoEvents();

                        // 保存图像
                        chartBrittleness.SaveImage(sfd.FileName, format);

                        // 恢复之前的视图
                        if (!double.IsNaN(currentMin) && !double.IsNaN(currentMax))
                        {
                            chartArea.AxisY.ScaleView.Zoom(currentMin, currentMax);
                            currentZoom = currentZoomLevel;
                            UpdateYAxisLabels();
                        }

                        MessageBox.Show($"图像已保存到 {sfd.FileName}", "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void SaveCurrentView_Click(object sender, EventArgs e)
        {
            using (var sfd = new SaveFileDialog { Filter = "PNG图像|*.png|JPEG图像|*.jpg" })
            {
                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var format = sfd.FileName.EndsWith(".png") ?
                            ChartImageFormat.Png : ChartImageFormat.Jpeg;

                        // 保存当前的视图
                        chartBrittleness.SaveImage(sfd.FileName, format);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnReadExcel_Click(object sender, EventArgs e)
        {
            if (mineralData == null)
            {
                MessageBox.Show("请先选择文件。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            AutoDetectColumns();
        }

        private void AutoDetectColumns()
        {
            try
            {
                if (mineralData == null)
                {
                    MessageBox.Show("数据为空，请先导入数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 开始自动检测列
                DetectColumnsPosition(mineralData);

                var cleanData = ProcessRawData(mineralData);

                dgvMineralData.DataSource = cleanData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"自动检测失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ManualDetectButton_Click(object sender, EventArgs e)
        {
            if (mineralData == null)
            {
                MessageBox.Show("请先选择文件。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var manualDetectForm = new ManualDetectForm(mineralData);
            manualDetectForm.FormClosed += ManualDetectForm_FormClosed;
            manualDetectForm.ShowDialog();
        }

        private void ManualDetectForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            var manualDetectForm = (ManualDetectForm)sender;
            if (manualDetectForm.SelectedColumns != null)
            {
                var cleanData = new DataTable();
                cleanData.Columns.Add("顶深/m", typeof(double));
                cleanData.Columns.Add("底深/m", typeof(double));
                cleanData.Columns.Add("脆性指数", typeof(double));

                int startRow = 1; // 假设从第二行开始是数据
                for (int i = startRow; i < mineralData.Rows.Count; i++)
                {
                    try
                    {
                        double top = ParseCell(mineralData.Rows[i][manualDetectForm.SelectedColumns["顶深"]]);
                        double bottom = ParseCell(mineralData.Rows[i][manualDetectForm.SelectedColumns["底深"]]);
                        double brittle = ParseCell(mineralData.Rows[i][manualDetectForm.SelectedColumns["脆性"]]);

                        if (IsValidData(top, bottom, brittle))
                        {
                            cleanData.Rows.Add(top, bottom, brittle);
                        }
                    }
                    catch
                    {
                        // 跳过无效行
                    }
                }

                dgvMineralData.DataSource = cleanData;
            }
        }

        private bool IsValidData(double top, double bottom, double brittle)
        {
            bool isValid = !double.IsNaN(top) &&
                          !double.IsNaN(bottom) &&
                          !double.IsNaN(brittle) &&
                          top < bottom &&
                          brittle >= 0 &&
                          brittle <= 100;

            if (!isValid)
            {
                string reason = "";
                if (double.IsNaN(top)) reason += "顶深无效 ";
                if (double.IsNaN(bottom)) reason += "底深无效 ";
                if (double.IsNaN(brittle)) reason += "脆性指数无效 ";
                if (top >= bottom) reason += "顶深不小于底深 ";
                if (brittle < 0 || brittle > 100) reason += "脆性指数超出范围 ";

                // 数据验证失败
            }

            return isValid;
        }

        private double ParseCell(object cellValue)
        {
            if (cellValue == DBNull.Value || string.IsNullOrWhiteSpace(cellValue.ToString()))
            {
                return double.NaN;
            }

            string strValue = cellValue.ToString().Trim();

            // 尝试直接解析为数字
            if (double.TryParse(strValue, out double result))
            {
                if (double.IsInfinity(result) || double.IsNaN(result))
                {
                    return double.NaN;
                }
                return result;
            }

            // 检查是否是Excel公式（如AU5+AT5+AS5+AR5+AQ5）
            if (IsExcelFormula(strValue))
            {
                // 尝试计算公式结果
                double formulaResult = EvaluateExcelFormula(strValue);
                if (!double.IsNaN(formulaResult))
                {
                    return formulaResult;
                }
            }

            return double.NaN;
        }

        // 检查字符串是否是Excel公式
        private bool IsExcelFormula(string value)
        {
            // 检查是否包含Excel单元格引用模式（如A1、B2等）和运算符
            return Regex.IsMatch(value, @"[A-Z]+\d+[\+\-\*\/][A-Z]+\d+");
        }

        // 计算Excel公式的值
        private double EvaluateExcelFormula(string formula)
        {
            try
            {
                // 分解公式（例如：AU5+AT5+AS5+AR5+AQ5）
                string[] parts = Regex.Split(formula, @"(\+|\-|\*|\/)");
                if (parts.Length < 3) // 至少需要两个操作数和一个运算符
                {
                    return double.NaN;
                }

                // 提取所有数值
                List<double> values = new List<double>();
                List<string> operators = new List<string>();

                for (int i = 0; i < parts.Length; i++)
                {
                    string part = parts[i].Trim();
                    if (string.IsNullOrEmpty(part)) continue;

                    // 如果是运算符，添加到运算符列表
                    if (part == "+" || part == "-" || part == "*" || part == "/")
                    {
                        operators.Add(part);
                    }
                    // 否则尝试获取单元格引用的值
                    else if (Regex.IsMatch(part, @"^[A-Z]+\d+$"))
                    {
                        // 这里我们假设单元格引用的值已经在Excel中计算好了
                        // 由于我们无法直接访问Excel单元格的值，我们假设每个单元格引用的值为0
                        // 在实际应用中，这里应该从Excel文件中获取对应单元格的值
                        values.Add(0);
                    }
                    // 尝试直接解析为数字
                    else if (double.TryParse(part, out double value))
                    {
                        values.Add(value);
                    }
                    else
                    {
                        // 无法解析的部分，返回NaN
                        return double.NaN;
                    }
                }

                // 如果值的数量不等于运算符数量+1，则公式无效
                if (values.Count != operators.Count + 1)
                {
                    return double.NaN;
                }

                // 计算公式结果
                double result = values[0];
                for (int i = 0; i < operators.Count; i++)
                {
                    switch (operators[i])
                    {
                        case "+":
                            result += values[i + 1];
                            break;
                        case "-":
                            result -= values[i + 1];
                            break;
                        case "*":
                            result *= values[i + 1];
                            break;
                        case "/":
                            if (values[i + 1] == 0) return double.NaN; // 避免除以零
                            result /= values[i + 1];
                            break;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算公式时出错: {ex.Message}", "调试", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return double.NaN;
            }
        }

        private void DetectColumnsPosition(DataTable table)
        {
            columnPositions.Clear();

            for (int row = 0; row < Math.Min(config.MaxHeaderSearchRows, table.Rows.Count); row++)
            {
                for (int col = 0; col < table.Columns.Count; col++)
                {
                    string cellValue = table.Rows[row][col].ToString().CleanString();

                    foreach (var keyword in config.RequiredColumns)
                    {
                        if (cellValue.Contains(keyword.CleanString()))
                        {
                            if (!columnPositions.ContainsKey(keyword))
                            {
                                columnPositions.Add(keyword, (row, col));
                            }
                        }
                    }
                }
            }

            var missing = config.RequiredColumns.Where(c => !columnPositions.ContainsKey(c)).ToList();
            if (missing.Any())
                throw new Exception($"缺少必要列头：{string.Join(",", missing)}");
        }

        private DataTable ProcessRawData(DataTable rawData)
        {
            var cleanData = new DataTable();
            cleanData.Columns.Add("顶深/m", typeof(double));
            cleanData.Columns.Add("底深/m", typeof(double));
            cleanData.Columns.Add("脆性指数", typeof(double));

            int startRow = columnPositions.Values.Max(p => p.row) + 1;
            List<double> topDepths = new List<double>();
            int validRows = 0;
            int invalidRows = 0;


            for (int i = startRow; i < rawData.Rows.Count; i++)
            {
                try
                {
                    var row = rawData.Rows[i];

                    double top = ParseCell(row[columnPositions["顶深"].col]);
                    double bottom = ParseCell(row[columnPositions["底深"].col]);
                    double brittle = ParseCell(row[columnPositions["脆性"].col]);

                    if (IsValidData(top, bottom, brittle))
                    {
                        cleanData.Rows.Add(top, bottom, brittle);
                        topDepths.Add(top);
                        validRows++;
                    }
                    else
                    {
                        invalidRows++;
                    }
                }
                catch (Exception ex)
                {
                    // 处理行时出错
                    invalidRows++;
                }
            }


            return cleanData;
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            if (dgvMineralData.DataSource == null || dgvMineralData.Rows.Count == 0)
            {
                MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            using (SaveFileDialog sfd = new SaveFileDialog())
            {
                sfd.Filter = "Excel文件|*.xlsx";
                sfd.Title = "导出Excel文件";
                sfd.FileName = "脆性指数数据_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");

                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        // 创建工作簿
                        var workbook = new XSSFWorkbook();
                        var sheet = workbook.CreateSheet("脆性指数数据");

                        // 写入列头
                        var headerRow = sheet.CreateRow(0);
                        for (int i = 0; i < dgvMineralData.Columns.Count; i++)
                        {
                            headerRow.CreateCell(i).SetCellValue(dgvMineralData.Columns[i].HeaderText);
                        }

                        // 写入数据
                        for (int i = 0; i < dgvMineralData.Rows.Count; i++)
                        {
                            var row = sheet.CreateRow(i + 1);
                            for (int j = 0; j < dgvMineralData.Columns.Count; j++)
                            {
                                var value = dgvMineralData.Rows[i].Cells[j].Value?.ToString() ?? "";
                                row.CreateCell(j).SetCellValue(value);
                            }
                        }

                        // 自动调整列宽
                        for (int i = 0; i < dgvMineralData.Columns.Count; i++)
                        {
                            sheet.AutoSizeColumn(i);
                        }

                        // 保存文件
                        using (var fs = new FileStream(sfd.FileName, FileMode.Create, FileAccess.Write))
                        {
                            workbook.Write(fs);
                        }

                        MessageBox.Show("数据导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void DataLoader_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                e.Result = ReadExcelSheets(e.Argument.ToString());
            }
            catch (Exception ex)
            {
                e.Result = ex;
            }
        }

        private void DataLoader_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => DataLoader_RunWorkerCompleted(sender, e)));
                return;
            }

            if (e.Error != null)
            {
                MessageBox.Show($"读取错误: {e.Error.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (e.Result is DataSet ds)
            {
                mineralData = ds.Tables[0]; // 假设取第一个工作表
                MessageBox.Show("数据加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else if (e.Result is Exception ex)
            {
                MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataSet ReadExcelSheets(string path)
        {
            var dataSet = new DataSet();

            using (var fs = new FileStream(path, FileMode.Open, FileAccess.Read))
            {
                var workbook = new XSSFWorkbook(fs);
                var evaluator = workbook.GetCreationHelper().CreateFormulaEvaluator();

                for (int i = 0; i < workbook.NumberOfSheets; i++)
                {
                    var sheet = workbook.GetSheetAt(i);
                    var dataTable = new DataTable(sheet.SheetName);

                    // 读取表头
                    var headerRow = sheet.GetRow(0);
                    if (headerRow != null)
                    {
                        for (int col = 0; col < headerRow.LastCellNum; col++)
                        {
                            var cell = headerRow.GetCell(col);
                            dataTable.Columns.Add(cell?.ToString() ?? $"Column{col + 1}");
                        }
                    }

                    // 读取数据
                    for (int row = 1; row <= sheet.LastRowNum; row++)
                    {
                        var dataRow = sheet.GetRow(row);
                        if (dataRow != null)
                        {
                            var newRow = dataTable.NewRow();
                            for (int col = 0; col < dataTable.Columns.Count; col++)
                            {
                                var cell = dataRow.GetCell(col);
                                if (cell != null)
                                {
                                    // 根据单元格类型处理
                                    switch (cell.CellType)
                                    {
                                        case CellType.Formula:
                                            try
                                            {
                                                // 尝试计算公式
                                                var evaluatedCell = evaluator.Evaluate(cell);
                                                if (evaluatedCell != null)
                                                {
                                                    switch (evaluatedCell.CellType)
                                                    {
                                                        case CellType.Numeric:
                                                            newRow[col] = evaluatedCell.NumberValue.ToString();
                                                            break;
                                                        case CellType.String:
                                                            newRow[col] = evaluatedCell.StringValue;
                                                            break;
                                                        case CellType.Boolean:
                                                            newRow[col] = evaluatedCell.BooleanValue.ToString();
                                                            break;
                                                        default:
                                                            // 如果无法计算，则保留原始公式
                                                            newRow[col] = cell.CellFormula;
                                                            break;
                                                    }
                                                }
                                                else
                                                {
                                                    // 如果无法计算，则保留原始公式
                                                    newRow[col] = cell.CellFormula;
                                                }
                                            }
                                            catch
                                            {
                                                // 如果计算出错，则保留原始公式
                                                newRow[col] = cell.CellFormula;
                                            }
                                            break;
                                        case CellType.Numeric:
                                            newRow[col] = cell.NumericCellValue.ToString();
                                            break;
                                        case CellType.String:
                                            newRow[col] = cell.StringCellValue;
                                            break;
                                        case CellType.Boolean:
                                            newRow[col] = cell.BooleanCellValue.ToString();
                                            break;
                                        default:
                                            newRow[col] = cell.ToString() ?? "";
                                            break;
                                    }
                                }
                                else
                                {
                                    newRow[col] = "";
                                }
                            }
                            dataTable.Rows.Add(newRow);
                        }
                    }

                    dataSet.Tables.Add(dataTable);
                }
            }

            return dataSet;
        }
