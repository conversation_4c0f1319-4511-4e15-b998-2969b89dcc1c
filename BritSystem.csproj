<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- 禁用默认项目项 -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <!-- 禁用自动生成资源名称 -->
    <GenerateResourceMSBuildArchitecture>CurrentArchitecture</GenerateResourceMSBuildArchitecture>
    <GenerateResourceMSBuildRuntime>CurrentRuntime</GenerateResourceMSBuildRuntime>
    <!-- 强制使用显式指定的ManifestResourceName -->
    <EmbeddedResourceUseDependentUponConvention>false</EmbeddedResourceUseDependentUponConvention>
    <!-- 高DPI支持设置 -->
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>
  </PropertyGroup>

  <!-- 包引用 -->
  <ItemGroup>
    <PackageReference Include="AntDesign" Version="1.3.2" />
    <PackageReference Include="AntDesign.Charts" Version="0.6.1" />
    <PackageReference Include="AntDesign.ProLayout" Version="1.3.1" />
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="EPPlus" Version="8.0.1" />
    <PackageReference Include="HIC.System.Windows.Forms.DataVisualization" Version="1.0.1" />
    <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.1774.30" />
    <PackageReference Include="Microsoft.Web.WebView2.DevToolsProtocolExtension" Version="1.0.824" />
    <PackageReference Include="System.Data.OleDb" Version="9.0.4" />
  </ItemGroup>

  <!-- 源代码文件 -->
  <ItemGroup>
    <!-- 核心类 -->
    <Compile Include="BritIndexAnalysisForm.cs" />
    <Compile Include="Core\AlgorithmFormulaCal.cs" />
    <Compile Include="Core\AlgorithmFormulaCal.Designer.cs" />
    <Compile Include="Core\BrittlenessCalculator.cs" />
    <Compile Include="Core\ColumnDetector.cs" />
    <Compile Include="Core\DataManager.cs" />

    <!-- 模型类 -->
    <Compile Include="Models\BrittlenessDataPoint.cs" />
    <Compile Include="Models\MineralData.cs" />
    <Compile Include="Models\CalculationResult.cs" />

    <!-- 服务类 -->
    <Compile Include="Services\BrittlenessCalculationService.cs" />
    <Compile Include="Services\ExportService.cs" />
    <Compile Include="Services\ImportService.cs" />
    <Compile Include="Services\LoggingService.cs" />

    <!-- 辅助类 -->
    <Compile Include="Helpers\VisualizationHelper.cs" />

    <!-- 控件类 -->
    <Compile Include="Controls\MineralStackedBarChartControl.cs" />

    <!-- 非窗体类 -->
    <Compile Include="ExcelFormulaParser.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="BritSystemApp.cs" />
    <Compile Include="DetectColumnsPositionNew.cs" />
    <Compile Include="ClayMineralColumnDetector.cs" />
    <Compile Include="VisualizationForm.cs" />
    <Compile Include="AppConfig.cs" />

    <!-- 窗体类 -->
    <Compile Include="MineralogicalForm.cs" />
    <Compile Include="MineralogicalForm.Designer.cs" />
    <Compile Include="MineralInputForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ManualDetectForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ManualDetectForm.Designer.cs">
      <DependentUpon>ManualDetectForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ManualMappingForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MineralogicalAnalysisForm.cs">
      <SubType>Form</SubType>
    </Compile>

    <!-- 修复文件 -->
    <Compile Include="IsTOCFix.cs" />

    <!-- 日志文件 -->
    <Compile Include="LogDataManager.cs" />
    <Compile Include="CreateLogData.cs" />
  </ItemGroup>

  <!-- 资源文件 -->
  <ItemGroup>
    <EmbeddedResource Include="BritIndexAnalysisForm.resx" />
    <EmbeddedResource Include="Controls\MineralStackedBarChartControl.resx" />
    <EmbeddedResource Include="Core\AlgorithmFormulaCal.resx" />
    <EmbeddedResource Include="DashboardForm.resx" />
    <EmbeddedResource Include="LoginForm.resx" />
    <EmbeddedResource Include="ManualDetectForm.resx">
      <DependentUpon>ManualDetectForm.cs</DependentUpon>
      <LogicalName>BritSystem.UI.ManualDetectForm.resources</LogicalName>
      <ManifestResourceName>BritSystem.UI.ManualDetectForm</ManifestResourceName>
    </EmbeddedResource>
    <EmbeddedResource Include="DetectColumnsPositionNew.resx">
      <LogicalName>BritSystem.Resources.DetectColumnsPositionNew.resources</LogicalName>
      <ManifestResourceName>BritSystem.Resources.DetectColumnsPositionNew</ManifestResourceName>
    </EmbeddedResource>
    <EmbeddedResource Include="ManualMappingForm.resx" />
    <EmbeddedResource Include="MineralInputForm.resx" />
    <EmbeddedResource Include="MineralogicalAnalysisForm.resx" />
    <EmbeddedResource Include="MineralogicalForm.resx" />
    <EmbeddedResource Include="MineralogicalFormVisualization.resx" />
    <EmbeddedResource Include="VisualizationForm.resx" />
  </ItemGroup>

  <!-- HTML/CSS文件 -->
  <ItemGroup>
    <None Include="index.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="style.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="dashboard.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="dashboard.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="mineralogical.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="mineralogical.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="SimpleTest.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <!-- WebView2固定版本 -->
  <ItemGroup>
    <None Include="WebView2FixedVersion\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
