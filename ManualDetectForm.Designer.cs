using System;
using System.Windows.Forms;
using System.Drawing;

namespace BritSystem
{
    public partial class ManualDetectForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        // 控件声明
        private Button btnBrittlenessAlgorithm;
        private Button btnConfirm;
        private Label lblInstructions;
        private Label lblTopDepth;
        private Label lblBottomDepth;
        private Label lblBrittleIndex;
        private Button btnTopDepth;
        private Button btnBottomDepth;
        private Button btnBrittleIndex;
        private DataGridView dataGridView;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnBrittlenessAlgorithm = new Button();
            this.btnConfirm = new Button();
            this.lblInstructions = new Label();
            this.lblTopDepth = new Label();
            this.lblBottomDepth = new Label();
            this.lblBrittleIndex = new Label();
            this.btnTopDepth = new Button();
            this.btnBottomDepth = new Button();
            this.btnBrittleIndex = new Button();
            this.dataGridView = new DataGridView();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).BeginInit();
            this.SuspendLayout();

            // 设置窗体属性
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Text = "手动识别脆性指数";
            this.StartPosition = FormStartPosition.CenterScreen;

            // 初始化标签
            this.lblInstructions = new Label();
            this.lblTopDepth = new Label();
            this.lblBottomDepth = new Label();
            this.lblBrittleIndex = new Label();

            // 设置标签属性
            this.lblTopDepth.Text = "未选择";
            this.lblTopDepth.AutoSize = true;
            this.lblTopDepth.Location = new Point(170, 50);
            this.lblTopDepth.ForeColor = Color.Red;

            this.lblBottomDepth.Text = "未选择";
            this.lblBottomDepth.AutoSize = true;
            this.lblBottomDepth.Location = new Point(170, 80);
            this.lblBottomDepth.ForeColor = Color.Red;

            this.lblBrittleIndex.Text = "未选择";
            this.lblBrittleIndex.AutoSize = true;
            this.lblBrittleIndex.Location = new Point(170, 110);
            this.lblBrittleIndex.ForeColor = Color.Red;

            // 初始化选择按钮
            this.btnTopDepth = new Button();
            this.btnBottomDepth = new Button();
            this.btnBrittleIndex = new Button();

            // 设置选择按钮属性
            this.btnTopDepth.Text = "选择顶深列";
            this.btnTopDepth.Size = new Size(150, 30);
            this.btnTopDepth.Location = new Point(10, 50);

            this.btnBottomDepth.Text = "选择底深列";
            this.btnBottomDepth.Size = new Size(150, 30);
            this.btnBottomDepth.Location = new Point(10, 80);

            this.btnBrittleIndex.Text = "选择脆性指数列";
            this.btnBrittleIndex.Size = new Size(150, 30);
            this.btnBrittleIndex.Location = new Point(10, 110);

            //
            // btnBrittlenessAlgorithm
            //
            this.btnBrittlenessAlgorithm.Name = "btnBrittlenessAlgorithm";
            this.btnBrittlenessAlgorithm.Text = "脆性指数算法";
            this.btnBrittlenessAlgorithm.Size = new Size(150, 35);
            this.btnBrittlenessAlgorithm.Location = new Point(10, 10);
            this.btnBrittlenessAlgorithm.BackColor = Color.FromArgb(0, 122, 204);
            this.btnBrittlenessAlgorithm.ForeColor = Color.White;
            this.btnBrittlenessAlgorithm.FlatStyle = FlatStyle.Flat;
            this.btnBrittlenessAlgorithm.FlatAppearance.BorderSize = 0;
            this.btnBrittlenessAlgorithm.Click += new EventHandler(btnBrittlenessAlgorithm_Click);
            this.Controls.Add(this.btnBrittlenessAlgorithm);

            //
            // btnConfirm
            //
            this.btnConfirm.Name = "btnConfirm";
            this.btnConfirm.Text = "确定";
            this.btnConfirm.Size = new Size(150, 35);
            this.btnConfirm.Location = new Point(170, 10);
            this.btnConfirm.BackColor = Color.Green;
            this.btnConfirm.ForeColor = Color.White;
            this.btnConfirm.FlatStyle = FlatStyle.Flat;
            this.btnConfirm.FlatAppearance.BorderSize = 0;
            this.btnConfirm.Click += new EventHandler(BtnConfirm_Click);
            this.Controls.Add(this.btnConfirm);

            // 添加所有控件到窗体
            this.Controls.Add(this.lblTopDepth);
            this.Controls.Add(this.lblBottomDepth);
            this.Controls.Add(this.lblBrittleIndex);
            this.Controls.Add(this.btnTopDepth);
            this.Controls.Add(this.btnBottomDepth);
            this.Controls.Add(this.btnBrittleIndex);

            ((System.ComponentModel.ISupportInitialize)(this.dataGridView)).EndInit();
            this.ResumeLayout(false);
        }

        // 注释掉重复的BtnConfirm_Click方法，因为已在ManualDetectForm.cs中定义
        // private void BtnConfirm_Click(object sender, EventArgs e)
        // {
        //     // 确认按钮点击事件
        //     this.DialogResult = DialogResult.OK;
        //     this.Close();
        // }

        #endregion
    }
}
