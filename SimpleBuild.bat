@echo off
echo 矿物脆性指数分析系统 - 简易打包工具
echo ===============================
echo.

REM 设置错误处理
setlocal enabledelayedexpansion
set "ERROR_OCCURRED=0"

REM 创建输出目录
if not exist "Output" mkdir "Output"

REM 查找 Inno Setup 编译器
set "ISCC_PATH="

if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
    echo 找到 Inno Setup 6 (32位)
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
    echo 找到 Inno Setup 6 (64位)
) else if exist "%ProgramFiles(x86)%\Inno Setup 5\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 5\ISCC.exe"
    echo 找到 Inno Setup 5 (32位)
) else if exist "%ProgramFiles%\Inno Setup 5\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles%\Inno Setup 5\ISCC.exe"
    echo 找到 Inno Setup 5 (64位)
)

if "%ISCC_PATH%"=="" (
    echo 未找到 Inno Setup。请安装 Inno Setup 后再试。
    echo 下载地址: https://jrsoftware.org/isdl.php
    set "ERROR_OCCURRED=1"
    goto :end
)

REM 检查 Release 构建是否存在
set "RELEASE_PATH=bin\Release\net8.0-windows"
set "EXE_PATH=%RELEASE_PATH%\BritSystem.exe"

echo 检查文件: %EXE_PATH%
if not exist "%EXE_PATH%" (
    echo 警告: Release 构建未找到。
    echo 正在构建项目...

    REM 尝试构建项目
    dotnet build -c Release

    if %ERRORLEVEL% NEQ 0 (
        echo 构建失败。请检查错误信息。
        set "ERROR_OCCURRED=1"
        goto :end
    )

    if not exist "%EXE_PATH%" (
        echo 构建完成，但可执行文件未找到。

        REM 尝试发布项目
        echo 尝试发布项目...
        dotnet publish -c Release

        if %ERRORLEVEL% NEQ 0 (
            echo 发布失败。请检查错误信息。
            set "ERROR_OCCURRED=1"
            goto :end
        )

        if not exist "%EXE_PATH%" (
            echo 发布完成，但可执行文件仍未找到。
            echo 请手动检查以下路径是否存在:
            echo 1. %RELEASE_PATH%\BritSystem.exe
            echo 2. %RELEASE_PATH%\publish\BritSystem.exe
            echo 3. %CD%\%RELEASE_PATH%\BritSystem.exe

            REM 尝试查找可执行文件
            echo 尝试查找可执行文件...
            if exist "%RELEASE_PATH%\publish\BritSystem.exe" (
                echo 找到可执行文件: %RELEASE_PATH%\publish\BritSystem.exe
                set "EXE_PATH=%RELEASE_PATH%\publish\BritSystem.exe"
                set "SourcePath=%RELEASE_PATH%\publish"
            ) else (
                echo 无法找到可执行文件。
                set "ERROR_OCCURRED=1"
                goto :end
            )
        )
    )

    echo 构建成功。
) else (
    echo 找到可执行文件: %EXE_PATH%
)

REM 创建临时 ISS 文件
echo 创建临时 ISS 文件...
set "TEMP_ISS=Output\temp_setup.iss"

echo #define MyAppName "矿物脆性指数分析系统" > "%TEMP_ISS%"
echo #define MyAppVersion "1.1" >> "%TEMP_ISS%"
echo #define MyAppPublisher "脆性指数系统开发团队" >> "%TEMP_ISS%"
echo #define MyAppExeName "BritSystem.exe" >> "%TEMP_ISS%"
echo. >> "%TEMP_ISS%"
echo [Setup] >> "%TEMP_ISS%"
echo AppId={{A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890} >> "%TEMP_ISS%"
echo AppName={#MyAppName} >> "%TEMP_ISS%"
echo AppVersion={#MyAppVersion} >> "%TEMP_ISS%"
echo AppPublisher={#MyAppPublisher} >> "%TEMP_ISS%"
echo DefaultDirName={autopf}\{#MyAppName} >> "%TEMP_ISS%"
echo DisableProgramGroupPage=yes >> "%TEMP_ISS%"
echo OutputDir=.\Output >> "%TEMP_ISS%"
echo OutputBaseFilename=BritSystemSetup >> "%TEMP_ISS%"
echo Compression=lzma >> "%TEMP_ISS%"
echo SolidCompression=yes >> "%TEMP_ISS%"
echo WizardStyle=modern >> "%TEMP_ISS%"
echo DefaultDialogFontName=Microsoft YaHei UI >> "%TEMP_ISS%"
echo. >> "%TEMP_ISS%"
echo [Languages] >> "%TEMP_ISS%"
echo Name: "english"; MessagesFile: "compiler:Default.isl" >> "%TEMP_ISS%"
echo. >> "%TEMP_ISS%"
echo [Messages] >> "%TEMP_ISS%"
echo SetupAppTitle=安装 {#MyAppName} >> "%TEMP_ISS%"
echo SetupWindowTitle=安装 - {#MyAppName} >> "%TEMP_ISS%"
echo. >> "%TEMP_ISS%"
echo [CustomMessages] >> "%TEMP_ISS%"
echo CreateDesktopIcon=创建桌面图标 >> "%TEMP_ISS%"
echo LaunchProgram=启动 {#MyAppName} >> "%TEMP_ISS%"
echo AdditionalIcons=附加图标: >> "%TEMP_ISS%"
echo. >> "%TEMP_ISS%"
echo [Tasks] >> "%TEMP_ISS%"
echo Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked >> "%TEMP_ISS%"
echo. >> "%TEMP_ISS%"
echo [Files] >> "%TEMP_ISS%"
echo Source: "%CD%\%RELEASE_PATH%\BritSystem.exe"; DestDir: "{app}"; Flags: ignoreversion >> "%TEMP_ISS%"
echo Source: "%CD%\%RELEASE_PATH%\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs >> "%TEMP_ISS%"
echo. >> "%TEMP_ISS%"
echo [Icons] >> "%TEMP_ISS%"
echo Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}" >> "%TEMP_ISS%"
echo Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon >> "%TEMP_ISS%"
echo. >> "%TEMP_ISS%"
echo [Run] >> "%TEMP_ISS%"
echo Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent >> "%TEMP_ISS%"

REM 编译安装程序
echo 正在编译安装程序...
echo 使用临时 ISS 文件: %TEMP_ISS%

REM 运行 Inno Setup 编译器
"%ISCC_PATH%" "%TEMP_ISS%"

REM 检查结果
if %ERRORLEVEL% EQU 0 (
    echo 构建成功!
    if exist "Output\BritSystemSetup.exe" (
        echo 安装程序位置: %CD%\Output\BritSystemSetup.exe

        REM 打开输出目录
        start "" "Output"
    ) else (
        echo 警告: 安装程序未在预期位置找到。
        echo 预期路径: %CD%\Output\BritSystemSetup.exe
        set "ERROR_OCCURRED=1"
    )
) else (
    echo 构建失败。请检查错误信息。
    echo.
    echo 常见问题:
    echo 1. 源文件未找到 - 确保您已在 Release 模式下构建项目
    echo 2. 权限问题 - 确保您有写入输出目录的权限
    echo 3. 路径问题 - 避免路径中包含特殊字符或空格
    echo 4. 中文路径问题 - 避免路径中包含中文字符
    set "ERROR_OCCURRED=1"
)

REM 清理临时文件
if exist "%TEMP_ISS%" del "%TEMP_ISS%"

:end
echo.
echo 打包过程完成。

if %ERROR_OCCURRED% NEQ 0 (
    echo 在执行过程中遇到了错误，请查看上面的错误信息。
)

echo 按任意键退出...
pause > nul
