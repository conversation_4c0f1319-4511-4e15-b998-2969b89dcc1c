// 从截图中添加示例数据
        private void AddSampleDataFromScreenshot()
        {
            try
            {
                // 确保数据表已创建
                if (_sourceData == null)
                {
                    _sourceData = new DataTable();
                }
                
                // 如果没有列，添加必要的列
                if (_sourceData.Columns.Count == 0)
                {
                    _sourceData.Columns.Add("黏土矿物总量%", typeof(double));
                    _sourceData.Columns.Add("石英%", typeof(double));
                    _sourceData.Columns.Add("白云石%", typeof(double));
                    _sourceData.Columns.Add("菱铁矿%", typeof(double));
                    _sourceData.Columns.Add("斜长石%", typeof(double));
                    _sourceData.Columns.Add("钾长石（正长石）%", typeof(double));
                    _sourceData.Columns.Add("脆性矿物含量/%", typeof(double));
                    _sourceData.Columns.Add("高岭石(K)", typeof(double));
                    _sourceData.Columns.Add("绿泥石(C)", typeof(double));
                    _sourceData.Columns.Add("伊利石(It)", typeof(double));
                    _sourceData.Columns.Add("伊/蒙间层矿物(I/S)", typeof(double));
                }
                
                // 添加截图中的数据
                DataRow row1 = _sourceData.NewRow();
                row1["黏土矿物总量%"] = 38.47;
                row1["石英%"] = 35.01;
                row1["白云石%"] = 0;
                row1["菱铁矿%"] = 0;
                row1["斜长石%"] = 19.25;
                row1["钾长石（正长石）%"] = 5.96;
                row1["脆性矿物含量/%"] = 60.22;
                row1["高岭石(K)"] = 5;
                row1["绿泥石(C)"] = 23;
                _sourceData.Rows.Add(row1);
                
                DataRow row2 = _sourceData.NewRow();
                row2["黏土矿物总量%"] = 31.44;
                row2["石英%"] = 31.26;
                row2["白云石%"] = 0;
                row2["菱铁矿%"] = 0;
                row2["斜长石%"] = 29.05;
                row2["钾长石（正长石）%"] = 8.25;
                row2["脆性矿物含量/%"] = 68.56;
                row2["高岭石(K)"] = 4;
                row2["绿泥石(C)"] = 12;
                _sourceData.Rows.Add(row2);
                
                DataRow row3 = _sourceData.NewRow();
                row3["黏土矿物总量%"] = 29;
                row3["石英%"] = 32.34;
                row3["白云石%"] = 11.06;
                row3["菱铁矿%"] = 0;
                row3["斜长石%"] = 11.1;
                row3["钾长石（正长石）%"] = 6.49;
                row3["脆性矿物含量/%"] = 60.99;
                row3["高岭石(K)"] = 3;
                row3["绿泥石(C)"] = 11;
                _sourceData.Rows.Add(row3);
                
                DataRow row4 = _sourceData.NewRow();
                row4["黏土矿物总量%"] = 40.7;
                row4["石英%"] = 31.42;
                row4["白云石%"] = 0;
                row4["菱铁矿%"] = 0;
                row4["斜长石%"] = 20.91;
                row4["钾长石（正长石）%"] = 6.97;
                row4["脆性矿物含量/%"] = 59.3;
                row4["高岭石(K)"] = 3;
                row4["绿泥石(C)"] = 11;
                _sourceData.Rows.Add(row4);
                
                DataRow row5 = _sourceData.NewRow();
                row5["黏土矿物总量%"] = 23.27;
                row5["石英%"] = 36.96;
                row5["白云石%"] = 10.63;
                row5["菱铁矿%"] = 0;
                row5["斜长石%"] = 29.13;
                row5["钾长石（正长石）%"] = 0;
                row5["脆性矿物含量/%"] = 76.72;
                row5["高岭石(K)"] = 4;
                row5["绿泥石(C)"] = 13;
                _sourceData.Rows.Add(row5);
                
                // 自动选择脆性和塑性矿物列
                _brittleColumns.Clear();
                _ductileColumns.Clear();
                lstBrittleColumns.Items.Clear();
                lstDuctileColumns.Items.Clear();
                
                // 脆性矿物：石英、长石类
                if (_sourceData.Columns.Contains("石英%"))
                {
                    _brittleColumns.Add("石英%");
                    lstBrittleColumns.Items.Add("石英%");
                }
                if (_sourceData.Columns.Contains("斜长石%"))
                {
                    _brittleColumns.Add("斜长石%");
                    lstBrittleColumns.Items.Add("斜长石%");
                }
                if (_sourceData.Columns.Contains("钾长石（正长石）%"))
                {
                    _brittleColumns.Add("钾长石（正长石）%");
                    lstBrittleColumns.Items.Add("钾长石（正长石）%");
                }
                
                // 塑性矿物：黏土、碳酸盐类
                // 注释掉自动添加塑性矿物的代码，防止系统自动加载列名到lstDuctileColumns中
                // 用户需要手动添加塑性矿物列
                /*
                if (_sourceData.Columns.Contains("黏土矿物总量%"))
                {
                    _ductileColumns.Add("黏土矿物总量%");
                    lstDuctileColumns.Items.Add("黏土矿物总量%");
                }
                if (_sourceData.Columns.Contains("白云石%"))
                {
                    _ductileColumns.Add("白云石%");
                    lstDuctileColumns.Items.Add("白云石%");
                }
                if (_sourceData.Columns.Contains("菱铁矿%"))
                {
                    _ductileColumns.Add("菱铁矿%");
                    lstDuctileColumns.Items.Add("菱铁矿%");
                }
                */
                
                // 更新可用列列表
                LoadColumnNames();
                
                MessageBox.Show($"已成功添加 {_sourceData.Rows.Count} 行示例数据。", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加示例数据时出错: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"添加示例数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
