<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <!-- 禁用自动资源命名 -->
    <GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>
    <GenerateResourceMSBuildArchitecture>CurrentArchitecture</GenerateResourceMSBuildArchitecture>
    <GenerateResourceMSBuildRuntime>CurrentRuntime</GenerateResourceMSBuildRuntime>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AntDesign" Version="1.3.2" />
    <PackageReference Include="AntDesign.Charts" Version="0.6.1" />
    <PackageReference Include="AntDesign.ProLayout" Version="1.3.1" />
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="EPPlus" Version="8.0.1" />
    <PackageReference Include="HIC.System.Windows.Forms.DataVisualization" Version="1.0.1" />
    <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
    <PackageReference Include="System.Data.OleDb" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="DetectColumnsPositionNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DetectColumnsPositionNew.Designer.cs">
      <DependentUpon>DetectColumnsPositionNew.cs</DependentUpon>
    </Compile>
    <Compile Include="ExcelFormulaParser.cs" />
    <Compile Include="Main.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main.Designer.cs">
      <DependentUpon>Main.cs</DependentUpon>
    </Compile>
    <Compile Include="ManualDetectForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ManualDetectForm.Designer.cs">
      <DependentUpon>ManualDetectForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MineralogicalForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MineralogicalForm.Designer.cs">
      <DependentUpon>MineralogicalForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="DetectColumnsPositionNew.resx">
      <LogicalName>BritSystem.Forms.DetectColumnsPositionNew.resources</LogicalName>
      <DependentUpon>DetectColumnsPositionNew.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main.resx">
      <LogicalName>BritSystem.Forms.Main.resources</LogicalName>
      <DependentUpon>Main.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MineralogicalForm.resx">
      <LogicalName>BritSystem.Forms.MineralogicalForm.resources</LogicalName>
      <DependentUpon>MineralogicalForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>

</Project>
