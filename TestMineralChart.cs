using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using BritSystem.Controls;

namespace BritSystem
{
    /// <summary>
    /// 测试矿物堆叠柱状图控件的Y轴铺满功能
    /// </summary>
    public partial class TestMineralChart : Form
    {
        private MineralStackedBarChartControl mineralChart;

        public TestMineralChart()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "测试矿物图表Y轴铺满功能";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;

            // 创建矿物图表控件
            mineralChart = new MineralStackedBarChartControl
            {
                Dock = DockStyle.Fill
            };

            this.Controls.Add(mineralChart);

            // 创建测试数据
            CreateTestData();
        }

        private void CreateTestData()
        {
            // 创建测试数据表
            DataTable testData = new DataTable();
            testData.Columns.Add("顶深/m", typeof(double));
            testData.Columns.Add("底深/m", typeof(double));
            testData.Columns.Add("石英", typeof(double));
            testData.Columns.Add("长石", typeof(double));
            testData.Columns.Add("黏土矿物", typeof(double));
            testData.Columns.Add("方解石", typeof(double));

            // 添加更多测试数据行，覆盖更大的深度范围
            Random random = new Random();
            for (int i = 1; i <= 20; i++)
            {
                DataRow row = testData.NewRow();
                double topDepth = 700 + i * 10; // 从700米开始，每层10米
                row["顶深/m"] = topDepth;
                row["底深/m"] = topDepth + 8; // 每层厚度8米
                
                // 生成随机矿物含量，确保总和为100%
                double quartz = 20 + random.Next(20);
                double feldspar = 15 + random.Next(15);
                double clay = 30 + random.Next(20);
                double calcite = 100 - quartz - feldspar - clay;
                
                row["石英"] = quartz;
                row["长石"] = feldspar;
                row["黏土矿物"] = clay;
                row["方解石"] = Math.Max(0, calcite); // 确保不为负数
                
                testData.Rows.Add(row);
            }

            // 设置数据到控件
            mineralChart.ResultData = testData;
            mineralChart.BrittleMinerals = new System.Collections.Generic.List<string> { "石英", "长石" };
            mineralChart.DuctileMinerals = new System.Collections.Generic.List<string> { "黏土矿物", "方解石" };
        }

        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestMineralChart());
        }
    }
}
