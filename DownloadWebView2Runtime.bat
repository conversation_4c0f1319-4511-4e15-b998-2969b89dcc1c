@echo off
echo 正在下载WebView2运行时...

set DOWNLOAD_URL=https://go.microsoft.com/fwlink/p/?LinkId=2124703
set OUTPUT_DIR=WebView2FixedVersion
set OUTPUT_FILE=%OUTPUT_DIR%\MicrosoftEdgeWebview2Setup.exe

if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

echo 下载WebView2运行时到 %OUTPUT_FILE%...
powershell -Command "Invoke-WebRequest -Uri '%DOWNLOAD_URL%' -OutFile '%OUTPUT_FILE%'"

echo 下载完成。
echo 现在您可以运行 %OUTPUT_FILE% 来安装WebView2运行时。
pause
