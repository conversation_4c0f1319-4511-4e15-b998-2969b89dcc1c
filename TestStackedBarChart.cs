using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace BritSystem
{
    public partial class TestStackedBarChart : Form
    {
        private Chart chart;
        private Panel legendPanel;

        public TestStackedBarChart()
        {
            InitializeComponent();
            CreateChart();
            LoadTestData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            // 
            // TestStackedBarChart
            // 
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Name = "TestStackedBarChart";
            this.Text = "测试堆叠柱状图";
            this.Load += new System.EventHandler(this.TestStackedBarChart_Load);
            this.ResumeLayout(false);
        }

        private void CreateChart()
        {
            // 创建图表控件
            chart = new Chart();
            chart.Dock = DockStyle.Fill;
            chart.BackColor = Color.White;
            
            // 创建图例面板
            legendPanel = new Panel();
            legendPanel.Dock = DockStyle.Bottom;
            legendPanel.Height = 40;
            legendPanel.BackColor = Color.White;
            legendPanel.BorderStyle = BorderStyle.FixedSingle;
            
            // 添加控件到窗体
            this.Controls.Add(legendPanel);
            this.Controls.Add(chart);
        }

        private void LoadTestData()
        {
            // 清除现有图表
            chart.Series.Clear();
            chart.ChartAreas.Clear();
            chart.Legends.Clear();
            chart.Titles.Clear();
            
            // 添加标题
            chart.Titles.Add(new Title("矿物含量分布", Docking.Top, new Font("Microsoft YaHei", 14, FontStyle.Bold), Color.Black));
            
            // 添加图表区域
            ChartArea chartArea = new ChartArea("MainArea");
            chartArea.AxisX.Title = "矿物含量/%";
            chartArea.AxisX.TitleFont = new Font("Microsoft YaHei", 10, FontStyle.Regular);
            chartArea.AxisY.Title = "深度/m";
            chartArea.AxisY.TitleFont = new Font("Microsoft YaHei", 10, FontStyle.Regular);
            chartArea.AxisY.IsReversed = true; // 深度轴反向显示
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisX.Interval = 25; // 设置X轴间隔为25
            chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
            chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
            
            // 设置X轴刻度线向下
            chartArea.AxisX.MajorTickMark.Enabled = true;
            chartArea.AxisX.MajorTickMark.TickMarkStyle = TickMarkStyle.OutsideArea;
            chartArea.AxisX.MajorTickMark.Size = 2;
            chartArea.AxisX.MajorTickMark.LineColor = Color.Black;
            
            chart.ChartAreas.Add(chartArea);
            
            // 创建测试数据
            List<string> minerals = new List<string> { "石英", "黏土矿物" };
            List<double> depths = new List<double> { 20, 40, 60, 80, 100 };
            Dictionary<string, Color> mineralColors = new Dictionary<string, Color>
            {
                { "石英", Color.Yellow },
                { "黏土矿物", Color.Purple }
            };
            
            // 创建一个系列用于每个深度
            for (int i = 0; i < depths.Count; i++)
            {
                double depth = depths[i];
                
                // 创建一个系列
                Series series = new Series($"深度{depth}");
                series.ChartType = SeriesChartType.StackedBar;
                series.IsVisibleInLegend = false;
                
                // 添加数据点
                double quartz = 30 + i * 5; // 石英含量
                double clay = 70 - i * 5;   // 黏土含量
                
                // 添加石英数据点
                DataPoint quartzPoint = new DataPoint();
                quartzPoint.SetValueXY(quartz, depth);
                quartzPoint.Color = mineralColors["石英"];
                quartzPoint.BorderWidth = 0;
                quartzPoint.ToolTip = $"深度: {depth}m, 石英: {quartz:F2}%";
                series.Points.Add(quartzPoint);
                
                // 添加黏土数据点
                DataPoint clayPoint = new DataPoint();
                clayPoint.SetValueXY(clay, depth);
                clayPoint.Color = mineralColors["黏土矿物"];
                clayPoint.BorderWidth = 0;
                clayPoint.ToolTip = $"深度: {depth}m, 黏土矿物: {clay:F2}%";
                series.Points.Add(clayPoint);
                
                chart.Series.Add(series);
            }
            
            // 创建图例
            legendPanel.Controls.Clear();
            
            // 计算每个图例项的宽度
            int totalWidth = legendPanel.Width;
            int legendItemWidth = totalWidth / minerals.Count;
            
            // 创建图例项
            for (int i = 0; i < minerals.Count; i++)
            {
                string mineral = minerals[i];
                
                // 创建颜色方块
                Panel colorBox = new Panel();
                colorBox.BackColor = mineralColors[mineral];
                colorBox.Size = new Size(20, 20);
                colorBox.Location = new Point(i * legendItemWidth + 5, 10);
                
                // 创建标签
                Label label = new Label();
                label.Text = $"{mineral}: {(mineral == "石英" ? 35 : 65):F0}";
                label.AutoSize = true;
                label.Location = new Point(i * legendItemWidth + 30, 12);
                
                // 添加到图例面板
                legendPanel.Controls.Add(colorBox);
                legendPanel.Controls.Add(label);
            }
        }

        private void TestStackedBarChart_Load(object sender, EventArgs e)
        {
            // 窗体加载时刷新图表
            LoadTestData();
        }
        
        // 主函数入口
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestStackedBarChart());
        }
    }
}
