using System;
using System.Windows.Forms;
using System.Drawing;
using System.Collections.Generic;

namespace NativeFormTest
{
    public class MineralogicalForm : Form
    {
        private Label lblTitle;
        private Label lblWelcome;
        private Button btnBack;
        private Button btnLogout;
        private Panel pnlInput;
        private Panel pnlData;
        private DataGridView dgvMineralData;
        private TextBox txtQuartz;
        private TextBox txtFeldspar;
        private TextBox txtCarbonate;
        private TextBox txtClay;
        private Button btnCalculate;
        private Label lblResult;
        private string username;

        public MineralogicalForm(string username)
        {
            this.username = username;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            // 窗体设置
            this.Text = "脆性指数系统 - 矿物组分法";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(33, 33, 33); // 深色背景

            // 标题
            lblTitle = new Label();
            lblTitle.Text = "脆性指数系统 - 矿物组分法";
            lblTitle.Font = new Font("Microsoft YaHei", 20, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Height = 50;
            lblTitle.Padding = new Padding(0, 10, 0, 0);

            // 欢迎信息
            lblWelcome = new Label();
            lblWelcome.Text = $"欢迎, {username}";
            lblWelcome.Font = new Font("Microsoft YaHei", 12);
            lblWelcome.ForeColor = Color.White;
            lblWelcome.TextAlign = ContentAlignment.MiddleLeft;
            lblWelcome.Location = new Point(20, 60);
            lblWelcome.Size = new Size(300, 30);

            // 返回按钮
            btnBack = new Button();
            btnBack.Text = "返回仪表盘";
            btnBack.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnBack.Location = new Point(20, 100);
            btnBack.Size = new Size(150, 40);
            btnBack.BackColor = Color.FromArgb(0, 120, 212);
            btnBack.ForeColor = Color.White;
            btnBack.FlatStyle = FlatStyle.Flat;
            btnBack.Click += BtnBack_Click;

            // 退出登录按钮
            btnLogout = new Button();
            btnLogout.Text = "退出登录";
            btnLogout.Font = new Font("Microsoft YaHei", 10);
            btnLogout.Location = new Point(this.ClientSize.Width - 150 - 20, 100);
            btnLogout.Size = new Size(150, 40);
            btnLogout.BackColor = Color.FromArgb(153, 153, 153);
            btnLogout.ForeColor = Color.White;
            btnLogout.FlatStyle = FlatStyle.Flat;
            btnLogout.Click += BtnLogout_Click;

            // 输入面板
            pnlInput = new Panel();
            pnlInput.Location = new Point(20, 160);
            pnlInput.Size = new Size(this.ClientSize.Width - 40, 150);
            pnlInput.BackColor = Color.FromArgb(45, 45, 45);
            pnlInput.BorderStyle = BorderStyle.FixedSingle;

            // 添加输入控件
            Label lblInputTitle = new Label();
            lblInputTitle.Text = "输入矿物组分 (%)";
            lblInputTitle.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblInputTitle.ForeColor = Color.White;
            lblInputTitle.Location = new Point(10, 10);
            lblInputTitle.Size = new Size(200, 25);
            pnlInput.Controls.Add(lblInputTitle);

            // 石英输入
            Label lblQuartz = new Label();
            lblQuartz.Text = "石英:";
            lblQuartz.Font = new Font("Microsoft YaHei", 10);
            lblQuartz.ForeColor = Color.White;
            lblQuartz.Location = new Point(20, 50);
            lblQuartz.Size = new Size(80, 25);
            pnlInput.Controls.Add(lblQuartz);

            txtQuartz = new TextBox();
            txtQuartz.Location = new Point(100, 50);
            txtQuartz.Size = new Size(80, 25);
            txtQuartz.Font = new Font("Microsoft YaHei", 10);
            pnlInput.Controls.Add(txtQuartz);

            // 长石输入
            Label lblFeldspar = new Label();
            lblFeldspar.Text = "长石:";
            lblFeldspar.Font = new Font("Microsoft YaHei", 10);
            lblFeldspar.ForeColor = Color.White;
            lblFeldspar.Location = new Point(200, 50);
            lblFeldspar.Size = new Size(80, 25);
            pnlInput.Controls.Add(lblFeldspar);

            txtFeldspar = new TextBox();
            txtFeldspar.Location = new Point(280, 50);
            txtFeldspar.Size = new Size(80, 25);
            txtFeldspar.Font = new Font("Microsoft YaHei", 10);
            pnlInput.Controls.Add(txtFeldspar);

            // 碳酸盐输入
            Label lblCarbonate = new Label();
            lblCarbonate.Text = "碳酸盐:";
            lblCarbonate.Font = new Font("Microsoft YaHei", 10);
            lblCarbonate.ForeColor = Color.White;
            lblCarbonate.Location = new Point(380, 50);
            lblCarbonate.Size = new Size(80, 25);
            pnlInput.Controls.Add(lblCarbonate);

            txtCarbonate = new TextBox();
            txtCarbonate.Location = new Point(460, 50);
            txtCarbonate.Size = new Size(80, 25);
            txtCarbonate.Font = new Font("Microsoft YaHei", 10);
            pnlInput.Controls.Add(txtCarbonate);

            // 粘土输入
            Label lblClay = new Label();
            lblClay.Text = "粘土:";
            lblClay.Font = new Font("Microsoft YaHei", 10);
            lblClay.ForeColor = Color.White;
            lblClay.Location = new Point(560, 50);
            lblClay.Size = new Size(80, 25);
            pnlInput.Controls.Add(lblClay);

            txtClay = new TextBox();
            txtClay.Location = new Point(640, 50);
            txtClay.Size = new Size(80, 25);
            txtClay.Font = new Font("Microsoft YaHei", 10);
            pnlInput.Controls.Add(txtClay);

            // 计算按钮
            btnCalculate = new Button();
            btnCalculate.Text = "计算脆性指数";
            btnCalculate.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnCalculate.Location = new Point(20, 90);
            btnCalculate.Size = new Size(150, 35);
            btnCalculate.BackColor = Color.Cyan;
            btnCalculate.ForeColor = Color.Black;
            btnCalculate.FlatStyle = FlatStyle.Flat;
            btnCalculate.Click += BtnCalculate_Click;
            pnlInput.Controls.Add(btnCalculate);

            // 结果标签
            lblResult = new Label();
            lblResult.Text = "";
            lblResult.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblResult.ForeColor = Color.Cyan;
            lblResult.Location = new Point(200, 90);
            lblResult.Size = new Size(500, 35);
            lblResult.TextAlign = ContentAlignment.MiddleLeft;
            pnlInput.Controls.Add(lblResult);

            // 数据面板
            pnlData = new Panel();
            pnlData.Location = new Point(20, 330);
            pnlData.Size = new Size(this.ClientSize.Width - 40, this.ClientSize.Height - 350);
            pnlData.BackColor = Color.FromArgb(45, 45, 45);
            pnlData.BorderStyle = BorderStyle.FixedSingle;

            // 添加数据网格
            dgvMineralData = new DataGridView();
            dgvMineralData.Location = new Point(10, 40);
            dgvMineralData.Size = new Size(pnlData.Width - 20, pnlData.Height - 50);
            dgvMineralData.BackgroundColor = Color.FromArgb(45, 45, 45);
            dgvMineralData.ForeColor = Color.Black;
            dgvMineralData.GridColor = Color.FromArgb(60, 60, 60);
            dgvMineralData.BorderStyle = BorderStyle.None;
            dgvMineralData.RowHeadersVisible = false;
            dgvMineralData.AllowUserToAddRows = false;
            dgvMineralData.AllowUserToDeleteRows = false;
            dgvMineralData.ReadOnly = true;
            dgvMineralData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvMineralData.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(60, 60, 60);
            dgvMineralData.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvMineralData.ColumnHeadersDefaultCellStyle.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            dgvMineralData.DefaultCellStyle.BackColor = Color.FromArgb(50, 50, 50);
            dgvMineralData.DefaultCellStyle.ForeColor = Color.White;
            dgvMineralData.DefaultCellStyle.Font = new Font("Microsoft YaHei", 9);
            dgvMineralData.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(55, 55, 55);
            dgvMineralData.AlternatingRowsDefaultCellStyle.ForeColor = Color.White;
            dgvMineralData.AlternatingRowsDefaultCellStyle.Font = new Font("Microsoft YaHei", 9);
            dgvMineralData.EnableHeadersVisualStyles = false;
            pnlData.Controls.Add(dgvMineralData);

            // 添加数据标题
            Label lblDataTitle = new Label();
            lblDataTitle.Text = "矿物组分数据";
            lblDataTitle.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblDataTitle.ForeColor = Color.White;
            lblDataTitle.Location = new Point(10, 10);
            lblDataTitle.Size = new Size(200, 25);
            pnlData.Controls.Add(lblDataTitle);

            // 添加控件到窗体
            this.Controls.Add(lblTitle);
            this.Controls.Add(lblWelcome);
            this.Controls.Add(btnBack);
            this.Controls.Add(btnLogout);
            this.Controls.Add(pnlInput);
            this.Controls.Add(pnlData);

            // 窗体大小调整事件
            this.Resize += MineralogicalForm_Resize;
        }

        private void MineralogicalForm_Resize(object sender, EventArgs e)
        {
            // 调整控件位置和大小
            btnLogout.Location = new Point(this.ClientSize.Width - 150 - 20, 100);
            pnlInput.Size = new Size(this.ClientSize.Width - 40, 150);
            pnlData.Size = new Size(this.ClientSize.Width - 40, this.ClientSize.Height - 350);
            dgvMineralData.Size = new Size(pnlData.Width - 20, pnlData.Height - 50);
        }

        private void LoadData()
        {
            // 设置数据网格列
            dgvMineralData.Columns.Clear();
            dgvMineralData.Columns.Add("topDepth", "顶深度 (m)");
            dgvMineralData.Columns.Add("bottomDepth", "底深度 (m)");
            dgvMineralData.Columns.Add("quartz", "石英 (%)");
            dgvMineralData.Columns.Add("feldspar", "长石 (%)");
            dgvMineralData.Columns.Add("carbonate", "碳酸盐 (%)");
            dgvMineralData.Columns.Add("clay", "粘土 (%)");
            dgvMineralData.Columns.Add("brittleness", "脆性指数 (%)");

            // 添加示例数据
            dgvMineralData.Rows.Add(100.0, 110.0, 30.0, 20.0, 40.0, 10.0, 50.0);
            dgvMineralData.Rows.Add(110.0, 120.0, 35.0, 25.0, 30.0, 10.0, 60.0);
            dgvMineralData.Rows.Add(120.0, 130.0, 40.0, 20.0, 30.0, 10.0, 60.0);
            dgvMineralData.Rows.Add(130.0, 140.0, 45.0, 15.0, 30.0, 10.0, 60.0);
            dgvMineralData.Rows.Add(140.0, 150.0, 50.0, 10.0, 30.0, 10.0, 60.0);
        }

        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取输入值
                if (!double.TryParse(txtQuartz.Text, out double quartz) ||
                    !double.TryParse(txtFeldspar.Text, out double feldspar) ||
                    !double.TryParse(txtCarbonate.Text, out double carbonate) ||
                    !double.TryParse(txtClay.Text, out double clay))
                {
                    MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 计算脆性指数
                double brittleness = (quartz + feldspar + carbonate) / (quartz + feldspar + carbonate + clay) * 100;

                // 显示结果
                lblResult.Text = $"脆性指数: {Math.Round(brittleness, 2)}%";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算脆性指数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            // 返回仪表盘
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            // 退出登录
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
