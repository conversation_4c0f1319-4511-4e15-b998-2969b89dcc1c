#define MyAppName "矿物脆性指数分析系统"
#define MyAppVersion "1.1"
#define MyAppPublisher "脆性指数系统开发团队"
#define MyAppExeName "BritSystem.exe"

[Setup]
AppId={{A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
DefaultDirName={autopf}\{#MyAppName}
DisableProgramGroupPage=yes
OutputDir=.\Output
OutputBaseFilename=BritSystemSetup
Compression=lzma
SolidCompression=yes
WizardStyle=modern
DefaultDialogFontName=Microsoft YaHei UI

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Messages]
SetupAppTitle=安装 {#MyAppName}
SetupWindowTitle=安装 - {#MyAppName}

[CustomMessages]
CreateDesktopIcon=创建桌面图标
LaunchProgram=启动 {#MyAppName}
AdditionalIcons=附加图标:

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
; 使用绝对路径
Source: "bin\Release\net8.0-windows\publish\BritSystem.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "bin\Release\net8.0-windows\publish\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent
