﻿++解决方案 'BritSystem' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:BritSystem.sln
++BritSystem
i:{00000000-0000-0000-0000-000000000000}:BritSystem
++依赖项
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3228
++Brittleness
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\brittleness\
++Controls
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\controls\
++MineralStackedBarChartControl.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\controls\mineralstackedbarchartcontrol.cs
++Core
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core\
++AlgorithmFormulaCal.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core\algorithmformulacal.cs
++BrittlenessCalculator.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core\brittlenesscalculator.cs
++ColumnDetector.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core\columndetector.cs
++DataManager.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core\datamanager.cs
++Docs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\docs\
++用户手册.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\docs\用户手册.txt
++Helpers
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\helpers\
++VisualizationHelper.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\helpers\visualizationhelper.cs
++Images
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\images\
++Models
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\models\
++NativeFormTest
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\
++Output
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\output\
++SampleData
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\sampledata\
++Services
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\services\
++TestChart
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\
++app.manifest
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\app.manifest
++AppConfig.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\appconfig.cs
++BritIndexAnalysisForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\britindexanalysisform.cs
++BritSystem.csproj.Backup.tmp
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\britsystem.csproj.backup.tmp
++BritSystem.csproj.bak
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\britsystem.csproj.bak
++BritSystem.csproj.new
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\britsystem.csproj.new
++BritSystemApp.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\britsystemapp.cs
++BritSystemSetup.iss
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\britsystemsetup.iss
++BritSystemSetup_Fixed.iss
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\britsystemsetup_fixed.iss
++BuildInstaller.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\buildinstaller.bat
++BuildInstaller_Fixed.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\buildinstaller_fixed.bat
++ClayMineralColumnDetector.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\claymineralcolumndetector.cs
++ClayMineralLog.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\clayminerallog.txt
++CleanWebView2Data.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\cleanwebview2data.bat
++CompileSetup.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\compilesetup.bat
++core.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core.txt
++CORE1.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core1.txt
++CreateLogData.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\createlogdata.cs
++dashboard.css
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\dashboard.css
++dashboard.html
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\dashboard.html
++DashboardForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\dashboardform.cs
++DebugBuild.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\debugbuild.bat
++DetectColumnsPositionNew.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\detectcolumnspositionnew.cs
++DirectBuild.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\directbuild.bat
++DownloadWebView2Runtime.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\downloadwebview2runtime.bat
++errors.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\errors.txt
++ExcelFormulaParser.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\excelformulaparser.cs
++index.html
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\index.html
++InstallWebView2.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\installwebview2.bat
++IsTOC_method.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\istoc_method.txt
++IsTOCFix.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\istocfix.cs
++LICENSE.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\license.txt
++Log.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\log.txt
++LogDataManager.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\logdatamanager.cs
++LoginForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\loginform.cs
++ManualDetectForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\manualdetectform.cs
++ManualDetectForm.resx.bak
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\manualdetectform.resx.bak
++ManualMappingForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\manualmappingform.cs
++MineralInputForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralinputform.cs
++mineralogical.css
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralogical.css
++mineralogical.html
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralogical.html
++MineralogicalAnalysisForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralogicalanalysisform.cs
++MineralogicalForm.cs
++MineralogicalForm.resx.bak
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralogicalform.resx.bak
++MineralogicalForm.resx.old
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralogicalform.resx.old
++MineralogicalFormVisualization.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralogicalformvisualization.resx
++msbuild.log
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\msbuild.log
++NOTICE.TXT
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\users\<USER>\.nuget\packages\dotnetcore.npoi\1.2.3\contentfiles\any\netstandard2.0\notice.txt
++Program.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\program.cs
++PublishAndPackage.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\publishandpackage.bat
++README.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\readme.txt
++RunSimpleWebViewContainer.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\runsimplewebviewcontainer.bat
++RunTest.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\runtest.bat
++RunWebView2Test.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\runwebview2test.bat
++SimpleBuild.bat
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\simplebuild.bat
++SimpleSetup.iss
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\simplesetup.iss
++SimpleTest.html
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\simpletest.html
++SimpleWebView2Test.html
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\simplewebview2test.html
++StaticRockMechanicsForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\staticrockmechanicsform.cs
++style.css
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\style.css
++temp_script.ps1
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\temp_script.ps1
++UserManual.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\usermanual.txt
++VisualizationForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\visualizationform.cs
++WebViewContainer.cs.bak
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\webviewcontainer.cs.bak
++静态岩石力学参数测试数据.csv
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\静态岩石力学参数测试数据.csv
++静态岩石力学参数法使用说明.md
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\静态岩石力学参数法使用说明.md
++包
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3246
++分析器
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3229
++框架
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3243
++.gitattributes
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\brittleness\.gitattributes
++MineralStackedBarChartControl.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\controls\mineralstackedbarchartcontrol.resx
++AlgorithmFormulaCal.Designer.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core\algorithmformulacal.designer.cs
++AlgorithmFormulaCal.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\core\algorithmformulacal.resx
++Ikun.ico
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\images\ikun.ico
++Mi.ico
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\images\mi.ico
++BrittlenessDataPoint.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\models\brittlenessdatapoint.cs
++CalculationResult.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\models\calculationresult.cs
++MineralData.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\models\mineraldata.cs
++bin
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\bin\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\
++obj
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\
++BritSystemSetup.exe
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\output\britsystemsetup.exe
++示例数据说明.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\sampledata\示例数据说明.txt
++BrittlenessCalculationService.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\services\brittlenesscalculationservice.cs
++ExportService.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\services\exportservice.cs
++ImportService.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\services\importservice.cs
++LoggingService.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\services\loggingservice.cs
++BritIndexAnalysisForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\britindexanalysisform.resx
++DashboardForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\dashboardform.resx
++DetectColumnsPositionNew.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\detectcolumnspositionnew.resx
++LoginForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\loginform.resx
++ManualDetectForm.Designer.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\manualdetectform.designer.cs
++ManualDetectForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\manualdetectform.resx
++ManualMappingForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\manualmappingform.resx
++MineralInputForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralinputform.resx
++MineralogicalAnalysisForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\mineralogicalanalysisform.resx
++MineralogicalForm.Designer.cs
++MineralogicalForm.resx
++VisualizationForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\visualizationform.resx
++AntDesign (1.3.2)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3248
++AntDesign.Charts (0.6.1)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3252
++AntDesign.ProLayout (1.3.1)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3254
++DotNetCore.NPOI (1.2.3)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3253
++EPPlus (8.0.1)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3250
++HIC.System.Windows.Forms.DataVisualization (1.0.1)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3247
++Microsoft.Office.Interop.Excel (15.0.4795.1001)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3249
++Microsoft.Web.WebView2 (1.0.1774.30)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3251
++Microsoft.Web.WebView2.DevToolsProtocolExtension (1.0.824)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3256
++System.Data.OleDb (9.0.4)
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3255
++Microsoft.AspNetCore.Components.Analyzers
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\users\<USER>\.nuget\packages\microsoft.aspnetcore.components.analyzers\8.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++System.Windows.Forms.Analyzers
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\8.0.15\analyzers\dotnet\system.windows.forms.analyzers.dll
++System.Windows.Forms.Analyzers.CSharp
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\8.0.15\analyzers\dotnet\cs\system.windows.forms.analyzers.csharp.dll
++Microsoft.NETCore.App
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3244
++Microsoft.WindowsDesktop.App.WindowsForms
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:>3245
++Debug
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\bin\debug\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\
++Release
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\release\
++NativeFormTest.csproj.nuget.dgspec.json
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\nativeformtest.csproj.nuget.dgspec.json
++NativeFormTest.csproj.nuget.g.props
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\nativeformtest.csproj.nuget.g.props
++NativeFormTest.csproj.nuget.g.targets
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\nativeformtest.csproj.nuget.g.targets
++project.assets.json
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\project.assets.json
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\project.assets.json
++project.nuget.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\project.nuget.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\project.nuget.cache
++TestMineralChart.csproj.nuget.dgspec.json
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\testmineralchart.csproj.nuget.dgspec.json
++TestMineralChart.csproj.nuget.g.props
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\testmineralchart.csproj.nuget.g.props
++TestMineralChart.csproj.nuget.g.targets
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\testmineralchart.csproj.nuget.g.targets
++net6.0-windows
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\bin\debug\net6.0-windows\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\release\net6.0-windows\
++net8.0-windows
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\
++NativeFormTest.deps.json
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.deps.json
++NativeFormTest.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\ref\nativeformtest.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\refint\nativeformtest.dll
++NativeFormTest.exe
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.exe
++NativeFormTest.pdb
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.pdb
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.pdb
++NativeFormTest.runtimeconfig.json
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.runtimeconfig.json
++ref
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\ref\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\ref\
++refint
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\refint\
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\refint\
++apphost.exe
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\apphost.exe
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\apphost.exe
++NativeFormTest.AssemblyInfoInputs.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.assemblyinfoinputs.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\release\net6.0-windows\nativeformtest.assemblyinfoinputs.cache
++NativeFormTest.assets.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.assets.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\release\net6.0-windows\nativeformtest.assets.cache
++NativeFormTest.csproj.CoreCompileInputs.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.csproj.corecompileinputs.cache
++NativeFormTest.csproj.FileListAbsolute.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.csproj.filelistabsolute.txt
++NativeFormTest.GeneratedMSBuildEditorConfig.editorconfig
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.generatedmsbuildeditorconfig.editorconfig
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\release\net6.0-windows\nativeformtest.generatedmsbuildeditorconfig.editorconfig
++NativeFormTest.genruntimeconfig.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.genruntimeconfig.cache
++BritSystem.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\britsystem.dll
++BritSystem.pdb
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\britsystem.pdb
++ICSharpCode.SharpZipLib.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\icsharpcode.sharpziplib.dll
++Microsoft.Data.SqlClient.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\microsoft.data.sqlclient.dll
++Microsoft.Identity.Client.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\microsoft.identity.client.dll
++NPOI.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\npoi.dll
++NPOI.OOXML.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\npoi.ooxml.dll
++NPOI.OpenXml4Net.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\npoi.openxml4net.dll
++NPOI.OpenXmlFormats.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\npoi.openxmlformats.dll
++System.Windows.Forms.DataVisualization.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\system.windows.forms.datavisualization.dll
++TestMineralChart.deps.json
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\testmineralchart.deps.json
++TestMineralChart.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\testmineralchart.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\ref\testmineralchart.dll
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\refint\testmineralchart.dll
++TestMineralChart.exe
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\testmineralchart.exe
++TestMineralChart.pdb
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\testmineralchart.pdb
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.pdb
++TestMineralChart.runtimeconfig.json
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\bin\debug\net8.0-windows\testmineralchart.runtimeconfig.json
++TestMine.28B86F58.Up2Date
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmine.28b86f58.up2date
++TestMineralChart.AssemblyInfoInputs.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.assemblyinfoinputs.cache
++TestMineralChart.assets.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.assets.cache
++TestMineralChart.csproj.AssemblyReference.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.csproj.assemblyreference.cache
++TestMineralChart.csproj.CoreCompileInputs.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.csproj.corecompileinputs.cache
++TestMineralChart.csproj.FileListAbsolute.txt
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.csproj.filelistabsolute.txt
++TestMineralChart.GeneratedMSBuildEditorConfig.editorconfig
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.generatedmsbuildeditorconfig.editorconfig
++TestMineralChart.genruntimeconfig.cache
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\testchart\obj\debug\net8.0-windows\testmineralchart.genruntimeconfig.cache
++StaticRockMechanicsForm.resx
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\staticrockmechanicsform.resx
++StaticRockMechanicsFormR.cs
++StaticRockMechanicsFormR.Designer.cs
++StaticRockMechanicsFormR.resx
++StaticRockMechanicsFormNew.cs
++StaticRockMechanicsFormNew.Designer.cs
++StaticRockMechanicsFormNew.resx
++RealStaticRockMechanicsForm.cs
i:{20f450ba-a30f-4b65-aeb2-1d328012f078}:f:\1-work\2025\2025-5\23\britsystem\realstaticrockmechanicsform.cs
++RealStaticRockMechanicsForm.Designer.cs
++RealStaticRockMechanicsForm.resx
