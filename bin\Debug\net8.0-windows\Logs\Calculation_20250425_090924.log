﻿===== 计算按钮点击事件开始 - 2025-04-25 09:09:24 =====
源数据表: 77 行, 63 列
脆性矿物列数量: 3, 塑性矿物列数量: 1
已选择的脆性矿物列: 
  - 石英%
  - 菱铁矿%
  - 白云石%
已选择的塑性矿物列: 
  - 黏土矿物总量%
清空之前的结果数据
开始处理所有行数据，共 77 行
处理行 0...
  顶深: 0, 底深: 0
===== 计算过程中发生错误 - 2025-04-25 09:09:24 =====
错误信息: Column '顶深/m' does not belong to table .
堆栈跟踪:    at System.Data.DataRow.GetDataColumn(String columnName)
   at System.Data.DataRow.set_Item(String columnName, Object value)
   at BritSystem.AlgorithmFormulaCal.btnCalculate_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\Brittleness index system\repos\BritSystem\AlgorithmFormulaCal.cs:line 2534
===== 错误日志结束 =====
