using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using BritSystem.Models;
using BritSystem.Controls;

namespace BritSystem.Helpers
{
    /// <summary>
    /// 可视化辅助类，提供绘制各种图表的功能
    /// </summary>
    public class VisualizationHelper
    {
        #region 私有字段

        private readonly DataTable _resultData;
        private readonly List<string> _brittleColumns;
        private readonly List<string> _ductileColumns;
        private Form _visualizationForm;
        private Chart _depthVsBrittlenessChart;
        private Chart _brittleDuctileRatioChart;
        private MineralStackedBarChartControl _mineralStackedBarChart;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="resultData">结果数据</param>
        /// <param name="brittleColumns">脆性矿物列名</param>
        /// <param name="ductileColumns">塑性矿物列名</param>
        public VisualizationHelper(DataTable resultData, List<string> brittleColumns, List<string> ductileColumns)
        {
            _resultData = resultData ?? throw new ArgumentNullException(nameof(resultData));
            _brittleColumns = brittleColumns ?? new List<string>();
            _ductileColumns = ductileColumns ?? new List<string>();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 显示可视化窗口
        /// </summary>
        public void ShowVisualizationForm()
        {
            if (_resultData == null || _resultData.Rows.Count == 0)
            {
                throw new InvalidOperationException("没有数据可供可视化");
            }

            // 初始化可视化窗口
            InitializeVisualizationForm();

            // 创建图表
            CreateCharts();

            // 绘制图表
            DrawCharts();

            // 显示窗口
            _visualizationForm.ShowDialog();
        }

        /// <summary>
        /// 导出图表为图片
        /// </summary>
        /// <param name="chartType">图表类型</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">图片格式</param>
        public void ExportChartAsImage(string chartType, string filePath, ChartImageFormat format)
        {
            Chart chartToExport = null;

            // 根据图表类型选择相应的图表
            switch (chartType.ToLower())
            {
                case "depth":
                    chartToExport = _depthVsBrittlenessChart;
                    break;
                case "ratio":
                    chartToExport = _brittleDuctileRatioChart;
                    break;
                case "mineral":
                    // 对于矿物堆叠柱状图，需要特殊处理
                    using (Bitmap bitmap = new Bitmap(_mineralStackedBarChart.Width, _mineralStackedBarChart.Height))
                    {
                        _mineralStackedBarChart.DrawToBitmap(bitmap, new Rectangle(0, 0, _mineralStackedBarChart.Width, _mineralStackedBarChart.Height));
                        bitmap.Save(filePath, System.Drawing.Imaging.ImageFormat.Png);
                    }
                    return; // 直接返回，不执行后续代码
                default:
                    throw new ArgumentException($"未知的图表类型: {chartType}");
            }

            if (chartToExport != null)
            {
                try
                {
                    chartToExport.SaveImage(filePath, format);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出图表为图片时出错: {ex.Message}", "导出错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化可视化窗口
        /// </summary>
        private void InitializeVisualizationForm()
        {
            _visualizationForm = new Form
            {
                Text = "脆性指数可视化分析",
                Size = new Size(1000, 700),
                StartPosition = FormStartPosition.CenterScreen,
                MinimizeBox = true,
                MaximizeBox = true,
                FormBorderStyle = FormBorderStyle.Sizable,
                BackColor = Color.White
            };

            // 创建选项卡控件
            TabControl tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                TabPages =
                {
                    new TabPage("深度与脆性指数关系"),
                    new TabPage("脆性/塑性矿物比例"),
                    new TabPage("矿物含量分布")
                }
            };

            // 添加选项卡控件到窗口
            _visualizationForm.Controls.Add(tabControl);

            // 创建图表控件
            _depthVsBrittlenessChart = new Chart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };

            _brittleDuctileRatioChart = new Chart
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };

            // 创建矿物堆叠柱状图控件
            _mineralStackedBarChart = new MineralStackedBarChartControl
            {
                Dock = DockStyle.Fill
            };

            // 添加图表控件到选项卡
            tabControl.TabPages[0].Controls.Add(_depthVsBrittlenessChart);
            tabControl.TabPages[1].Controls.Add(_brittleDuctileRatioChart);
            tabControl.TabPages[2].Controls.Add(_mineralStackedBarChart);
        }

        /// <summary>
        /// 创建图表
        /// </summary>
        private void CreateCharts()
        {
            // 创建深度与脆性指数关系图表
            CreateDepthVsBrittlenessChart();

            // 创建脆性/塑性矿物比例图表
            CreateBrittleDuctileRatioChart();

            // 创建矿物堆叠柱状图
            CreateMineralStackedBarChart();
        }

        /// <summary>
        /// 创建深度与脆性指数关系图表
        /// </summary>
        private void CreateDepthVsBrittlenessChart()
        {
            // 初始化图表
            _depthVsBrittlenessChart.Titles.Add(new Title("深度 vs 脆性指数", Docking.Top, new Font("Arial", 14, FontStyle.Bold), Color.Black));

            // 添加图表区域
            ChartArea chartArea = new ChartArea("MainArea");
            chartArea.AxisX.Title = "深度 (m)";
            chartArea.AxisY.Title = "脆性指数 (%)";
            chartArea.AxisY.Minimum = 0;
            chartArea.AxisY.Maximum = 100; // 修改为100，因为脆性指数已乘以100
            chartArea.AxisX.IsReversed = true; // 深度从小到大向下
            chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
            chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
            chartArea.BackColor = Color.White;
            _depthVsBrittlenessChart.ChartAreas.Add(chartArea);

            // 添加数据系列
            Series series = new Series("脆性指数");
            series.ChartType = SeriesChartType.Line;
            series.MarkerStyle = MarkerStyle.Circle;
            series.MarkerSize = 6;
            series.XValueType = ChartValueType.Double;
            series.YValueType = ChartValueType.Double;
            series.Color = Color.Blue;
            _depthVsBrittlenessChart.Series.Add(series);
        }

        /// <summary>
        /// 创建脆性/塑性矿物比例图表
        /// </summary>
        private void CreateBrittleDuctileRatioChart()
        {
            // 初始化图表
            _brittleDuctileRatioChart.Titles.Add(new Title("脆性矿物 vs 塑性矿物比例", Docking.Top, new Font("Arial", 14, FontStyle.Bold), Color.Black));

            // 添加图表区域
            ChartArea chartArea = new ChartArea("MainArea");
            chartArea.BackColor = Color.White;
            _brittleDuctileRatioChart.ChartAreas.Add(chartArea);

            // 添加图例
            Legend legend = new Legend("Legend1");
            legend.Docking = Docking.Bottom;
            _brittleDuctileRatioChart.Legends.Add(legend);

            // 添加数据系列
            Series series = new Series("比例");
            series.ChartType = SeriesChartType.Pie;
            series.Label = "#PERCENT{P1}";
            series.Legend = "Legend1";
            _brittleDuctileRatioChart.Series.Add(series);
        }

        /// <summary>
        /// 创建矿物堆叠柱状图
        /// </summary>
        private void CreateMineralStackedBarChart()
        {
            // 设置矿物堆叠柱状图的数据
            _mineralStackedBarChart.ResultData = _resultData;
            _mineralStackedBarChart.BrittleMinerals = _brittleColumns;
            _mineralStackedBarChart.DuctileMinerals = _ductileColumns;
        }

        /// <summary>
        /// 绘制图表
        /// </summary>
        private void DrawCharts()
        {
            // 绘制深度与脆性指数关系图表
            DrawDepthVsBrittlenessChart();

            // 绘制脆性/塑性矿物比例图表
            DrawBrittleDuctileRatioChart();

            // 矿物堆叠柱状图不需要单独绘制，已在CreateMineralStackedBarChart中设置数据
        }

        /// <summary>
        /// 绘制深度与脆性指数关系图表
        /// </summary>
        private void DrawDepthVsBrittlenessChart()
        {
            Series series = _depthVsBrittlenessChart.Series[0];
            series.Points.Clear();

            foreach (DataRow row in _resultData.Rows)
            {
                if (row["顶深/m"] != DBNull.Value && row["脆性指数"] != DBNull.Value)
                {
                    double depth = Convert.ToDouble(row["顶深/m"]);
                    double brittlenessIndex = Convert.ToDouble(row["脆性指数"]);

                    // 确保脆性指数值已乘以100
                    if (brittlenessIndex < 1.0)
                    {
                        brittlenessIndex *= 100;
                    }

                    DataPoint point = new DataPoint(depth, brittlenessIndex);
                    point.ToolTip = $"深度: {depth:F2}m, 脆性指数: {brittlenessIndex:F2}%";
                    series.Points.Add(point);
                }
            }
        }

        /// <summary>
        /// 绘制脆性/塑性矿物比例图表
        /// </summary>
        private void DrawBrittleDuctileRatioChart()
        {
            Series series = _brittleDuctileRatioChart.Series[0];
            series.Points.Clear();

            double brittleTotal = 0;
            double ductileTotal = 0;

            // 计算平均值
            foreach (DataRow row in _resultData.Rows)
            {
                if (row["脆性矿物总量"] != DBNull.Value && row["塑性矿物总量"] != DBNull.Value)
                {
                    brittleTotal += Convert.ToDouble(row["脆性矿物总量"]);
                    ductileTotal += Convert.ToDouble(row["塑性矿物总量"]);
                }
            }

            // 添加数据点
            if (brittleTotal <= 0 && ductileTotal <= 0)
            {
                DataPoint point = new DataPoint(0, 1);
                point.AxisLabel = "无数据";
                point.Color = Color.Gray;
                series.Points.Add(point);
            }
            else
            {
                DataPoint brittlePoint = new DataPoint(0, brittleTotal);
                brittlePoint.AxisLabel = "脆性矿物";
                brittlePoint.LegendText = "脆性矿物";
                brittlePoint.Color = Color.Blue;
                brittlePoint.ToolTip = $"脆性矿物: {brittleTotal:F2}";
                series.Points.Add(brittlePoint);

                DataPoint ductilePoint = new DataPoint(1, ductileTotal);
                ductilePoint.AxisLabel = "塑性矿物";
                ductilePoint.LegendText = "塑性矿物";
                ductilePoint.Color = Color.Green;
                ductilePoint.ToolTip = $"塑性矿物: {ductileTotal:F2}";
                series.Points.Add(ductilePoint);
            }
        }



        #endregion
    }
}