; -- 程序信息定义 --
#define MyAppName "脆性矿物分析系统"
#define MyAppVersion "1.1"
#define MyAppPublisher "成明凯"
#define MyAppExeName "BritSystem.exe"
#define SourcePath "bin\Release\net8.0-windows"  ; 发布输出路径

; -- 安装程序设置 --
[Setup]
AppId={{A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
DefaultDirName={autopf}\{#MyAppName}
DisableProgramGroupPage=yes
OutputDir=.\Output
OutputBaseFilename=BritSystemSetup
Compression=lzma
SolidCompression=yes
WizardStyle=modern
DefaultDialogFontName=Microsoft YaHei UI
; 安装包图标（绝对路径）
SetupIconFile="C:\Users\<USER>\Desktop\Brittleness index system\repos\BritSystem\Images\Ikun.ico"

; -- 语言支持 --
[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

; -- 界面文本设置 --
[Messages]
SetupAppTitle=安装 {#MyAppName}
SetupWindowTitle=安装 - {#MyAppName}
BeveledLabel=中文版
WelcomeLabel1=欢迎使用 {#MyAppName} 安装向导
WelcomeLabel2=这将在您的计算机上安装 {#MyAppName} {#MyAppVersion}。%n%n建议在继续之前关闭所有其他应用程序。
ClickNext=点击"下一步"继续，或点击"取消"退出安装程序。
ButtonNext=下一步(&N)
ButtonBack=上一步(&B)
ButtonInstall=安装(&I)
ButtonCancel=取消(&C)
ButtonFinish=完成(&F)
SelectDirLabel3=安装程序将安装 {#MyAppName} 到以下文件夹。
SelectDirBrowseLabel=点击"下一步"继续。如果要选择其他文件夹，请点击"浏览"。
DiskSpaceMBLabel=至少需要 [mb] MB 的可用磁盘空间。
ReadyLabel1=安装程序已准备好开始安装 {#MyAppName} 到您的计算机。
ExitSetupMessage=安装尚未完成。如果您现在退出，程序将不会被安装。%n%n您可以稍后再运行安装程序。%n%n退出安装吗？
FinishedHeadingLabel=完成 {#MyAppName} 安装
FinishedLabel=安装程序已完成在您的计算机上安装 {#MyAppName}。

; -- 自定义消息 --
[CustomMessages]
CreateDesktopIcon=创建桌面图标
LaunchProgram=启动 {#MyAppName}
AdditionalIcons=附加图标:
UninstallProgram=卸载 {#MyAppName}

; -- 安装任务选项 --
[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

; -- 文件复制设置 --
[Files]
; 主程序文件
Source: "{#SourcePath}\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "{#SourcePath}\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; 图标文件
Source: "C:\Users\<USER>\Desktop\Brittleness index system\repos\BritSystem\Images\Mi.ico"; DestDir: "{app}\Images"; Flags: ignoreversion
Source: "C:\Users\<USER>\Desktop\Brittleness index system\repos\BritSystem\Images\Ikun.ico"; DestDir: "{app}\Images"; Flags: ignoreversion
; 用户手册
Source: "UserManual.txt"; DestDir: "{app}\Docs"; Flags: ignoreversion

; -- 快捷方式设置 --
[Icons]
; 开始菜单快捷方式
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\Images\Mi.ico"
; 用户手册快捷方式
Name: "{autoprograms}\{#MyAppName}\用户手册"; Filename: "{app}\Docs\UserManual.txt"
; 桌面图标
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\Images\Mi.ico"; Tasks: desktopicon

; -- 安装后运行程序 --
[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent
Filename: "{app}\Docs\UserManual.txt"; Description: "查看用户手册"; Flags: postinstall shellexec skipifsilent

; -- 卸载设置 --
[UninstallDelete]
Type: files; Name: "{app}\*.*"
Type: dirifempty; Name: "{app}"

; -- 注册表设置 --
[Registry]
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\{#MyAppPublisher}\{#MyAppName}"; ValueType: string; ValueName: "Version"; ValueData: "{#MyAppVersion}"; Flags: uninsdeletekey

; -- 自定义代码（检测 WebView2 运行时）--
[Code]
function InitializeSetup(): Boolean;
var
  WebView2Installed: Boolean;
  ResultCode: Integer;
begin
  Result := True;

  WebView2Installed := RegKeyExists(HKLM, 'SOFTWARE\WOW6432Node\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}') or
                       RegKeyExists(HKCU, 'SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}');

  if not WebView2Installed then
  begin
    if MsgBox('Microsoft Edge WebView2 运行时未安装，该组件是程序运行所必需的。' + #13#10 + '是否现在下载并安装？', mbConfirmation, MB_YESNO) = IDYES then
    begin
      if not Exec('powershell.exe', '-Command "& { Invoke-WebRequest -Uri ''https://go.microsoft.com/fwlink/p/?LinkId=2124703'' -OutFile ''WebView2Setup.exe''; Start-Process -FilePath ''WebView2Setup.exe'' -ArgumentList ''/silent /install'' -Wait }"', '', SW_SHOW, ewWaitUntilTerminated, ResultCode) then
      begin
        MsgBox('无法下载 WebView2 运行时。请手动安装后再运行本安装程序。', mbError, MB_OK);
        Result := False;
      end;
    end
    else
    begin
      if MsgBox('没有 WebView2 运行时，程序可能无法正常运行。' + #13#10 + '是否仍要继续安装？', mbConfirmation, MB_YESNO) = IDNO then
        Result := False;
    end;
  end;
end;
