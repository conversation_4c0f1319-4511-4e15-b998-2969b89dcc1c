using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BritSystem.Logging
{
    /// <summary>
    /// 日志数据管理类，用于创建和处理日志数据
    /// </summary>
    public class LogDataManager
    {
        // 日志文件路径
        private string _logFilePath;
        
        // 日志数据列表
        private List<LogEntry> _logEntries;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        public LogDataManager(string logFilePath = "Log.txt")
        {
            _logFilePath = logFilePath;
            _logEntries = new List<LogEntry>();
            
            // 如果日志文件存在，则加载日志数据
            if (File.Exists(_logFilePath))
            {
                LoadLogData();
            }
        }
        
        /// <summary>
        /// 加载日志数据
        /// </summary>
        private void LoadLogData()
        {
            try
            {
                // 读取所有日志行
                string[] lines = File.ReadAllLines(_logFilePath);
                
                foreach (string line in lines)
                {
                    // 解析日志行
                    if (TryParseLogLine(line, out LogEntry entry))
                    {
                        _logEntries.Add(entry);
                    }
                }
                
                Console.WriteLine($"已加载 {_logEntries.Count} 条日志记录");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载日志数据时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 尝试解析日志行
        /// </summary>
        /// <param name="line">日志行文本</param>
        /// <param name="entry">解析后的日志条目</param>
        /// <returns>是否成功解析</returns>
        private bool TryParseLogLine(string line, out LogEntry entry)
        {
            entry = null;
            
            try
            {
                // 检查行是否为空
                if (string.IsNullOrWhiteSpace(line))
                    return false;
                
                // 尝试解析日志行格式: [时间] [级别] 消息
                string[] parts = line.Split(new[] { ']' }, 3);
                
                if (parts.Length >= 3)
                {
                    // 解析时间
                    string timeStr = parts[0].Trim('[', ' ');
                    if (DateTime.TryParse(timeStr, out DateTime timestamp))
                    {
                        // 解析日志级别
                        string levelStr = parts[1].Trim('[', ' ');
                        if (Enum.TryParse(levelStr, true, out LogLevel level))
                        {
                            // 提取消息
                            string message = parts[2].Trim();
                            
                            // 创建日志条目
                            entry = new LogEntry
                            {
                                Timestamp = timestamp,
                                Level = level,
                                Message = message
                            };
                            
                            return true;
                        }
                    }
                }
                
                // 如果无法按标准格式解析，则将整行作为消息
                entry = new LogEntry
                {
                    Timestamp = DateTime.Now,
                    Level = LogLevel.Info,
                    Message = line
                };
                
                return true;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// 添加日志条目
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        public void AddLogEntry(LogLevel level, string message)
        {
            LogEntry entry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message
            };
            
            _logEntries.Add(entry);
            
            // 将日志写入文件
            AppendLogToFile(entry);
        }
        
        /// <summary>
        /// 将日志条目追加到文件
        /// </summary>
        /// <param name="entry">日志条目</param>
        private void AppendLogToFile(LogEntry entry)
        {
            try
            {
                // 格式化日志行
                string logLine = $"[{entry.Timestamp:yyyy-MM-dd HH:mm:ss}] [{entry.Level}] {entry.Message}";
                
                // 追加到文件
                File.AppendAllText(_logFilePath, logLine + Environment.NewLine);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志文件时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogInfo(string message)
        {
            AddLogEntry(LogLevel.Info, message);
        }
        
        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogWarning(string message)
        {
            AddLogEntry(LogLevel.Warning, message);
        }
        
        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogError(string message)
        {
            AddLogEntry(LogLevel.Error, message);
        }
        
        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public void LogDebug(string message)
        {
            AddLogEntry(LogLevel.Debug, message);
        }
        
        /// <summary>
        /// 获取所有日志条目
        /// </summary>
        /// <returns>日志条目列表</returns>
        public List<LogEntry> GetAllLogEntries()
        {
            return _logEntries.ToList();
        }
        
        /// <summary>
        /// 获取指定级别的日志条目
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <returns>日志条目列表</returns>
        public List<LogEntry> GetLogEntriesByLevel(LogLevel level)
        {
            return _logEntries.Where(e => e.Level == level).ToList();
        }
        
        /// <summary>
        /// 获取指定时间范围内的日志条目
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>日志条目列表</returns>
        public List<LogEntry> GetLogEntriesByTimeRange(DateTime startTime, DateTime endTime)
        {
            return _logEntries.Where(e => e.Timestamp >= startTime && e.Timestamp <= endTime).ToList();
        }
        
        /// <summary>
        /// 清除所有日志
        /// </summary>
        public void ClearLogs()
        {
            _logEntries.Clear();
            
            // 清空日志文件
            File.WriteAllText(_logFilePath, string.Empty);
        }
    }
    
    /// <summary>
    /// 日志条目类
    /// </summary>
    public class LogEntry
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; }
        
        /// <summary>
        /// 日志消息
        /// </summary>
        public string Message { get; set; }
    }
    
    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Fatal
    }
}
