# 脆性矿物分析系统 - 用户手册

## 1. 系统简介

脆性矿物分析系统是一款专业的地质分析软件，用于计算岩石样本的脆性指数。系统通过分析岩石中的脆性矿物和塑性矿物含量，计算出脆性指数，帮助地质工作者评估岩石的脆性特性。

## 2. 系统登录

- 用户名: admin
- 密码: 123

## 3. 主要功能

### 3.1 数据导入

系统支持从Excel文件导入岩石矿物数据。支持的文件格式包括.xlsx和.xls。

### 3.2 矿物列识别

系统能够自动识别Excel文件中的以下列：
- 顶深列和底深列
- 脆性矿物列（如石英、白云石、菱铁矿、斜长石等）
- 塑性矿物列（如黏土矿物、有机质等）

### 3.3 手动映射

如果系统无法自动识别某些列，用户可以使用手动映射功能，将Excel文件中的列映射到系统预定义的矿物类型。

### 3.4 脆性指数计算

系统使用以下公式计算脆性指数：
脆性指数 = 脆性矿物总量 / (脆性矿物总量 + 塑性矿物总量)

### 3.5 数据可视化

系统提供了数据可视化功能，可以生成深度-脆性指数关系曲线，帮助用户直观地分析岩石脆性随深度的变化。

### 3.6 数据导出

计算结果可以导出为Excel文件，方便用户进一步分析和报告生成。

## 4. 操作指南

### 4.1 导入数据

1. 点击"加载数据"按钮
2. 在文件选择对话框中选择Excel文件
3. 系统将自动加载数据并显示在数据网格中

### 4.2 选择深度列

1. 点击"选择顶深列"按钮，从弹出的列表中选择顶深列
2. 点击"选择底深列"按钮，从弹出的列表中选择底深列

### 4.3 选择矿物列

1. 从可用列表中选择脆性矿物列，点击"+"按钮添加到脆性矿物列表
2. 从可用列表中选择塑性矿物列，点击"+"按钮添加到塑性矿物列表
3. 如需移除已选择的列，选中该列并点击"-"按钮

### 4.4 手动映射

如果系统无法自动识别某些列，可以使用手动映射功能：
1. 点击"手动映射"按钮
2. 在弹出的对话框中，选择源列和目标类型
3. 点击"添加映射"按钮
4. 完成所有映射后，点击"保存"按钮

### 4.5 计算脆性指数

1. 确保已选择顶深列、底深列，以及至少一个脆性矿物列或塑性矿物列
2. 点击"计算脆性指数"按钮
3. 系统将计算脆性指数并显示结果

### 4.6 可视化结果

1. 计算完成后，点击"可视化"按钮
2. 系统将生成深度-脆性指数关系曲线
3. 可以在曲线上点击数据点查看详细信息

### 4.7 保存结果

1. 点击"保存数据"按钮
2. 在文件保存对话框中选择保存位置和文件名
3. 系统将计算结果保存为Excel文件

## 5. 常见问题

### 5.1 系统无法识别矿物列

如果系统无法自动识别矿物列，可以尝试以下方法：
1. 检查Excel文件中的列名是否规范
2. 使用手动映射功能，手动指定矿物列

### 5.2 计算结果中有空值

如果计算结果中有空值，可能是因为：
1. 原始数据中存在缺失值
2. 某些行的脆性矿物和塑性矿物总量都为零

### 5.3 系统运行缓慢

如果系统运行缓慢，可以尝试以下方法：
1. 减少导入的数据量
2. 关闭其他占用内存的应用程序
3. 确保计算机满足系统最低配置要求

## 6. 技术支持

如有任何问题或建议，请联系系统开发团队：
- 邮箱：<EMAIL>
- 电话：123-456-7890

## 7. 版权信息

© 2025 脆性矿物分析系统开发团队的负责人：539办公室Ikun。保留所有权利。
