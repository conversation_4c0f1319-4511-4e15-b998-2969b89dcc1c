using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using BritSystem.Services;

namespace BritSystem
{
    /// <summary>
    /// 改进的黏土矿物总量列识别类
    /// </summary>
    public class ImprovedClayMineralDetection
    {
        /// <summary>
        /// 查找黏土矿物总量列
        /// </summary>
        /// <param name="sourceData">源数据表</param>
        /// <param name="targetColumnName">目标列名</param>
        /// <returns>找到的列名</returns>
        public static string FindClayMineralTotalColumn(DataTable sourceData, string targetColumnName = "黏土矿物总量%")
        {
            if (sourceData == null)
                return string.Empty;

            LoggingService.Instance.Info($"开始查找黏土矿物总量列，目标列名: {targetColumnName}");

            try
            {
                // 精确匹配黏土矿物总量%z的关键词列表
                string[] clayTotalKeywords = {
                    "黏土矿物总量%z", "粘土矿物总量%z",
                    "黏土矿物总量/%", "粘土矿物总量/%",
                    "黏土矿物总量z", "粘土矿物总量z",
                    "黏土矿物总量相对含量", "粘土矿物总量相对含量",
                    "黏土矿物相对含量/%", "粘土矿物相对含量/%",
                    "黏土矿物相对含量%", "粘土矿物相对含量%",
                    "黏土矿物相对含量", "粘土矿物相对含量",
                    "黏土矿物总量%", "粘土矿物总量%",
                    "黏土矿物总量", "粘土矿物总量",
                    "黏土总量%", "粘土总量%",
                    "黏土总量", "粘土总量",
                    "clay minerals total", "clay minerals content",
                    "clay total", "total clay",
                    "clay minerals", "clayminerals",
                    "ductile minerals", "ductile content"
                };

                // 1. 精确匹配
                foreach (DataColumn col in sourceData.Columns)
                {
                    if (col.ColumnName.Equals(targetColumnName, StringComparison.OrdinalIgnoreCase))
                    {
                        if (!IsTOCColumn(col.ColumnName))
                        {
                            LoggingService.Instance.Info($"黏土矿物总量精确匹配成功: '{targetColumnName}' -> '{col.ColumnName}'");
                            return col.ColumnName;
                        }
                        else
                        {
                            LoggingService.Instance.Warning($"跳过TOC列，防止误匹配: {col.ColumnName}");
                        }
                    }
                }

                // 2. 尝试使用关键词匹配
                foreach (string keyword in clayTotalKeywords)
                {
                    foreach (DataColumn col in sourceData.Columns)
                    {
                        if (!IsTOCColumn(col.ColumnName) &&
                            (col.ColumnName.Trim().Equals(keyword.Trim(), StringComparison.OrdinalIgnoreCase) ||
                             col.ColumnName.Replace(" ", "").Contains(keyword.Replace(" ", ""), StringComparison.OrdinalIgnoreCase)))
                        {
                            LoggingService.Instance.Info($"黏土矿物总量关键词匹配成功: '{targetColumnName}' -> '{col.ColumnName}' (关键词: {keyword})");
                            return col.ColumnName;
                        }
                    }
                }

                // 3. 在表格前几行中查找黏土矿物总量列
                int maxRowsToCheck = Math.Min(10, sourceData.Rows.Count);
                for (int rowIndex = 0; rowIndex < maxRowsToCheck; rowIndex++)
                {
                    for (int colIndex = 0; colIndex < sourceData.Columns.Count; colIndex++)
                    {
                        // 获取单元格值
                        object cellObj = sourceData.Rows[rowIndex][colIndex];
                        if (cellObj == null || cellObj == DBNull.Value)
                            continue;

                        string cellValue = cellObj.ToString().Trim();
                        if (string.IsNullOrWhiteSpace(cellValue))
                            continue;

                        // 检查单元格值是否匹配任何关键词
                        foreach (string keyword in clayTotalKeywords)
                        {
                            if (cellValue.Trim().Equals(keyword.Trim(), StringComparison.OrdinalIgnoreCase) ||
                                cellValue.Replace(" ", "").Contains(keyword.Replace(" ", ""), StringComparison.OrdinalIgnoreCase))
                            {
                                if (!IsTOCColumn(cellValue))
                                {
                                    LoggingService.Instance.Info($"在行 {rowIndex + 1}, 列 {colIndex + 1} 找到黏土矿物总量值: '{cellValue}'");
                                    return sourceData.Columns[colIndex].ColumnName;
                                }
                            }
                        }
                    }
                }

                // 4. 查找包含"黏土矿物总量"或"粘土矿物总量"的列
                var totalClayColumns = new List<DataColumn>();
                foreach (DataColumn col in sourceData.Columns)
                {
                    if ((col.ColumnName.Contains("黏土矿物总量") || col.ColumnName.Contains("粘土矿物总量")) &&
                        !IsTOCColumn(col.ColumnName))
                    {
                        totalClayColumns.Add(col);
                        LoggingService.Instance.Debug($"找到黏土矿物总量候选列: {col.ColumnName}");
                    }
                }

                if (totalClayColumns.Count > 0)
                {
                    var bestMatch = totalClayColumns.First();
                    LoggingService.Instance.Info($"黏土矿物总量特殊匹配成功: '{targetColumnName}' -> '{bestMatch.ColumnName}'");
                    return bestMatch.ColumnName;
                }

                // 5. 查找包含"黏土矿物相对含量"或"粘土矿物相对含量"的列
                var relativeClayColumns = new List<DataColumn>();
                foreach (DataColumn col in sourceData.Columns)
                {
                    if ((col.ColumnName.Contains("黏土矿物相对含量") || col.ColumnName.Contains("粘土矿物相对含量")) &&
                        !IsTOCColumn(col.ColumnName))
                    {
                        relativeClayColumns.Add(col);
                        LoggingService.Instance.Debug($"找到黏土矿物相对含量候选列: {col.ColumnName}");
                    }
                }

                if (relativeClayColumns.Count > 0)
                {
                    var bestMatch = relativeClayColumns.First();
                    LoggingService.Instance.Info($"黏土矿物相对含量特殊匹配成功: '{targetColumnName}' -> '{bestMatch.ColumnName}'");
                    return bestMatch.ColumnName;
                }

                // 6. 查找包含"黏土"或"粘土"且包含"总量"或"含量"的列
                var clayWithTotalColumns = new List<DataColumn>();
                foreach (DataColumn col in sourceData.Columns)
                {
                    if ((col.ColumnName.Contains("黏土") || col.ColumnName.Contains("粘土")) &&
                        (col.ColumnName.Contains("总量") || col.ColumnName.Contains("含量")) &&
                        !IsTOCColumn(col.ColumnName))
                    {
                        clayWithTotalColumns.Add(col);
                        LoggingService.Instance.Debug($"找到黏土+总量候选列: {col.ColumnName}");
                    }
                }

                if (clayWithTotalColumns.Count > 0)
                {
                    var bestMatch = clayWithTotalColumns.First();
                    LoggingService.Instance.Info($"黏土+总量匹配成功: '{targetColumnName}' -> '{bestMatch.ColumnName}'");
                    return bestMatch.ColumnName;
                }

                // 7. 查找包含"黏土"或"粘土"的列，但不包含"TOC"
                var clayOnlyColumns = new List<DataColumn>();
                foreach (DataColumn col in sourceData.Columns)
                {
                    if ((col.ColumnName.Contains("黏土") || col.ColumnName.Contains("粘土") ||
                         col.ColumnName.ToLower().Contains("clay")) &&
                        !IsTOCColumn(col.ColumnName))
                    {
                        clayOnlyColumns.Add(col);
                        LoggingService.Instance.Debug($"找到黏土候选列: {col.ColumnName}");
                    }
                }

                if (clayOnlyColumns.Count > 0)
                {
                    var bestMatch = clayOnlyColumns.First();
                    LoggingService.Instance.Info($"黏土匹配成功: '{targetColumnName}' -> '{bestMatch.ColumnName}'");
                    return bestMatch.ColumnName;
                }

                // 8. 尝试使用英文名称匹配
                var englishClayColumns = new List<DataColumn>();
                foreach (DataColumn col in sourceData.Columns)
                {
                    string lowerColName = col.ColumnName.ToLower();
                    if ((lowerColName.Contains("clay mineral") ||
                         lowerColName.Contains("clay content") ||
                         lowerColName.Contains("clay total") ||
                         lowerColName.Contains("ductile mineral") ||
                         lowerColName.Contains("ductile content") ||
                         lowerColName.Contains("total clay") ||
                         lowerColName.Contains("clayminerals")) &&
                        !IsTOCColumn(col.ColumnName))
                    {
                        englishClayColumns.Add(col);
                        LoggingService.Instance.Debug($"找到英文黏土候选列: {col.ColumnName}");
                    }
                }

                if (englishClayColumns.Count > 0)
                {
                    var bestMatch = englishClayColumns.First();
                    LoggingService.Instance.Info($"英文黏土匹配成功: '{targetColumnName}' -> '{bestMatch.ColumnName}'");
                    return bestMatch.ColumnName;
                }

                // 9. 尝试使用X-衍射分析区域附近的列
                int xrdColumnIndex = -1;
                for (int colIndex = 0; colIndex < sourceData.Columns.Count; colIndex++)
                {
                    string columnName = sourceData.Columns[colIndex].ColumnName;
                    if (columnName.Contains("X-衍射") || columnName.Contains("X衍射") || 
                        columnName.ToLower().Contains("xrd") || columnName.ToLower().Contains("x-ray diffraction"))
                    {
                        xrdColumnIndex = colIndex;
                        LoggingService.Instance.Debug($"找到X-衍射分析列: {columnName}, 索引: {colIndex}");
                        break;
                    }
                }

                if (xrdColumnIndex >= 0)
                {
                    // 检查X-衍射分析区域附近的列
                    int startColIndex = Math.Max(0, xrdColumnIndex - 5);
                    int endColIndex = Math.Min(sourceData.Columns.Count - 1, xrdColumnIndex + 10);
                    
                    // 首先检查是否有Column43附近的列（通常是黏土矿物总量）
                    for (int colIndex = startColIndex; colIndex <= endColIndex; colIndex++)
                    {
                        string columnName = sourceData.Columns[colIndex].ColumnName;
                        if (Regex.IsMatch(columnName, @"^Column4[1-5]$", RegexOptions.IgnoreCase))
                        {
                            LoggingService.Instance.Warning($"在X-衍射分析区域找到可能的黏土矿物总量列: {columnName}");
                            
                            // 检查列数据是否为数值且大部分在0-100范围内
                            bool isPercentageColumn = IsPercentageDataColumn(sourceData, colIndex);
                            if (isPercentageColumn)
                            {
                                LoggingService.Instance.Info($"使用X-衍射分析区域附近的列作为黏土矿物总量列: {columnName}");
                                return columnName;
                            }
                        }
                    }
                }

                // 10. 最后尝试使用列索引匹配
                var columnIndexPattern = new Regex(@"^Column(\d+)$", RegexOptions.IgnoreCase);
                var columnIndexMatches = new List<Tuple<DataColumn, int>>();

                foreach (DataColumn col in sourceData.Columns)
                {
                    var match = columnIndexPattern.Match(col.ColumnName);
                    if (match.Success && int.TryParse(match.Groups[1].Value, out int columnIndex))
                    {
                        // 黏土矿物总量列通常在40-45之间
                        if (columnIndex >= 40 && columnIndex <= 45 && !IsTOCColumn(col.ColumnName))
                        {
                            // 检查列数据是否为数值且大部分在0-100范围内
                            bool isPercentageColumn = IsPercentageDataColumn(sourceData, sourceData.Columns.IndexOf(col));
                            if (isPercentageColumn)
                            {
                                columnIndexMatches.Add(new Tuple<DataColumn, int>(col, columnIndex));
                                LoggingService.Instance.Debug($"找到可能的黏土矿物总量列: {col.ColumnName}, 索引: {columnIndex}");
                            }
                        }
                    }
                }

                if (columnIndexMatches.Count > 0)
                {
                    // 按列索引排序
                    var sortedColumns = columnIndexMatches.OrderBy(t => t.Item2).ToList();
                    var bestMatch = sortedColumns.First().Item1;
                    LoggingService.Instance.Warning($"使用列索引匹配黏土矿物总量列: '{targetColumnName}' -> '{bestMatch.ColumnName}'");
                    return bestMatch.ColumnName;
                }

                // 如果所有尝试都失败，返回空字符串
                LoggingService.Instance.Error($"无法找到黏土矿物总量列，目标列名: {targetColumnName}");
                return string.Empty;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Exception(ex, "查找黏土矿物总量列时出错");
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查列是否包含百分比数据（0-100范围内的数值）
        /// </summary>
        /// <param name="table">数据表</param>
        /// <param name="columnIndex">列索引</param>
        /// <returns>是否是百分比数据列</returns>
        private static bool IsPercentageDataColumn(DataTable table, int columnIndex)
        {
            if (table == null || columnIndex < 0 || columnIndex >= table.Columns.Count)
                return false;

            int totalRows = Math.Min(20, table.Rows.Count); // 检查前20行或所有行
            if (totalRows == 0) return false;

            int validNumberCount = 0;
            int percentageRangeCount = 0;

            for (int rowIndex = 0; rowIndex < totalRows; rowIndex++)
            {
                object cellValue = table.Rows[rowIndex][columnIndex];
                if (cellValue != null && cellValue != DBNull.Value)
                {
                    if (double.TryParse(cellValue.ToString(), out double value))
                    {
                        validNumberCount++;
                        if (value >= 0 && value <= 100)
                        {
                            percentageRangeCount++;
                        }
                    }
                }
            }

            // 如果有效数字数量大于0，且大部分在0-100范围内，则认为是百分比列
            return validNumberCount > 0 && (double)percentageRangeCount / validNumberCount > 0.7;
        }

        /// <summary>
        /// 检查列名是否是TOC列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>是否是TOC列</returns>
        private static bool IsTOCColumn(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return false;

            string lowerColName = columnName.ToLower();

            return lowerColName == "toc" ||
                   lowerColName == "toc%" ||
                   lowerColName.Contains("有机碳") ||
                   lowerColName.Contains("总有机碳") ||
                   lowerColName.Contains("totalorganiccarbon") ||
                   lowerColName.Contains("organiccarbon") ||
                   lowerColName.Contains("total organic carbon") ||
                   lowerColName.Contains("total organic") ||
                   lowerColName.Contains("organic carbon");
        }

        /// <summary>
        /// 获取所有可能的黏土矿物总量列
        /// </summary>
        /// <param name="sourceData">源数据表</param>
        /// <returns>可能的黏土矿物总量列列表</returns>
        public static List<string> GetPossibleClayMineralColumns(DataTable sourceData)
        {
            var result = new List<string>();

            if (sourceData == null)
                return result;

            try
            {
                // 精确匹配黏土矿物总量的关键词列表
                string[] clayTotalKeywords = {
                    // 保留原始列名符号
                    "黏土矿物总量%z", "粘土矿物总量%z",
                    "黏土矿物总量/%", "粘土矿物总量/%",
                    // 新增模糊匹配模式
                    "黏土矿物总量（%）", "∑黏土矿物",
                    "clay_minerals_total", "clay_total_%"
                };

                // 1. 使用关键词精确匹配
                foreach (string keyword in clayTotalKeywords)
                {
                    foreach (DataColumn col in sourceData.Columns)
                    {
                        if (!IsTOCColumn(col.ColumnName) &&
                            (col.ColumnName.Trim().Equals(keyword.Trim(), StringComparison.OrdinalIgnoreCase) ||
                             col.ColumnName.Replace(" ", "").Contains(keyword.Replace(" ", ""), StringComparison.OrdinalIgnoreCase)) &&
                            !result.Contains(col.ColumnName))
                        {
                            result.Add(col.ColumnName);
                            LoggingService.Instance.Info($"黏土矿物总量关键词匹配成功: '{col.ColumnName}' (关键词: {keyword})");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Exception(ex, "获取可能的黏土矿物列时发生异常");
                return result;
            }

            // 3. 查找包含"黏土矿物总量"或"粘土矿物总量"的列
            foreach (DataColumn col in sourceData.Columns)
            {
                if ((col.ColumnName.Contains("黏土矿物总量") || col.ColumnName.Contains("粘土矿物总量")) &&
                    !IsTOCColumn(col.ColumnName) &&
                    !result.Contains(col.ColumnName))
                {
                    result.Add(col.ColumnName);
                    LoggingService.Instance.Debug($"找到黏土矿物总量列: {col.ColumnName}");
                }
            }

            // 3. 查找包含"黏土矿物相对含量"或"粘土矿物相对含量"的列
            foreach (DataColumn col in sourceData.Columns)
            {
                if ((col.ColumnName.Contains("黏土矿物相对含量") || col.ColumnName.Contains("粘土矿物相对含量")) &&
                    !IsTOCColumn(col.ColumnName) &&
                    !result.Contains(col.ColumnName))
                {
                    result.Add(col.ColumnName);
                    LoggingService.Instance.Debug($"找到黏土矿物相对含量列: {col.ColumnName}");
                }
            }

            // 4. 查找包含"黏土"或"粘土"且包含"总量"或"含量"的列
            foreach (DataColumn col in sourceData.Columns)
            {
                if ((col.ColumnName.Contains("黏土") || col.ColumnName.Contains("粘土")) &&
                    (col.ColumnName.Contains("总量") || col.ColumnName.Contains("含量")) &&
                    !IsTOCColumn(col.ColumnName) &&
                    !result.Contains(col.ColumnName))
                {
                    result.Add(col.ColumnName);
                    LoggingService.Instance.Debug($"找到黏土+总量列: {col.ColumnName}");
                }
            }

            // 5. 查找包含"黏土"或"粘土"的列，但不包含"TOC"
            foreach (DataColumn col in sourceData.Columns)
            {
                if ((col.ColumnName.Contains("黏土") || col.ColumnName.Contains("粘土") ||
                     col.ColumnName.ToLower().Contains("clay")) &&
                    !IsTOCColumn(col.ColumnName) &&
                    !result.Contains(col.ColumnName))
                {
                    result.Add(col.ColumnName);
                    LoggingService.Instance.Debug($"找到黏土列: {col.ColumnName}");
                }
            }

            // 6. 尝试使用英文名称匹配
            foreach (DataColumn col in sourceData.Columns)
            {
                string lowerColName = col.ColumnName.ToLower();
                if ((lowerColName.Contains("clay mineral") ||
                     lowerColName.Contains("clay content") ||
                     lowerColName.Contains("clay total") ||
                     lowerColName.Contains("ductile mineral") ||
                     lowerColName.Contains("ductile content") ||
                     lowerColName.Contains("total clay") ||
                     lowerColName.Contains("clayminerals")) &&
                    !IsTOCColumn(col.ColumnName) &&
                    !result.Contains(col.ColumnName))
                {
                    result.Add(col.ColumnName);
                    LoggingService.Instance.Debug($"找到英文黏土列: {col.ColumnName}");
                }
            }

            // 7. 尝试使用列索引匹配
            var columnIndexPattern = new Regex(@"Column(\d+)", RegexOptions.IgnoreCase);

            foreach (DataColumn col in sourceData.Columns)
            {
                var match = columnIndexPattern.Match(col.ColumnName);
                if (match.Success && int.TryParse(match.Groups[1].Value, out int columnIndex))
                {
                    // 通常黏土矿物总量列在40-45之间
                    if (columnIndex >= 40 && columnIndex <= 45 &&
                        !IsTOCColumn(col.ColumnName) &&
                        !result.Contains(col.ColumnName))
                    {
                        result.Add(col.ColumnName);
                        LoggingService.Instance.Debug($"通过列索引找到可能的黏土矿物总量列: {col.ColumnName}, 索引: {columnIndex}");
                    }
                }
            }

            // 记录找到的所有可能的黏土矿物总量列
            if (result.Count > 0)
            {
                LoggingService.Instance.Info($"找到 {result.Count} 个可能的黏土矿物总量列:");
                foreach (var colName in result)
                {
                    LoggingService.Instance.Info($"  - {colName}");
                }
            }
            else
            {
                LoggingService.Instance.Warning("未找到任何可能的黏土矿物总量列");
            }

            return result;
        }
    }
}
