<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #333;
            color: white;
            text-align: center;
            padding: 50px;
        }
        h1 {
            color: #00FFFF;
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
        }
        button {
            background-color: #00FFFF;
            color: #333;
            border: none;
            padding: 10px 20px;
            font-size: 1.1rem;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        button:hover {
            background-color: #00CCCC;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简单测试页面</h1>
        <p>如果您能看到这个页面，说明WebView2已经成功初始化并且可以正常工作！</p>
        <p>当前时间: <span id="current-time"></span></p>
        <p>这是一个非常简单的测试页面，没有任何复杂的JavaScript或CSS。</p>
        <button id="login-btn">登录系统</button>
    </div>

    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        }
        
        // 每秒更新一次时间
        updateTime();
        setInterval(updateTime, 1000);
        
        // 登录按钮点击事件
        document.getElementById('login-btn').addEventListener('click', function() {
            if (window.chrome && window.chrome.webview) {
                // 向C#发送消息
                window.chrome.webview.postMessage({
                    action: 'login',
                    username: 'admin',
                    password: '123'
                });
            } else {
                alert('此功能仅在应用程序中可用');
            }
        });
    </script>
</body>
</html>
