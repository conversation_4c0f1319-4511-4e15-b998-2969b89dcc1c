        // 添加一个显示源数据的方法
        private void ShowSourceData()
        {
            try
            {
                if (_sourceData == null || _sourceData.Columns.Count == 0)
                {
                    MessageBox.Show("未加载数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 创建一个新窗体显示源数据
                Form dataForm = new Form();
                dataForm.Text = "源数据表";
                dataForm.Size = new Size(800, 600);
                dataForm.StartPosition = FormStartPosition.CenterParent;

                // 添加数据网格视图
                DataGridView dgv = new DataGridView();
                dgv.Dock = DockStyle.Fill;
                dgv.DataSource = _sourceData;
                dgv.AllowUserToAddRows = false;
                dgv.AllowUserToDeleteRows = false;
                dgv.ReadOnly = true;
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.DisplayedCells;

                // 添加到窗体
                dataForm.Controls.Add(dgv);

                // 显示窗体
                dataForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示源数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"显示源数据时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 添加一个按钮到界面上
        private void AddShowSourceDataButton()
        {
            Button btnShowSourceData = new Button();
            btnShowSourceData.Text = "显示源数据";
            btnShowSourceData.Size = new Size(183, 48);
            btnShowSourceData.Location = new Point(26, 204);
            btnShowSourceData.Click += (s, e) => ShowSourceData();
            rightPanel.Controls.Add(btnShowSourceData);
        }
