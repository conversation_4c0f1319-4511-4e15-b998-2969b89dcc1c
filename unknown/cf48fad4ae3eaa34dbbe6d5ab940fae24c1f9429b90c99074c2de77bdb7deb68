<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView2测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #333;
            color: white;
            text-align: center;
            padding: 50px;
        }
        h1 {
            color: #00FFFF;
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: rgba(0, 0, 0, 0.3);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
        }
        button {
            background-color: #00FFFF;
            color: #333;
            border: none;
            padding: 10px 20px;
            font-size: 1.1rem;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        button:hover {
            background-color: #00CCCC;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebView2测试页面</h1>
        <p>如果您能看到这个页面，说明WebView2已经成功初始化并且可以正常工作！</p>
        <p>这是一个简单的测试页面，用于验证WebView2的功能。</p>
        <p>当前时间: <span id="current-time"></span></p>
        <button id="test-button">测试交互</button>
        <p id="result"></p>
    </div>

    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        }
        
        // 每秒更新一次时间
        updateTime();
        setInterval(updateTime, 1000);
        
        // 测试按钮点击事件
        document.getElementById('test-button').addEventListener('click', function() {
            const result = document.getElementById('result');
            result.textContent = '按钮点击成功！JavaScript正常工作。';
            
            // 检查是否在WebView2环境中运行
            if (window.chrome && window.chrome.webview) {
                // 向C#发送消息
                window.chrome.webview.postMessage({
                    action: 'test',
                    message: '这是从JavaScript发送到C#的测试消息'
                });
                
                result.textContent += ' WebView2消息通道正常工作。';
            } else {
                result.textContent += ' 注意：未检测到WebView2环境。';
            }
        });
    </script>
</body>
</html>
