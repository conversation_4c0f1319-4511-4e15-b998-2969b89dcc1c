using System;
using System.Collections.Generic;
using System.IO;
using BritSystem.Models;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace BritSystem.Services
{
    /// <summary>
    /// 数据导出服务
    /// </summary>
    public class ExportService
    {
        /// <summary>
        /// 导出数据点到Excel文件
        /// </summary>
        /// <param name="dataPoints">数据点列表</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否导出成功</returns>
        public bool ExportToExcel(List<BrittlenessDataPoint> dataPoints, string filePath)
        {
            try
            {
                if (dataPoints == null || dataPoints.Count == 0)
                {
                    throw new ArgumentException("没有数据可导出");
                }

                // 创建工作簿和工作表
                XSSFWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("脆性指数计算结果");

                // 创建标题行
                IRow headerRow = sheet.CreateRow(0);
                headerRow.CreateCell(0).SetCellValue("GeoID");
                headerRow.CreateCell(1).SetCellValue("顶深/m");
                headerRow.CreateCell(2).SetCellValue("底深/m");
                headerRow.CreateCell(3).SetCellValue("脆性指数");
                headerRow.CreateCell(4).SetCellValue("脆性矿物");
                headerRow.CreateCell(5).SetCellValue("塑性矿物");
                headerRow.CreateCell(6).SetCellValue("矿物成分");

                // 创建单元格样式
                ICellStyle numberStyle = workbook.CreateCellStyle();
                IDataFormat format = workbook.CreateDataFormat();
                numberStyle.DataFormat = format.GetFormat("0.00");

                ICellStyle percentStyle = workbook.CreateCellStyle();
                percentStyle.DataFormat = format.GetFormat("0.00%");

                // 填充数据
                for (int i = 0; i < dataPoints.Count; i++)
                {
                    BrittlenessDataPoint point = dataPoints[i];
                    IRow row = sheet.CreateRow(i + 1);

                    // 设置单元格值
                    row.CreateCell(0).SetCellValue(point.GeoID);

                    // 设置数字格式单元格
                    ICell topDepthCell = row.CreateCell(1);
                    topDepthCell.SetCellValue(point.TopDepth);
                    topDepthCell.CellStyle = numberStyle;

                    ICell bottomDepthCell = row.CreateCell(2);
                    bottomDepthCell.SetCellValue(point.BottomDepth);
                    bottomDepthCell.CellStyle = numberStyle;

                    // 设置百分比格式单元格
                    ICell brittleIndexCell = row.CreateCell(3);
                    brittleIndexCell.SetCellValue(point.BrittleIndex / 100.0); // 转换为小数以便于百分比格式化
                    brittleIndexCell.CellStyle = percentStyle;

                    // 设置文本单元格
                    row.CreateCell(4).SetCellValue(string.Join(", ", point.BrittleMinerals));
                    row.CreateCell(5).SetCellValue(string.Join(", ", point.DuctileMinerals));

                    // 设置矿物成分列
                    string mineralComposition = $"{string.Join(", ", point.BrittleMinerals)}; {string.Join(", ", point.DuctileMinerals)}";
                    row.CreateCell(6).SetCellValue(mineralComposition);
                }

                // 自动调整列宽
                for (int i = 0; i < 7; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出Excel失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 保存计算日志到文件
        /// </summary>
        /// <param name="dataPoints">数据点列表</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否保存成功</returns>
        public bool SaveCalculationLog(
            List<BrittlenessDataPoint> dataPoints,
            List<string> brittleColumns,
            List<string> ductileColumns,
            string filePath)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(filePath))
                {
                    writer.WriteLine("脆性指数计算日志");
                    writer.WriteLine($"计算时间: {DateTime.Now}");
                    writer.WriteLine();

                    writer.WriteLine("脆性矿物列:");
                    foreach (string col in brittleColumns)
                    {
                        writer.WriteLine($"- {col}");
                    }
                    writer.WriteLine();

                    writer.WriteLine("塑性矿物列:");
                    foreach (string col in ductileColumns)
                    {
                        writer.WriteLine($"- {col}");
                    }
                    writer.WriteLine();

                    writer.WriteLine("计算结果:");
                    writer.WriteLine("GeoID\t顶深/m\t底深/m\t脆性指数");
                    foreach (var point in dataPoints)
                    {
                        writer.WriteLine($"{point.GeoID}\t{point.TopDepth:F2}\t{point.BottomDepth:F2}\t{point.BrittleIndex:F2}%");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存计算日志失败: {ex.Message}");
                return false;
            }
        }
    }
}