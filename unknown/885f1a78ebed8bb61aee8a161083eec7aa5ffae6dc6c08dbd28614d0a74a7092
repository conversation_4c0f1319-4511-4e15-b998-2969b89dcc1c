using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Diagnostics;

namespace BritSystem.Core
{
    /// <summary>
    /// 脆性指数计算器类，负责处理所有与脆性指数计算相关的功能
    /// </summary>
    public class BrittlenessCalculator
    {
        #region 私有字段

        private readonly DataTable _sourceData;
        private readonly List<string> _brittleColumns;
        private readonly List<string> _ductileColumns;
        private readonly int _topDepthIndex;
        private readonly int _bottomDepthIndex;
        private readonly string _topDepthColumnName;
        private readonly string _bottomDepthColumnName;
        private readonly Dictionary<string, string> _columnMappings;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化脆性指数计算器类
        /// </summary>
        /// <param name="sourceData">原始数据表</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        /// <param name="topDepthIndex">顶深列索引</param>
        /// <param name="bottomDepthIndex">底深列索引</param>
        /// <param name="topDepthColumnName">顶深列名称</param>
        /// <param name="bottomDepthColumnName">底深列名称</param>
        /// <param name="columnMappings">列名映射</param>
        public BrittlenessCalculator(
            DataTable sourceData,
            List<string> brittleColumns,
            List<string> ductileColumns,
            int topDepthIndex,
            int bottomDepthIndex,
            string topDepthColumnName,
            string bottomDepthColumnName,
            Dictionary<string, string> columnMappings)
        {
            _sourceData = sourceData ?? throw new ArgumentNullException(nameof(sourceData));
            _brittleColumns = brittleColumns ?? new List<string>();
            _ductileColumns = ductileColumns ?? new List<string>();
            _topDepthIndex = topDepthIndex;
            _bottomDepthIndex = bottomDepthIndex;
            _topDepthColumnName = topDepthColumnName;
            _bottomDepthColumnName = bottomDepthColumnName;
            _columnMappings = columnMappings ?? new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 计算脆性指数
        /// </summary>
        /// <returns>计算结果表</returns>
        public DataTable CalculateBrittlenessIndex()
        {
            Debug.WriteLine("开始计算脆性指数...");
            
            // 创建结果表
            DataTable resultTable = CreateResultTable();
            
            // 验证数据和列选择
            if (!ValidateData())
            {
                Debug.WriteLine("数据验证失败");
                return resultTable;
            }
            
            // 逐行计算脆性指数
            foreach (DataRow sourceRow in _sourceData.Rows)
            {
                try
                {
                    // 计算当前行的脆性指数
                    double brittleIndex = CalculateRowBrittlenessIndex(sourceRow, out double brittleTotal, out double ductileTotal);
                    
                    // 创建结果行
                    DataRow resultRow = resultTable.NewRow();
                    
                    // 设置基本信息
                    SetBasicRowInfo(resultRow, sourceRow, brittleIndex, brittleTotal, ductileTotal);
                    
                    // 复制原始矿物数据
                    CopyMineralData(resultRow, sourceRow);
                    
                    // 添加到结果表
                    resultTable.Rows.Add(resultRow);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"处理行时出错: {ex.Message}");
                }
            }
            
            Debug.WriteLine($"脆性指数计算完成，共有 {resultTable.Rows.Count} 行结果");
            return resultTable;
        }

        /// <summary>
        /// 计算单行数据的脆性指数
        /// </summary>
        /// <param name="row">数据行</param>
        /// <returns>脆性指数值</returns>
        public double CalculateSingleRowBrittlenessIndex(DataRow row)
        {
            double brittleIndex = CalculateRowBrittlenessIndex(row, out _, out _);
            return brittleIndex;
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 创建结果表结构
        /// </summary>
        /// <returns>初始化的结果表</returns>
        private DataTable CreateResultTable()
        {
            DataTable resultTable = new DataTable("ResultData");
            
            // 添加基本列
            resultTable.Columns.Add("GeoID", typeof(string));
            resultTable.Columns.Add("顶深/m", typeof(double));
            resultTable.Columns.Add("底深/m", typeof(double));
            resultTable.Columns.Add("脆性指数", typeof(double));
            resultTable.Columns.Add("脆性矿物总量", typeof(double));
            resultTable.Columns.Add("塑性矿物总量", typeof(double));
            
            // 添加脆性矿物列
            foreach (string columnName in _brittleColumns)
            {
                string actualColumnName = FindActualColumnName(columnName);
                if (!string.IsNullOrEmpty(actualColumnName) && !resultTable.Columns.Contains(actualColumnName))
                {
                    resultTable.Columns.Add(actualColumnName, typeof(double));
                }
            }
            
            // 添加塑性矿物列
            foreach (string columnName in _ductileColumns)
            {
                string actualColumnName = FindActualColumnName(columnName);
                if (!string.IsNullOrEmpty(actualColumnName) && !resultTable.Columns.Contains(actualColumnName))
                {
                    resultTable.Columns.Add(actualColumnName, typeof(double));
                }
            }
            
            return resultTable;
        }

        /// <summary>
        /// 验证数据和列选择是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        private bool ValidateData()
        {
            // 验证源数据表是否有效
            if (_sourceData == null || _sourceData.Rows.Count == 0)
            {
                Debug.WriteLine("源数据表为空或没有行");
                return false;
            }
            
            // 验证脆性和塑性矿物列是否选择
            if (_brittleColumns.Count == 0 && _ductileColumns.Count == 0)
            {
                Debug.WriteLine("未选择脆性和塑性矿物列");
                return false;
            }
            
            // 验证顶深和底深列是否有效
            if (_topDepthIndex < 0 || _bottomDepthIndex < 0)
            {
                Debug.WriteLine("顶深或底深列索引无效");
                return false;
            }
            
            return true;
        }

        /// <summary>
        /// 计算一行数据的脆性指数
        /// </summary>
        /// <param name="sourceRow">源数据行</param>
        /// <param name="brittleTotal">计算的脆性矿物总量</param>
        /// <param name="ductileTotal">计算的塑性矿物总量</param>
        /// <returns>脆性指数</returns>
        private double CalculateRowBrittlenessIndex(DataRow sourceRow, out double brittleTotal, out double ductileTotal)
        {
            // 初始化总量
            brittleTotal = 0;
            ductileTotal = 0;
            
            // 计算脆性矿物总量
            foreach (string columnName in _brittleColumns)
            {
                string actualColumnName = FindActualColumnName(columnName);
                if (string.IsNullOrEmpty(actualColumnName))
                    continue;
                    
                if (_sourceData.Columns.Contains(actualColumnName) && sourceRow[actualColumnName] != DBNull.Value)
                {
                    double value = SafeParseDouble(sourceRow[actualColumnName]);
                    brittleTotal += value;
                }
            }
            
            // 计算塑性矿物总量
            foreach (string columnName in _ductileColumns)
            {
                string actualColumnName = FindActualColumnName(columnName);
                if (string.IsNullOrEmpty(actualColumnName))
                    continue;
                    
                if (_sourceData.Columns.Contains(actualColumnName) && sourceRow[actualColumnName] != DBNull.Value)
                {
                    double value = SafeParseDouble(sourceRow[actualColumnName]);
                    ductileTotal += value;
                }
            }
            
            // 计算脆性指数
            double brittlenessIndex = 0;
            if (brittleTotal + ductileTotal > 0)
            {
                brittlenessIndex = brittleTotal / (brittleTotal + ductileTotal);
            }
            
            return brittlenessIndex;
        }

        /// <summary>
        /// 设置结果行的基本信息
        /// </summary>
        /// <param name="resultRow">结果行</param>
        /// <param name="sourceRow">源数据行</param>
        /// <param name="brittleIndex">脆性指数</param>
        /// <param name="brittleTotal">脆性矿物总量</param>
        /// <param name="ductileTotal">塑性矿物总量</param>
        private void SetBasicRowInfo(DataRow resultRow, DataRow sourceRow, double brittleIndex, double brittleTotal, double ductileTotal)
        {
            // 设置GeoID (可以是行索引或其他标识)
            resultRow["GeoID"] = _sourceData.Rows.IndexOf(sourceRow).ToString();
            
            // 设置顶深和底深
            if (_topDepthIndex >= 0 && _topDepthIndex < _sourceData.Columns.Count)
            {
                resultRow["顶深/m"] = SafeParseDouble(sourceRow[_topDepthIndex]);
            }
            
            if (_bottomDepthIndex >= 0 && _bottomDepthIndex < _sourceData.Columns.Count)
            {
                resultRow["底深/m"] = SafeParseDouble(sourceRow[_bottomDepthIndex]);
            }
            
            // 设置脆性指数和矿物总量
            resultRow["脆性指数"] = brittleIndex;
            resultRow["脆性矿物总量"] = brittleTotal;
            resultRow["塑性矿物总量"] = ductileTotal;
        }

        /// <summary>
        /// 复制矿物数据
        /// </summary>
        /// <param name="resultRow">结果行</param>
        /// <param name="sourceRow">源数据行</param>
        private void CopyMineralData(DataRow resultRow, DataRow sourceRow)
        {
            // 复制脆性矿物数据
            foreach (string columnName in _brittleColumns)
            {
                string actualColumnName = FindActualColumnName(columnName);
                if (!string.IsNullOrEmpty(actualColumnName) && 
                    _sourceData.Columns.Contains(actualColumnName) && 
                    resultRow.Table.Columns.Contains(actualColumnName))
                {
                    resultRow[actualColumnName] = sourceRow[actualColumnName] != DBNull.Value ? 
                        SafeParseDouble(sourceRow[actualColumnName]) : 0;
                }
            }
            
            // 复制塑性矿物数据
            foreach (string columnName in _ductileColumns)
            {
                string actualColumnName = FindActualColumnName(columnName);
                if (!string.IsNullOrEmpty(actualColumnName) && 
                    _sourceData.Columns.Contains(actualColumnName) && 
                    resultRow.Table.Columns.Contains(actualColumnName))
                {
                    resultRow[actualColumnName] = sourceRow[actualColumnName] != DBNull.Value ? 
                        SafeParseDouble(sourceRow[actualColumnName]) : 0;
                }
            }
        }

        /// <summary>
        /// 查找实际的列名
        /// </summary>
        /// <param name="columnName">原始列名</param>
        /// <returns>实际列名</returns>
        private string FindActualColumnName(string columnName)
        {
            // 如果列名直接存在于源数据表中，直接返回
            if (_sourceData.Columns.Contains(columnName))
            {
                return columnName;
            }
            
            // 检查是否存在映射
            if (_columnMappings.TryGetValue(columnName, out string mappedName))
            {
                if (_sourceData.Columns.Contains(mappedName))
                {
                    return mappedName;
                }
            }
            
            // 尝试智能匹配
            foreach (DataColumn column in _sourceData.Columns)
            {
                if (column.ColumnName.Contains(columnName, StringComparison.OrdinalIgnoreCase))
                {
                    return column.ColumnName;
                }
            }
            
            return null;
        }

        /// <summary>
        /// 安全解析双精度浮点数
        /// </summary>
        /// <param name="value">需要解析的值</param>
        /// <returns>解析后的浮点数</returns>
        private double SafeParseDouble(object value)
        {
            if (value == null || value == DBNull.Value)
            {
                return 0;
            }

            // 如果本身就是数值类型，直接转换
            if (value is double doubleValue)
            {
                return doubleValue;
            }
            if (value is int intValue)
            {
                return intValue;
            }
            if (value is decimal decimalValue)
            {
                return (double)decimalValue;
            }
            if (value is float floatValue)
            {
                return floatValue;
            }

            // 尝试从字符串转换
            string stringValue = value.ToString().Trim();

            // 处理百分比符号
            if (stringValue.EndsWith("%"))
            {
                stringValue = stringValue.TrimEnd('%');
                if (double.TryParse(stringValue, out double percentValue))
                {
                    return percentValue;
                }
            }

            // 处理科学计数法
            if (stringValue.Contains("×10") || stringValue.Contains("x10"))
            {
                stringValue = stringValue
                    .Replace("×10^", "e")
                    .Replace("×10-", "e-")
                    .Replace("×10", "e")
                    .Replace("x10^", "e")
                    .Replace("x10-", "e-")
                    .Replace("x10", "e");
            }

            // 尝试直接解析
            if (double.TryParse(stringValue, out double result))
            {
                return result;
            }

            return 0;
        }

        #endregion
    }
} 