@echo off
setlocal enabledelayedexpansion

echo 矿物脆性指数分析系统 - 安装包生成工具 (修复版)
echo =======================================
echo.

REM 设置颜色
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

REM 创建输出目录
if not exist "Output" (
    echo %BLUE%创建输出目录...%RESET%
    mkdir "Output"
)

REM 查找 Inno Setup 编译器
echo %BLUE%查找 Inno Setup 编译器...%RESET%
set "ISCC_PATH="

REM 尝试查找 Inno Setup 6
if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
    echo %GREEN%找到 Inno Setup 6 (32位)%RESET%
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
    echo %GREEN%找到 Inno Setup 6 (64位)%RESET%
)

REM 尝试查找 Inno Setup 5
if "%ISCC_PATH%"=="" (
    if exist "%ProgramFiles(x86)%\Inno Setup 5\ISCC.exe" (
        set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 5\ISCC.exe"
        echo %YELLOW%找到 Inno Setup 5 (32位) - 建议升级到 Inno Setup 6%RESET%
    ) else if exist "%ProgramFiles%\Inno Setup 5\ISCC.exe" (
        set "ISCC_PATH=%ProgramFiles%\Inno Setup 5\ISCC.exe"
        echo %YELLOW%找到 Inno Setup 5 (64位) - 建议升级到 Inno Setup 6%RESET%
    )
)

REM 如果找不到 Inno Setup，提示用户手动输入路径
if "%ISCC_PATH%"=="" (
    echo %RED%未找到 Inno Setup。%RESET%
    echo 请输入 ISCC.exe 的完整路径:
    set /p ISCC_PATH=
    
    if not exist "!ISCC_PATH!" (
        echo %RED%错误: 指定的路径不存在。%RESET%
        echo 请安装 Inno Setup 6 后再试。
        echo 下载地址: https://jrsoftware.org/isdl.php
        pause
        exit /b 1
    )
)

REM 检查 Release 构建是否存在
echo %BLUE%检查 Release 构建...%RESET%
set "RELEASE_PATH=bin\Release\net8.0-windows"
set "EXE_PATH=%RELEASE_PATH%\BritSystem.exe"

if not exist "%EXE_PATH%" (
    echo %RED%警告: Release 构建未找到。%RESET%
    echo 是否要先构建项目? (Y/N, 默认=Y):
    set /p BUILD_PROJECT=
    
    if not defined BUILD_PROJECT set "BUILD_PROJECT=Y"
    
    if /i "!BUILD_PROJECT!"=="Y" (
        echo %BLUE%正在构建项目...%RESET%
        dotnet build -c Release
        
        if !ERRORLEVEL! NEQ 0 (
            echo %RED%构建失败。请检查错误信息。%RESET%
            pause
            exit /b 1
        )
        
        if not exist "%EXE_PATH%" (
            echo %RED%构建完成，但可执行文件未找到。%RESET%
            echo 请检查构建输出路径是否正确。
            pause
            exit /b 1
        )
        
        echo %GREEN%构建成功。%RESET%
    ) else (
        echo %YELLOW%跳过构建。继续尝试打包...%RESET%
    )
)

REM 检查依赖项
echo %BLUE%检查依赖项...%RESET%
set "MISSING_DEPS=0"

REM 检查 WebView2 相关文件
if not exist "%RELEASE_PATH%\runtimes\win-x64\native\WebView2Loader.dll" (
    echo %RED%警告: WebView2Loader.dll (x64) 未找到%RESET%
    set /a MISSING_DEPS+=1
)

if not exist "%RELEASE_PATH%\Microsoft.Web.WebView2.Core.dll" (
    echo %RED%警告: Microsoft.Web.WebView2.Core.dll 未找到%RESET%
    set /a MISSING_DEPS+=1
)

REM 检查 HTML/CSS 文件
if not exist "%RELEASE_PATH%\index.html" (
    echo %YELLOW%警告: index.html 未找到%RESET%
    set /a MISSING_DEPS+=1
)

if !MISSING_DEPS! GTR 0 (
    echo %YELLOW%发现 !MISSING_DEPS! 个缺失的依赖项。继续打包可能导致安装后程序无法正常运行。%RESET%
    echo 是否继续? (Y/N, 默认=N):
    set /p CONTINUE_WITH_MISSING=
    
    if not defined CONTINUE_WITH_MISSING set "CONTINUE_WITH_MISSING=N"
    
    if /i "!CONTINUE_WITH_MISSING!"=="N" (
        echo %RED%已取消。请确保所有依赖项都存在。%RESET%
        pause
        exit /b 1
    )
)

REM 选择 ISS 文件
set "ISS_FILE=BritSystemSetup.iss"
if exist "BritSystemSetup_Fixed.iss" (
    set "ISS_FILE=BritSystemSetup_Fixed.iss"
    echo %GREEN%使用修复版 ISS 文件: !ISS_FILE!%RESET%
) else (
    echo %YELLOW%使用标准 ISS 文件: !ISS_FILE!%RESET%
)

REM 编译安装程序
echo %BLUE%正在编译安装程序...%RESET%
echo 输出目录: %CD%\Output
echo 使用 ISS 文件: !ISS_FILE!

REM 运行 Inno Setup 编译器
echo.
echo %BLUE%运行 Inno Setup 编译器...%RESET%
"%ISCC_PATH%" "!ISS_FILE!" /O"Output"

REM 检查结果
if %ERRORLEVEL% EQU 0 (
    echo %GREEN%构建成功!%RESET%
    if exist "Output\BritSystemSetup.exe" (
        echo %GREEN%安装程序位置: %CD%\Output\BritSystemSetup.exe%RESET%
        
        REM 打开输出目录
        start "" "Output"
    ) else (
        echo %YELLOW%警告: 安装程序未在预期位置找到。%RESET%
        echo 预期路径: %CD%\Output\BritSystemSetup.exe
    )
) else (
    echo %RED%构建失败。请检查错误信息。%RESET%
    echo.
    echo %YELLOW%常见问题:%RESET%
    echo 1. 源文件未找到 - 确保您已在 Release 模式下构建项目
    echo 2. 权限问题 - 确保您有写入输出目录的权限
    echo 3. 路径问题 - 避免路径中包含特殊字符或空格
    echo 4. 中文路径问题 - 避免路径中包含中文字符
    echo 5. ISS 文件语法错误 - 检查 !ISS_FILE! 文件
)

echo.
echo %BLUE%打包过程完成。%RESET%
pause
