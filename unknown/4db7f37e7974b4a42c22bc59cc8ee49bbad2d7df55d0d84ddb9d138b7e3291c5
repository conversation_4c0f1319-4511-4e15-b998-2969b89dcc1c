using System;
using System.Windows.Forms;
using System.Drawing;
using System.Collections.Generic;

namespace NativeFormTest
{
    public class DashboardForm : Form
    {
        private Label lblTitle;
        private Label lblWelcome;
        private Button btnMineralogical;
        private Button btnLogout;
        private Panel pnlStats;
        private Panel pnlActivities;
        private string username;

        public DashboardForm(string username)
        {
            this.username = username;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            // 窗体设置
            this.Text = "脆性指数系统 - 控制面板";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(33, 33, 33); // 深色背景

            // 标题
            lblTitle = new Label();
            lblTitle.Text = "脆性指数系统 - 控制面板";
            lblTitle.Font = new Font("Microsoft YaHei", 20, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Height = 50;
            lblTitle.Padding = new Padding(0, 10, 0, 0);

            // 欢迎信息
            lblWelcome = new Label();
            lblWelcome.Text = $"欢迎, {username}";
            lblWelcome.Font = new Font("Microsoft YaHei", 12);
            lblWelcome.ForeColor = Color.White;
            lblWelcome.TextAlign = ContentAlignment.MiddleLeft;
            lblWelcome.Location = new Point(20, 60);
            lblWelcome.Size = new Size(300, 30);

            // 矿物组分法按钮
            btnMineralogical = new Button();
            btnMineralogical.Text = "矿物组分法";
            btnMineralogical.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnMineralogical.Location = new Point(20, 100);
            btnMineralogical.Size = new Size(150, 40);
            btnMineralogical.BackColor = Color.FromArgb(0, 120, 212);
            btnMineralogical.ForeColor = Color.White;
            btnMineralogical.FlatStyle = FlatStyle.Flat;
            btnMineralogical.Click += BtnMineralogical_Click;

            // 退出登录按钮
            btnLogout = new Button();
            btnLogout.Text = "退出登录";
            btnLogout.Font = new Font("Microsoft YaHei", 10);
            btnLogout.Location = new Point(this.ClientSize.Width - 150 - 20, 100);
            btnLogout.Size = new Size(150, 40);
            btnLogout.BackColor = Color.FromArgb(153, 153, 153);
            btnLogout.ForeColor = Color.White;
            btnLogout.FlatStyle = FlatStyle.Flat;
            btnLogout.Click += BtnLogout_Click;

            // 统计数据面板
            pnlStats = new Panel();
            pnlStats.Location = new Point(20, 160);
            pnlStats.Size = new Size(this.ClientSize.Width - 40, 150);
            pnlStats.BackColor = Color.FromArgb(45, 45, 45);
            pnlStats.BorderStyle = BorderStyle.FixedSingle;

            // 活动记录面板
            pnlActivities = new Panel();
            pnlActivities.Location = new Point(20, 330);
            pnlActivities.Size = new Size(this.ClientSize.Width - 40, this.ClientSize.Height - 350);
            pnlActivities.BackColor = Color.FromArgb(45, 45, 45);
            pnlActivities.BorderStyle = BorderStyle.FixedSingle;
            pnlActivities.AutoScroll = true;

            // 添加控件到窗体
            this.Controls.Add(lblTitle);
            this.Controls.Add(lblWelcome);
            this.Controls.Add(btnMineralogical);
            this.Controls.Add(btnLogout);
            this.Controls.Add(pnlStats);
            this.Controls.Add(pnlActivities);

            // 窗体大小调整事件
            this.Resize += DashboardForm_Resize;
        }

        private void DashboardForm_Resize(object sender, EventArgs e)
        {
            // 调整控件位置和大小
            btnLogout.Location = new Point(this.ClientSize.Width - 150 - 20, 100);
            pnlStats.Size = new Size(this.ClientSize.Width - 40, 150);
            pnlActivities.Size = new Size(this.ClientSize.Width - 40, this.ClientSize.Height - 350);
        }

        private void LoadData()
        {
            // 加载统计数据
            LoadStats();

            // 加载活动记录
            LoadActivities();
        }

        private void LoadStats()
        {
            // 清空面板
            pnlStats.Controls.Clear();

            // 添加标题
            Label lblStatsTitle = new Label();
            lblStatsTitle.Text = "系统统计";
            lblStatsTitle.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblStatsTitle.ForeColor = Color.White;
            lblStatsTitle.Location = new Point(10, 10);
            lblStatsTitle.Size = new Size(200, 25);
            pnlStats.Controls.Add(lblStatsTitle);

            // 统计数据
            var stats = new[]
            {
                new { value = "1,234", description = "条记录" },
                new { value = "5", description = "个进行中" },
                new { value = "42", description = "份已生成" },
                new { value = "正常", description = "运行良好" }
            };

            // 添加统计数据
            int startX = 10;
            int width = (pnlStats.Width - 20) / stats.Length;
            for (int i = 0; i < stats.Length; i++)
            {
                Panel statPanel = new Panel();
                statPanel.Location = new Point(startX + i * width, 40);
                statPanel.Size = new Size(width - 10, 80);
                statPanel.BackColor = Color.FromArgb(55, 55, 55);

                Label lblValue = new Label();
                lblValue.Text = stats[i].value;
                lblValue.Font = new Font("Microsoft YaHei", 16, FontStyle.Bold);
                lblValue.ForeColor = Color.Cyan;
                lblValue.TextAlign = ContentAlignment.MiddleCenter;
                lblValue.Dock = DockStyle.Top;
                lblValue.Height = 40;

                Label lblDescription = new Label();
                lblDescription.Text = stats[i].description;
                lblDescription.Font = new Font("Microsoft YaHei", 10);
                lblDescription.ForeColor = Color.White;
                lblDescription.TextAlign = ContentAlignment.MiddleCenter;
                lblDescription.Dock = DockStyle.Bottom;
                lblDescription.Height = 30;

                statPanel.Controls.Add(lblValue);
                statPanel.Controls.Add(lblDescription);
                pnlStats.Controls.Add(statPanel);
            }
        }

        private void LoadActivities()
        {
            // 清空面板
            pnlActivities.Controls.Clear();

            // 添加标题
            Label lblActivitiesTitle = new Label();
            lblActivitiesTitle.Text = "最近活动";
            lblActivitiesTitle.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblActivitiesTitle.ForeColor = Color.White;
            lblActivitiesTitle.Location = new Point(10, 10);
            lblActivitiesTitle.Size = new Size(200, 25);
            pnlActivities.Controls.Add(lblActivitiesTitle);

            // 活动记录
            var activities = new[]
            {
                new { time = DateTime.Now.AddHours(-1).ToString("yyyy-MM-dd HH:mm"), action = "数据导入", user = "admin", status = "success", statusText = "成功" },
                new { time = DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm"), action = "指数计算", user = "admin", status = "success", statusText = "成功" },
                new { time = DateTime.Now.AddHours(-5).ToString("yyyy-MM-dd HH:mm"), action = "报告生成", user = "admin", status = "success", statusText = "成功" },
                new { time = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd HH:mm"), action = "系统备份", user = "admin", status = "success", statusText = "成功" }
            };

            // 添加表头
            Panel headerPanel = new Panel();
            headerPanel.Location = new Point(10, 40);
            headerPanel.Size = new Size(pnlActivities.Width - 30, 30);
            headerPanel.BackColor = Color.FromArgb(60, 60, 60);

            string[] headers = { "时间", "操作", "用户", "状态" };
            int[] widths = { 200, 200, 150, 150 };
            int startX = 10;

            for (int i = 0; i < headers.Length; i++)
            {
                Label lblHeader = new Label();
                lblHeader.Text = headers[i];
                lblHeader.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
                lblHeader.ForeColor = Color.White;
                lblHeader.TextAlign = ContentAlignment.MiddleLeft;
                lblHeader.Location = new Point(startX, 0);
                lblHeader.Size = new Size(widths[i], 30);
                headerPanel.Controls.Add(lblHeader);
                startX += widths[i];
            }

            pnlActivities.Controls.Add(headerPanel);

            // 添加活动记录
            for (int i = 0; i < activities.Length; i++)
            {
                Panel activityPanel = new Panel();
                activityPanel.Location = new Point(10, 70 + i * 40);
                activityPanel.Size = new Size(pnlActivities.Width - 30, 40);
                activityPanel.BackColor = i % 2 == 0 ? Color.FromArgb(50, 50, 50) : Color.FromArgb(55, 55, 55);

                startX = 10;
                string[] values = { activities[i].time, activities[i].action, activities[i].user, activities[i].statusText };

                for (int j = 0; j < values.Length; j++)
                {
                    Label lblValue = new Label();
                    lblValue.Text = values[j];
                    lblValue.Font = new Font("Microsoft YaHei", 10);
                    lblValue.ForeColor = j == 3 && activities[i].status == "success" ? Color.LightGreen : Color.White;
                    lblValue.TextAlign = ContentAlignment.MiddleLeft;
                    lblValue.Location = new Point(startX, 0);
                    lblValue.Size = new Size(widths[j], 40);
                    activityPanel.Controls.Add(lblValue);
                    startX += widths[j];
                }

                pnlActivities.Controls.Add(activityPanel);
            }
        }

        private void BtnMineralogical_Click(object sender, EventArgs e)
        {
            // 打开矿物组分法窗体
            MineralogicalForm mineralogicalForm = new MineralogicalForm(username);
            this.Hide();
            DialogResult result = mineralogicalForm.ShowDialog();
            if (result == DialogResult.OK)
            {
                this.Show();
            }
            else
            {
                this.Close();
            }
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            // 退出登录
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
