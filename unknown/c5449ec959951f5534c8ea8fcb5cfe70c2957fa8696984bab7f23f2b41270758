private void MineralChart_MouseClick(object sender, MouseEventArgs e)
{
    try
    {
        if (chartBrittleness == null || chartBrittleness.Series.Count < 2 || dataPoints.Count == 0)
            return;

        // 获取点击位置对应的所有数据点
        var results = chartBrittleness.HitTest(e.X, e.Y, false, ChartElementType.DataPoint);

        // 输出调试信息
        System.Diagnostics.Debug.WriteLine($"点击图表: 位置=({e.X}, {e.Y}), 结果数量={results.Count()}");

        // 如果没有点击到任何点，直接返回
        if (results.Count() == 0)
        {
            System.Diagnostics.Debug.WriteLine("未点击到任何数据点");
            return;
        }

        // 获取点击的数据点
        var result = results.FirstOrDefault(r => r.ChartElementType == ChartElementType.DataPoint);
        if (result == null || result.PointIndex < 0 || result.Series == null)
        {
            System.Diagnostics.Debug.WriteLine("未找到有效的数据点");
            return;
        }

        // 获取点击的图表点的坐标
        var chartPoint = result.Series.Points[result.PointIndex];
        double brittleIndex = chartPoint.XValue;
        double topDepth = chartPoint.YValues[0];

        System.Diagnostics.Trace.WriteLine($"点击的图表点: 脆性指数={brittleIndex:F2}, 深度={topDepth:F2}");

        // 输出所有数据点信息以便调试
        System.Diagnostics.Trace.WriteLine($"当前数据点总数: {dataPoints.Count}");
        System.Diagnostics.Trace.WriteLine($"点击点脆性指数: {brittleIndex:F6}, 深度: {topDepth:F6}");
        
        // 直接使用图表点的索引查找对应的数据点
        DataPoint matchingDataPoint = null;

        // 首先尝试直接使用图表点索引匹配
        if (result.Series.Name == "矿物点" && result.PointIndex < dataPoints.Count)
        {
            // 直接使用图表点索引获取数据点
            matchingDataPoint = dataPoints[result.PointIndex];
            System.Diagnostics.Trace.WriteLine($"使用图表点索引匹配点: 索引={result.PointIndex}, 脆性指数={matchingDataPoint.BrittleIndex:F6}");
        }
        // 如果点击的是其他系列（如高亮点），尝试通过脆性指数值查找
        else if (result.Series.Name.StartsWith("Highlight") || result.Series.Name.Contains("选中点"))
        {
            // 尝试通过脆性指数精确匹配
            matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.01);
            if (matchingDataPoint != null)
            {
                System.Diagnostics.Debug.WriteLine($"通过脆性指数精确匹配找到数据点: 行索引={matchingDataPoint.RowIndex}, 脆性指数={matchingDataPoint.BrittleIndex:F2}");
            }
        }
        
        // 如果上述方法均未找到匹配的数据点，尝试更复杂的匹配策略
        if (matchingDataPoint == null)
        {
            // 1. 首先尝试精确匹配脆性指数和深度
            matchingDataPoint = dataPoints.FirstOrDefault(p =>
                Math.Abs(p.BrittleIndex - brittleIndex) < 0.001 &&
                Math.Abs(p.TopDepth - topDepth) < 0.001);

            if (matchingDataPoint != null)
            {
                System.Diagnostics.Debug.WriteLine($"策略 1: 精确匹配成功，行索引={matchingDataPoint.RowIndex}");
            }
            else
            {
                // 2. 尝试宽松匹配脆性指数和深度
                matchingDataPoint = dataPoints.FirstOrDefault(p =>
                    Math.Abs(p.BrittleIndex - brittleIndex) < 0.1 &&
                    Math.Abs(p.TopDepth - topDepth) < 0.1);

                if (matchingDataPoint != null)
                {
                    System.Diagnostics.Debug.WriteLine($"策略 2: 宽松匹配成功，行索引={matchingDataPoint.RowIndex}");
                }
                else
                {
                    // 3. 尝试只匹配脆性指数 - 对于脆性指数为100的点使用更精确的匹配
                    if (Math.Abs(brittleIndex - 100) < 0.01)
                    {
                        // 对于脆性指数为100的点，使用更精确的匹配
                        matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - 100) < 0.01);
                        System.Diagnostics.Debug.WriteLine($"特殊处理脆性指数为100的点，精确匹配结果: {(matchingDataPoint != null ? "成功" : "失败")}");
                    }
                    else
                    {
                        // 对于其他点，使用常规匹配
                        matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.1);
                    }

                    if (matchingDataPoint != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"策略 3: 只匹配脆性指数成功，行索引={matchingDataPoint.RowIndex}");
                    }
                    else
                    {
                        // 4. 尝试只匹配深度
                        matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.TopDepth - topDepth) < 0.1);

                        if (matchingDataPoint != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"策略 4: 只匹配深度成功，行索引={matchingDataPoint.RowIndex}");
                        }
                        else
                        {
                            // 5. 尝试找最接近的点
                            matchingDataPoint = dataPoints.OrderBy(p =>
                                Math.Pow(p.BrittleIndex - brittleIndex, 2) +
                                Math.Pow(p.TopDepth - topDepth, 2)).FirstOrDefault();

                            if (matchingDataPoint != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"策略 5: 找到最接近的数据点，行索引={matchingDataPoint.RowIndex}");
                            }
                        }
                    }
                }
            }
        }

        // 如果找到匹配的数据点，高亮显示对应的行
        if (matchingDataPoint != null)
        {
            // 清除当前选择
            selectedRows.Clear();

            // 只选中当前点击的行
            selectedRows.Add(matchingDataPoint.RowIndex);

            // 更新高亮显示
            UpdateHighlights();

            // 在图表中高亮显示对应的点
            HighlightChartPoints();

            // 选中数据表中对应的行
            // 修正行索引，确保在有效范围内
            int adjustedRowIndex = matchingDataPoint.RowIndex;
            if (adjustedRowIndex >= dgvMineralData.Rows.Count && dgvMineralData.Rows.Count > 0)
            {
                // 如果索引超出范围，使用最后一行的索引
                adjustedRowIndex = dgvMineralData.Rows.Count - 1;
                System.Diagnostics.Trace.WriteLine($"行索引调整: 原始索引={matchingDataPoint.RowIndex}, 调整后={adjustedRowIndex}");
            }

            if (dgvMineralData.Rows.Count > 0 && adjustedRowIndex >= 0)
            {
                try
                {
                    dgvMineralData.ClearSelection();
                    dgvMineralData.Rows[adjustedRowIndex].Selected = true;
                    dgvMineralData.CurrentCell = dgvMineralData.Rows[adjustedRowIndex].Cells[0];
                    dgvMineralData.FirstDisplayedScrollingRowIndex = adjustedRowIndex;

                    // 强制刷新选中状态
                    selectedRows.Clear();
                    selectedRows.Add(adjustedRowIndex);
                    UpdateHighlights();

                    // 特别处理脆性指数为100的点
                    if (Math.Abs(matchingDataPoint.BrittleIndex - 100) < 0.01)
                    {
                        System.Diagnostics.Trace.WriteLine($"特别处理脆性指数为100的点，确保高亮显示");
                        // 强制刷新数据表格
                        dgvMineralData.Refresh();
                    }

                    System.Diagnostics.Trace.WriteLine($"成功选中数据表行: {adjustedRowIndex}");

                    // 无论行索引是否调整，都显示点信息
                    MessageBox.Show($"顶深: {matchingDataPoint.TopDepth}m\n底深: {matchingDataPoint.BottomDepth}m\n脆性指数: {matchingDataPoint.BrittleIndex}%", "点信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Trace.WriteLine($"选中数据表行时出错: {ex.Message}");
                }
            }
            else
            {
                System.Diagnostics.Trace.WriteLine($"行索引 {adjustedRowIndex} 无效或数据表为空 (行数: {dgvMineralData.Rows.Count})");

                // 即使无法选中行，也显示点信息
                MessageBox.Show($"顶深: {matchingDataPoint.TopDepth}m\n底深: {matchingDataPoint.BottomDepth}m\n脆性指数: {matchingDataPoint.BrittleIndex}%", "点信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            // 输出调试信息
            System.Diagnostics.Debug.WriteLine($"选中数据点: 脆性指数={brittleIndex:F2}, 深度={topDepth:F2}, 行索引={matchingDataPoint.RowIndex}");
        }
        else
        {
            System.Diagnostics.Debug.WriteLine($"未找到匹配的数据点: 脆性指数={brittleIndex:F2}, 深度={topDepth:F2}");
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"鼠标点击事件处理出错: {ex.Message}");
        MessageBox.Show($"鼠标点击事件处理出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
