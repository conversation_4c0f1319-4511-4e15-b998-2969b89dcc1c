                    
                    // 检查是否成功添加数据行
                    if (rowsAdded == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("未添加任何数据行，尝试直接读取所有行");
                        
                        // 尝试直接读取所有行（不跳过任何行）
                        for (int i = 0; i <= sheet.LastRowNum; i++)
                        {
                            if (i == headerRowIndex) continue; // 跳过表头行
                            
                            IRow row = sheet.GetRow(i);
                            if (row == null) continue;
                            
                            try
                            {
                                DataRow dataRow = _sourceData.NewRow();
                                
                                for (int j = 0; j < Math.Min(row.LastCellNum, _sourceData.Columns.Count); j++)
                                {
                                    ICell cell = row.GetCell(j);
                                    if (cell != null)
                                    {
                                        try
                                        {
                                            // 尝试获取数值
                                            if (cell.CellType == CellType.Numeric)
                                            {
                                                dataRow[j] = cell.NumericCellValue;
                                            }
                                            else
                                            {
                                                string strValue = cell.ToString().Trim();
                                                if (double.TryParse(strValue, out double val))
                                                    dataRow[j] = val;
                                                else if (strValue == "√" || strValue == "✓")
                                                    dataRow[j] = 1.0;
                                                else
                                                    dataRow[j] = 0.0;
                                            }
                                        }
                                        catch
                                        {
                                            dataRow[j] = 0.0;
                                        }
                                    }
                                }
                                
                                // 检查行是否有有效数据
                                bool hasValidData = false;
                                foreach (var item in dataRow.ItemArray)
                                {
                                    if (item != DBNull.Value && item.ToString() != "0")
                                    {
                                        hasValidData = true;
                                        break;
                                    }
                                }
                                
                                if (hasValidData)
                                {
                                    _sourceData.Rows.Add(dataRow);
                                    rowsAdded++;
                                    System.Diagnostics.Debug.WriteLine($"直接添加了第 {i} 行数据");
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"直接添加第 {i} 行时出错: {ex.Message}");
                            }
                        }
                    }
                    
                    // 输出加载结果
                    System.Diagnostics.Debug.WriteLine($"成功加载 {_sourceData.Rows.Count} 行和 {_sourceData.Columns.Count} 列");
                    
                    // 如果仍然没有数据行，提示用户
                    if (_sourceData.Rows.Count == 0)
                    {
                        // 尝试手动添加数据行（从截图中提取）
                        DialogResult result = MessageBox.Show(
                            "未能从Excel文件中读取数据行。是否添加截图中的示例数据？", 
                            "提示", 
                            MessageBoxButtons.YesNo, 
                            MessageBoxIcon.Question);
                        
                        if (result == DialogResult.Yes)
                        {
                            // 添加截图中的数据
                            AddSampleDataFromScreenshot();
                        }
                        else
                        {
                            MessageBox.Show("请确保Excel文件格式正确，或尝试使用CSV格式。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    
                    // 显示源数据
                    dgvResult.DataSource = _sourceData;
                    
                    // 更新结果标签
                    resultLabel.Text = $"源数据: 共{_sourceData.Rows.Count}行, {_sourceData.Columns.Count}列";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载Excel数据时出错: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"加载Excel数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
