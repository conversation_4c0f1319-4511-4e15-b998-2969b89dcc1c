/// <summary>
/// 检查列名是否为黏土矿物总量列
/// </summary>
/// <param name="columnName">列名</param>
/// <returns>是否为黏土矿物总量列</returns>
public static bool IsClayMineralTotalColumn(string columnName)
{
    if (string.IsNullOrEmpty(columnName))
        return false;

    // 检查是否是TOC列，如果是则不是黏土矿物总量列
    if (IsTOCColumn(columnName))
    {
        LoggingService.Instance.Debug($"跳过列 '{columnName}'，因为它是TOC相关列，不是黏土矿物总量列");
        return false;
    }

    // 标准化列名
    string normalizedName = NormalizeColumnName(columnName);

    // 黏土矿物总量关键词列表
    string[] clayTotalKeywords = {
        "黏土矿物总量", "粘土矿物总量",
        "黏土矿物相对含量", "粘土矿物相对含量",
        "黏土总量", "粘土总量",
        "黏土含量", "粘土含量",
        "clay minerals total", "clay total",
        "total clay", "clay content"
    };

    // 检查是否包含关键词
    foreach (string keyword in clayTotalKeywords)
    {
        if (normalizedName.Contains(NormalizeColumnName(keyword)))
        {
            return true;
        }
    }

    return false;
}
