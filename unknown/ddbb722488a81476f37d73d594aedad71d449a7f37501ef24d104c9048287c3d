        // 查找最佳列名匹配
        private string FindBestColumnMatch(string columnName)
        {
            if (_sourceData == null || string.IsNullOrEmpty(columnName))
                return null;

            // 输出调试信息
            System.Diagnostics.Debug.WriteLine($"开始查找最佳匹配列 '{columnName}'");

            // 1. 精确匹配
            if (_sourceData.Columns.Contains(columnName))
            {
                System.Diagnostics.Debug.WriteLine($"精确匹配成功: {columnName}");
                return columnName;
            }

            // 2. 忽略大小写匹配
            foreach (DataColumn column in _sourceData.Columns)
            {
                if (string.Equals(column.ColumnName, columnName, StringComparison.OrdinalIgnoreCase))
                {
                    System.Diagnostics.Debug.WriteLine($"忽略大小写匹配成功: {columnName} -> {column.ColumnName}");
                    return column.ColumnName;
                }
            }

            // 3. 检查手动映射
            foreach (var mapping in _manualMappings)
            {
                if (string.Equals(mapping.Value, columnName, StringComparison.OrdinalIgnoreCase))
                {
                    if (_sourceData.Columns.Contains(mapping.Key))
                    {
                        System.Diagnostics.Debug.WriteLine($"手动映射匹配成功: {columnName} <- {mapping.Key}");
                        return mapping.Key;
                    }
                }
            }

            // 4. 忽略特殊符号匹配
            string normalizedTarget = NormalizeColumnName(columnName);
            System.Diagnostics.Debug.WriteLine($"清理后的目标列名: {normalizedTarget}");

            // 输出所有列的清理后名称，用于调试
            System.Diagnostics.Debug.WriteLine("源数据表中所有列的清理后名称: ");
            Dictionary<string, string> normalizedColumns = new Dictionary<string, string>();
            foreach (DataColumn column in _sourceData.Columns)
            {
                string normalizedColumn = NormalizeColumnName(column.ColumnName);
                normalizedColumns[normalizedColumn] = column.ColumnName;
                System.Diagnostics.Debug.WriteLine($"  {column.ColumnName} -> {normalizedColumn}");

                if (normalizedColumn == normalizedTarget)
                {
                    System.Diagnostics.Debug.WriteLine($"清理后精确匹配成功: {columnName} -> {column.ColumnName}");
                    return column.ColumnName;
                }
            }

            // 5. 特殊处理常见矿物名称
            Dictionary<string, List<string>> commonMinerals = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase)
            {
                { "石英", new List<string> { "石英", "quartz", "qtz", "sq" } },
                { "白云石", new List<string> { "白云石", "dolomite", "dol", "bys" } },
                { "菱铁矿", new List<string> { "菱铁矿", "siderite", "sid", "ltk" } },
                { "斜长石", new List<string> { "斜长石", "plagioclase", "plag", "xcs" } },
                { "钾长石", new List<string> { "钾长石", "正长石", "k-feldspar", "kfs", "jcs" } },
                { "黏土矿物", new List<string> { "黏土", "粘土", "clay", "nt", "ntw" } },
                { "高岭石", new List<string> { "高岭石", "kaolinite", "kao", "gls" } },
                { "绿泥石", new List<string> { "绿泥石", "chlorite", "chl", "lns" } },
                { "伊利石", new List<string> { "伊利石", "illite", "ill", "yls" } },
                { "蒙脱石", new List<string> { "蒙脱石", "蒙皂石", "montmorillonite", "mnt", "mts" } }
            };
            
            foreach (var mineral in commonMinerals)
            {
                if (normalizedTarget.Contains(mineral.Key) || mineral.Value.Any(v => normalizedTarget.Contains(v)))
                {
                    foreach (DataColumn column in _sourceData.Columns)
                    {
                        string normalizedColumn = NormalizeColumnName(column.ColumnName);
                        if (normalizedColumn.Contains(mineral.Key) || mineral.Value.Any(v => normalizedColumn.Contains(v)))
                        {
                            System.Diagnostics.Debug.WriteLine($"矿物名称特殊匹配成功: {columnName} -> {column.ColumnName}");
                            return column.ColumnName;
                        }
                    }
                }
            }

            // 6. 特殊处理黏土矿物总量
            if (columnName.Contains("黏土矿物总量") || columnName.Contains("粘土矿物总量"))
            {
                foreach (DataColumn column in _sourceData.Columns)
                {
                    if (column.ColumnName.Contains("黏土") || column.ColumnName.Contains("粘土"))
                    {
                        System.Diagnostics.Debug.WriteLine($"黏土矿物总量特殊匹配成功: {columnName} -> {column.ColumnName}");
                        return column.ColumnName;
                    }
                }
            }

            // 7. 部分匹配 - 改进的算法
            Dictionary<string, double> matchScores = new Dictionary<string, double>();
            foreach (DataColumn column in _sourceData.Columns)
            {
                string normalizedColumn = NormalizeColumnName(column.ColumnName);

                // 计算匹配分数
                double score = 0;

                // 如果一个包含另一个，给予较高分数
                if (normalizedColumn.Contains(normalizedTarget))
                    score += 0.8;
                else if (normalizedTarget.Contains(normalizedColumn))
                    score += 0.6;

                // 特殊处理黏土/粘土
                if ((normalizedTarget.Contains("黏土") || normalizedTarget.Contains("粘土") || normalizedTarget.Contains("黏") || normalizedTarget.Contains("粘")) &&
                    (normalizedColumn.Contains("黏土") || normalizedColumn.Contains("粘土") || normalizedColumn.Contains("黏") || normalizedColumn.Contains("粘")))
                {
                    score += 0.5;
                }

                // 特殊处理总量
                if ((normalizedTarget.Contains("总量") || columnName.Contains("总量")) &&
                    (normalizedColumn.Contains("总量") || column.ColumnName.Contains("总量")))
                {
                    score += 0.5;
                }

                // 特殊处理百分号
                if ((columnName.Contains("%") || columnName.Contains("％")) &&
                    (column.ColumnName.Contains("%") || column.ColumnName.Contains("％")))
                {
                    score += 0.3;
                }

                // 记录分数
                if (score > 0)
                {
                    matchScores[column.ColumnName] = score;
                    System.Diagnostics.Debug.WriteLine($"部分匹配: {columnName} -> {column.ColumnName}, 分数: {score}");
                }
            }

            // 找出最高分数的匹配
            if (matchScores.Count > 0)
            {
                string bestMatch = matchScores.OrderByDescending(x => x.Value).First().Key;
                System.Diagnostics.Debug.WriteLine($"最佳部分匹配成功: {columnName} -> {bestMatch}, 分数: {matchScores[bestMatch]}");
                return bestMatch;
            }

            // 8. 尝试更宽松的匹配 - 只保留字母和数字
            string alphaNumericTarget = new string(columnName.Where(c => char.IsLetterOrDigit(c)).ToArray()).ToLower();
            foreach (DataColumn column in _sourceData.Columns)
            {
                string alphaNumericColumn = new string(column.ColumnName.Where(c => char.IsLetterOrDigit(c)).ToArray()).ToLower();
                if (alphaNumericColumn.Contains(alphaNumericTarget) || alphaNumericTarget.Contains(alphaNumericColumn))
                {
                    System.Diagnostics.Debug.WriteLine($"字母数字匹配成功: {columnName} -> {column.ColumnName}");
                    return column.ColumnName;
                }
            }

            // 9. 尝试使用拼音首字母匹配
            Dictionary<string, string> pinyinInitials = new Dictionary<string, string>
            {
                { "石英", "sq" }, { "白云石", "bys" }, { "菱铁矿", "ltk" }, { "斜长石", "xcs" },
                { "钾长石", "jcs" }, { "正长石", "zcs" }, { "黏土", "nt" }, { "粘土", "zt" },
                { "高岭石", "gls" }, { "绿泥石", "lns" }, { "伊利石", "yls" }, { "蒙脱石", "mts" }
            };
            
            foreach (var pinyin in pinyinInitials)
            {
                if (normalizedTarget.Contains(pinyin.Key))
                {
                    foreach (DataColumn column in _sourceData.Columns)
                    {
                        string normalizedColumn = NormalizeColumnName(column.ColumnName);
                        if (normalizedColumn.Contains(pinyin.Value, StringComparison.OrdinalIgnoreCase))
                        {
                            System.Diagnostics.Debug.WriteLine($"拼音首字母匹配成功: {columnName} -> {column.ColumnName}");
                            return column.ColumnName;
                        }
                    }
                }
            }

            // 10. 特殊处理黏土矿物
            if (columnName.Contains("黏土") || columnName.Contains("粘土"))
            {
                foreach (DataColumn column in _sourceData.Columns)
                {
                    if (column.ColumnName.Contains("黏土") || column.ColumnName.Contains("粘土"))
                    {
                        System.Diagnostics.Debug.WriteLine($"黏土矿物特殊匹配成功: {columnName} -> {column.ColumnName}");
                        return column.ColumnName;
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"警告: 找不到列 '{columnName}' 的匹配");
            return null;
        }
