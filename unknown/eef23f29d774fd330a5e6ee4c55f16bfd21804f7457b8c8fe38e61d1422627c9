﻿===== 计算按钮点击事件开始 - 2025-04-25 08:35:45 =====
源数据表: 77 行, 63 列
脆性矿物列数量: 4, 塑性矿物列数量: 1
已选择的脆性矿物列:
  - 石英%
  - 白云石%
  - 菱铁矿%
  - 斜长石%
已选择的塑性矿物列:
  - 黏土矿物总量%
已选择行索引: 2
清空之前的结果数据
创建数据点对象, 行索引: 2
尝试查找顶深列: 顶深/m -> 顶深/m
找到顶深列: 顶深/m, 值: 705.46
尝试查找底深列: 底深/m -> 底深/m
找到底深列: 底深/m, 值: 705.61
开始计算脆性矿物总量:
  尝试查找脆性矿物列: 石英% -> 石英%
  警告: 脆性矿物列 石英% 在数据源中不存在
  尝试查找脆性矿物列: 白云石% -> 白云石%
  警告: 脆性矿物列 白云石% 在数据源中不存在
  尝试查找脆性矿物列: 菱铁矿% -> 菱铁矿%
  警告: 脆性矿物列 菱铁矿% 在数据源中不存在
  尝试查找脆性矿物列: 斜长石% -> 斜长石%
  警告: 脆性矿物列 斜长石% 在数据源中不存在
脆性矿物总量计算结果: 0
开始计算塑性矿物总量:
  尝试查找塑性矿物列: 黏土矿物总量% -> 黏土矿物总量%
  警告: 塑性矿物列 黏土矿物总量% 在数据源中不存在
塑性矿物总量计算结果: 0
警告: 脆性和塑性矿物总量均为0，脆性指数默认为0
生成GeoID: GEO_705.46_705.61_0.0000_53C9922A
数据点添加到列表
结果数据添加到结果表
处理行 2: 顶深=705.46, 底深=705.61, 脆性指数=0.00, 脆性矿物总量=0, 塑性矿物总量=0
更新数据网格视图
更新结果表完成，共 1 行数据
当前数据网格视图列信息: 
  列名: GeoID, 标题: GeoID, 索引: 0
  列名: 顶深/m, 标题: 顶深/m, 索引: 1
  列名: 底深/m, 标题: 底深/m, 索引: 2
  列名: 脆性指数, 标题: 脆性指数, 索引: 3
  列名: 脆性矿物总量, 标题: 脆性矿物总量, 索引: 4
  列名: 塑性矿物总量, 标题: 塑性矿物总量, 索引: 5
  列名: 脆性矿物, 标题: 脆性矿物, 索引: 6
  列名: 塑性矿物, 标题: 塑性矿物, 索引: 7
开始高亮显示选中列
完成高亮显示选中列
计算完成，共生成 1 个数据点
===== 计算按钮点击事件结束 - 2025-04-25 08:35:45 =====
