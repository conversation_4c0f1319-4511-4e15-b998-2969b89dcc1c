using System;
using System.Windows.Forms;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;

namespace WebView2Test
{
    public class Form1 : Form
    {
        private WebView2 webView;
        private TextBox logTextBox;

        public Form1()
        {
            InitializeComponent();
            this.Load += Form1_Load;
        }

        private void InitializeComponent()
        {
            this.Text = "WebView2测试";
            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // 创建分割面板
            SplitContainer splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Horizontal;
            splitContainer.SplitterDistance = 400;
            this.Controls.Add(splitContainer);

            // 创建WebView2控件
            webView = new WebView2();
            webView.Dock = DockStyle.Fill;
            splitContainer.Panel1.Controls.Add(webView);

            // 创建日志文本框
            logTextBox = new TextBox();
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.Dock = DockStyle.Fill;
            logTextBox.ReadOnly = true;
            logTextBox.BackColor = System.Drawing.Color.Black;
            logTextBox.ForeColor = System.Drawing.Color.Lime;
            logTextBox.Font = new System.Drawing.Font("Consolas", 10);
            splitContainer.Panel2.Controls.Add(logTextBox);
        }

        private async void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                Log("正在初始化WebView2...");

                // 使用最简单的方式初始化WebView2
                await webView.EnsureCoreWebView2Async();
                Log("WebView2初始化成功！");

                // 设置WebView2的设置
                webView.CoreWebView2.Settings.AreHostObjectsAllowed = true;
                webView.CoreWebView2.Settings.IsWebMessageEnabled = true;
                webView.CoreWebView2.Settings.AreDefaultScriptDialogsEnabled = true;
                webView.CoreWebView2.Settings.IsScriptEnabled = true;
                Log("WebView2设置已配置");

                // 创建一个简单的HTML内容
                string htmlContent = "<html><body style='background-color: #333; color: white; font-family: Arial; text-align: center;'><h1>WebView2测试成功！</h1><p>如果您能看到这个页面，说明WebView2已经正确加载。</p></body></html>";

                // 直接使用NavigateToString加载HTML内容
                webView.CoreWebView2.NavigateToString(htmlContent);
                Log("已加载测试页面");
            }
            catch (Exception ex)
            {
                Log($"错误: {ex.Message}");
                Log($"错误类型: {ex.GetType().Name}");
                Log($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"初始化WebView2失败: {ex.Message}\n\n类型: {ex.GetType().Name}\n\n堆栈跟踪: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Log(string message)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action(() => Log(message)));
                return;
            }

            logTextBox.AppendText($"[{DateTime.Now:HH:mm:ss.fff}] {message}{Environment.NewLine}");
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
        }
    }
}
