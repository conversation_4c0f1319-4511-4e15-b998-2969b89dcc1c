using System;
using System.Windows.Forms;
using System.Drawing;

namespace NativeFormTest
{
    public class MainForm : Form
    {
        private Label lblTitle;
        private Button btnLogin;
        private Button btnDashboard;
        private Button btnMineralogical;

        public MainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // 窗体设置
            this.Text = "原生窗体测试";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(33, 33, 33); // 深色背景

            // 标题
            lblTitle = new Label();
            lblTitle.Text = "原生窗体测试";
            lblTitle.Font = new Font("Microsoft YaHei", 16, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Height = 50;
            lblTitle.Padding = new Padding(0, 10, 0, 0);

            // 登录窗体按钮
            btnLogin = new Button();
            btnLogin.Text = "测试登录窗体";
            btnLogin.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnLogin.Location = new Point(100, 70);
            btnLogin.Size = new Size(200, 40);
            btnLogin.BackColor = Color.FromArgb(0, 120, 212);
            btnLogin.ForeColor = Color.White;
            btnLogin.FlatStyle = FlatStyle.Flat;
            btnLogin.Click += BtnLogin_Click;

            // 仪表盘窗体按钮
            btnDashboard = new Button();
            btnDashboard.Text = "测试仪表盘窗体";
            btnDashboard.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnDashboard.Location = new Point(100, 130);
            btnDashboard.Size = new Size(200, 40);
            btnDashboard.BackColor = Color.FromArgb(0, 120, 212);
            btnDashboard.ForeColor = Color.White;
            btnDashboard.FlatStyle = FlatStyle.Flat;
            btnDashboard.Click += BtnDashboard_Click;

            // 矿物组分法窗体按钮
            btnMineralogical = new Button();
            btnMineralogical.Text = "测试矿物组分法窗体";
            btnMineralogical.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnMineralogical.Location = new Point(100, 190);
            btnMineralogical.Size = new Size(200, 40);
            btnMineralogical.BackColor = Color.FromArgb(0, 120, 212);
            btnMineralogical.ForeColor = Color.White;
            btnMineralogical.FlatStyle = FlatStyle.Flat;
            btnMineralogical.Click += BtnMineralogical_Click;

            // 添加控件到窗体
            this.Controls.Add(lblTitle);
            this.Controls.Add(btnLogin);
            this.Controls.Add(btnDashboard);
            this.Controls.Add(btnMineralogical);
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            MessageBox.Show("登录窗体测试按钮被点击", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnDashboard_Click(object sender, EventArgs e)
        {
            MessageBox.Show("仪表盘窗体测试按钮被点击", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnMineralogical_Click(object sender, EventArgs e)
        {
            MessageBox.Show("矿物组分法窗体测试按钮被点击", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
