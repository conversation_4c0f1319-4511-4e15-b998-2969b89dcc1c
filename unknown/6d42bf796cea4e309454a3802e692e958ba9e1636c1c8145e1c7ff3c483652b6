{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\135.0.3179.73\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\135.0.3179.73\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "87D5D0E90CE0B435685292707566374DD613F171DCF6E3EFA5EFCDAC89CF43F8"}, "default_search_provider_data": {"template_url_data": "AEA34E97376B4853FEA7DFC2F94F41658E66C537D55CB5A20500DD5A84B550A6"}, "edge": {"services": {"account_id": "CD47712EA1261637B7F961B318D4E9215A3903B48F6729EB61D2629248BB5788", "last_username": "529A401E65E16B2FDF692115C6D73927AE17B8B6376CD105573C98BED42D4280"}}, "enterprise_signin": {"policy_recovery_token": "9CAB5DEC528E430C8E12EB6576605242545E8DB3CFA02279BE53A8B1984C854D"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "47AAB99F7311E7944FD086B763E2A31BF20C50F6240CE921C160C26D789C7A35", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "5C0CB5733CCD0C9165CC2E1F2B5D696C955674FE996C027B693066E1040555E5"}, "ui": {"developer_mode": "441DDC6650585072C9D5DC186994776CF3B752E8D51D019535D874B841582B8F"}}, "google": {"services": {"last_signed_in_username": "BAFF1C833AA93873AA7CC2F2ED4CBB81DDF7D3802E4BCD430B08177E6E57727F"}}, "homepage": "34480F48DD98981E7D7F5291C97B798B2E4D4BDE38A1BA7148FDF567BAB6FDAE", "homepage_is_newtabpage": "70E2BC8D56AA81F812205D1BE93E0DA1D72B4C95238C91A463A1B825BC42B371", "media": {"cdm": {"origin_data": "E39F501590D5780A5ACC4B5C957649FF326759F575614BFF6C2DDBD4513C4242"}, "storage_id_salt": "E00156707B72F21BE2E9F31BC02336346CEB30FC4FFB2798A5E8212700E519C5"}, "pinned_tabs": "4B1262249D795C403740359CEE46CA782E8DB629CC637CC41D3F5F25066A51EE", "prefs": {"preference_reset_time": "9E1FB1ED143CECDD49197B51A5BA29D3CBAC2EFE18B150418320191347C6F807"}, "safebrowsing": {"incidents_sent": "6ADAFA7D9B5580D2EBB5296E3EA1906DEE585022D52179B89717A5D5CC67FDC6"}, "search_provider_overrides": "2ED985678497AA7E340D30D600F603E264917B78804203EB82B5A54F5149F6B3", "session": {"restore_on_startup": "8B84809FDBEA10E1C435AD347F695FA9CB3937C4472BC0068DFAC03518315589", "startup_urls": "D70A62E317E6374CA8145167BB2A2775AA47418BA2C2294EC4E3E2F798346DE6"}}, "super_mac": "75B880183EDE640D2B287ECD69460A6078E779BC02AF099C95790272E7034155"}}