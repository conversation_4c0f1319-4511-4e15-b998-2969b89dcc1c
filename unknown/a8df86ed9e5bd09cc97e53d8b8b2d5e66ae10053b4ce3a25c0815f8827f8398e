using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using BritSystem.Services;

namespace BritSystem
{
    /// <summary>
    /// 列检测辅助类，用于改进列名匹配和黏土矿物总量检测
    /// </summary>
    public class ColumnDetectionHelper
    {
        /// <summary>
        /// 标准化列名，移除特殊字符并转为小写
        /// </summary>
        /// <param name="columnName">原始列名</param>
        /// <returns>标准化后的列名</returns>
        public static string NormalizeColumnName(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return string.Empty;

            // 如果是 ColumnXX 格式，直接返回小写形式，不做其他处理
            if (Regex.IsMatch(columnName, @"^Column\d+$", RegexOptions.IgnoreCase))
            {
                return columnName.ToLower();
            }

            // 保留百分号，但移除其他特殊字符，统一括号格式，转为小写
            string normalized = columnName
                .Replace("（", "(")
                .Replace("）", ")")
                .Replace("【", "[")
                .Replace("】", "]")
                .Replace("／", "/")
                .Replace("％", "%")
                .Trim()
                .ToLower();

            // 移除多余空格
            normalized = Regex.Replace(normalized, @"\s+", " ");

            return normalized;
        }

        /// <summary>
        /// 检查列名是否为黏土矿物总量列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>是否为黏土矿物总量列</returns>
        public static bool IsClayMineralTotalColumn(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return false;

            // 检查是否是TOC列，如果是则不是黏土矿物总量列
            if (IsTOCColumn(columnName))
            {
                LoggingService.Instance.Debug($"跳过列 '{columnName}'，因为它是TOC相关列，不是黏土矿物总量列");
                return false;
            }

            // 标准化列名
            string normalizedName = NormalizeColumnName(columnName);

            // 黏土矿物总量关键词列表
            string[] clayTotalKeywords = {
                "黏土矿物总量", "粘土矿物总量",
                "黏土矿物相对含量", "粘土矿物相对含量",
                "黏土总量", "粘土总量",
                "黏土含量", "粘土含量",
                "clay minerals total", "clay total",
                "total clay", "clay content"
            };

            // 检查是否包含关键词
            foreach (string keyword in clayTotalKeywords)
            {
                if (normalizedName.Contains(NormalizeColumnName(keyword)))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 检查列名是否为TOC列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>是否为TOC列</returns>
        public static bool IsTOCColumn(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return false;

            string normalizedName = NormalizeColumnName(columnName);

            // TOC关键词列表
            string[] tocKeywords = {
                "toc", "有机碳", "总有机碳", "有机质", "有机物",
                "total organic carbon", "organic carbon"
            };

            // 检查是否包含关键词
            foreach (string keyword in tocKeywords)
            {
                if (normalizedName.Contains(NormalizeColumnName(keyword)))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 根据行号和列名查找实际的列
        /// </summary>
        /// <param name="table">数据表</param>
        /// <param name="rowIndex">行索引</param>
        /// <param name="columnName">列名</param>
        /// <returns>找到的列索引，如果未找到则返回-1</returns>
        public static int FindColumnByRowAndName(DataTable table, int rowIndex, string columnName)
        {
            if (table == null || rowIndex < 0 || rowIndex >= table.Rows.Count || string.IsNullOrEmpty(columnName))
                return -1;

            string normalizedTarget = NormalizeColumnName(columnName);

            // 遍历该行的所有单元格
            for (int i = 0; i < table.Columns.Count; i++)
            {
                string cellValue = table.Rows[rowIndex][i]?.ToString() ?? "";
                string normalizedCell = NormalizeColumnName(cellValue);

                if (normalizedCell == normalizedTarget || 
                    normalizedCell.Contains(normalizedTarget) || 
                    normalizedTarget.Contains(normalizedCell))
                {
                    LoggingService.Instance.Debug($"在行 {rowIndex + 1} 找到列 '{columnName}' 匹配: '{cellValue}'，列索引: {i}");
                    return i;
                }
            }

            LoggingService.Instance.Debug($"在行 {rowIndex + 1} 未找到列 '{columnName}' 的匹配");
            return -1;
        }

        /// <summary>
        /// 在指定范围内查找黏土矿物总量列
        /// </summary>
        /// <param name="table">数据表</param>
        /// <param name="startRow">起始行索引</param>
        /// <param name="endRow">结束行索引</param>
        /// <returns>找到的黏土矿物总量列索引，如果未找到则返回-1</returns>
        public static int FindClayMineralTotalColumnInRows(DataTable table, int startRow, int endRow)
        {
            if (table == null || startRow < 0 || endRow >= table.Rows.Count || startRow > endRow)
                return -1;

            // 遍历指定范围内的行
            for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++)
            {
                // 遍历该行的所有单元格
                for (int colIndex = 0; colIndex < table.Columns.Count; colIndex++)
                {
                    string cellValue = table.Rows[rowIndex][colIndex]?.ToString() ?? "";
                    
                    if (IsClayMineralTotalColumn(cellValue))
                    {
                        LoggingService.Instance.Info($"在行 {rowIndex + 1} 找到黏土矿物总量列: '{cellValue}'，列索引: {colIndex}");
                        return colIndex;
                    }
                }
            }

            LoggingService.Instance.Debug("在指定行范围内未找到黏土矿物总量列");
            return -1;
        }
    }
}
