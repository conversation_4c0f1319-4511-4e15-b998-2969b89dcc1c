        // 从Excel文件加载数据
        private void LoadExcelData(string filePath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始加载Excel文件: {filePath}");
                
                // 创建文件流
                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    // 根据文件扩展名创建工作簿
                    IWorkbook workbook;
                    if (Path.GetExtension(filePath).ToLower() == ".xlsx")
                        workbook = new XSSFWorkbook(fs);
                    else
                        workbook = new HSSFWorkbook(fs);

                    // 获取第一个工作表
                    ISheet sheet = workbook.GetSheetAt(0);
                    
                    // 输出工作表信息
                    System.Diagnostics.Debug.WriteLine($"工作表名称: {sheet.SheetName}, 物理行数: {sheet.PhysicalNumberOfRows}, 最后行号: {sheet.LastRowNum}");

                    // 检查工作表是否为空
                    if (sheet.PhysicalNumberOfRows == 0)
                    {
                        MessageBox.Show("Excel文件中没有数据！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // 创建数据表
                    _sourceData = new DataTable();
                    
                    // 查找有效的表头行（通常不是第一行）
                    int headerRowIndex = -1;
                    int dataStartRowIndex = -1;
                    
                    // 扫描前10行，寻找可能的表头行
                    for (int i = 0; i <= Math.Min(10, sheet.LastRowNum); i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row == null) continue;
                        
                        // 检查该行是否包含关键词（如"矿物成分"、"石英%"等）
                        bool containsKeywords = false;
                        for (int j = 0; j < row.LastCellNum; j++)
                        {
                            ICell cell = row.GetCell(j);
                            if (cell != null && cell.CellType == CellType.String)
                            {
                                string cellValue = cell.StringCellValue.Trim();
                                if (cellValue.Contains("石英") || cellValue.Contains("黏土") || 
                                    cellValue.Contains("矿物成分") || cellValue.Contains("X-衍射分析"))
                                {
                                    containsKeywords = true;
                                    headerRowIndex = i;
                                    break;
                                }
                            }
                        }
                        
                        if (containsKeywords) break;
                    }
                    
                    // 如果没有找到表头行，使用第一行作为表头
                    if (headerRowIndex == -1)
                    {
                        headerRowIndex = 0;
                        System.Diagnostics.Debug.WriteLine("未找到明确的表头行，使用第一行作为表头");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"找到表头行，索引为: {headerRowIndex}");
                    }
                    
                    // 获取表头行
                    IRow headerRow = sheet.GetRow(headerRowIndex);
                    
                    // 添加列（跳过空列）
                    for (int i = 0; i < headerRow.LastCellNum; i++)
                    {
                        ICell cell = headerRow.GetCell(i);
                        if (cell != null)
                        {
                            string columnName = cell.ToString().Trim();
                            if (!string.IsNullOrEmpty(columnName))
                            {
                                // 检查是否已存在同名列
                                if (!_sourceData.Columns.Contains(columnName))
                                {
                                    _sourceData.Columns.Add(columnName, typeof(double));
                                    System.Diagnostics.Debug.WriteLine($"添加列: {columnName}, 类型: {typeof(double)}");
                                }
                                else
                                {
                                    // 如果列名重复，添加索引后缀
                                    string newColumnName = $"{columnName}_{i}";
                                    _sourceData.Columns.Add(newColumnName, typeof(double));
                                    System.Diagnostics.Debug.WriteLine($"添加列(重命名): {newColumnName}, 类型: {typeof(double)}");
                                }
                            }
                        }
                    }
