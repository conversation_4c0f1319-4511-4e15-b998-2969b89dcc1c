using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;

namespace BritSystem
{
    public partial class ManualDetectForm : Form
    {
        private DataTable mineralData;
        private Dictionary<string, int> selectedColumns = new Dictionary<string, int>();
        private DataTable? processedData;
        public DataTable? ProcessedData => processedData;
        public Dictionary<string, int> SelectedColumns => selectedColumns;
        private string currentSelectionType = null;

        public ManualDetectForm(DataTable data)
        {
            InitializeComponent();
            this.mineralData = data;
            this.Text = "手动选择列";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.DialogResult = DialogResult.Cancel; // 默认为取消

            // 初始化UI和数据
            InitializeUI();
        }

        private void InitializeUI()
        {
            // 创建指导面板
            var instructionPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 120,
                BackColor = Color.LightGray
            };

            lblInstructions = new Label
            {
                Text = "请先点击下方的按钮选择要映射的列类型，然后点击表格中的列标题。\n您需要选择三个列：顶深、底深和脆性指数。",
                Dock = DockStyle.Top,
                Height = 60,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Microsoft YaHei UI", 10, FontStyle.Bold),
                AutoSize = false
            };
            instructionPanel.Controls.Add(lblInstructions);

            // 创建选择按钮面板
            var selectionPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                FlowDirection = FlowDirection.LeftToRight,
                Padding = new Padding(10),
                BackColor = Color.LightGray,
                AutoScroll = true
            };

            // 顶深选择
            btnTopDepth = new Button
            {
                Text = "选择顶深列",
                Size = new Size(150, 30),
                BackColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Microsoft YaHei UI", 9)
            };
            btnTopDepth.Click += (s, e) => SelectColumnType("顶深");
            selectionPanel.Controls.Add(btnTopDepth);

            lblTopDepth = new Label
            {
                Text = "未选择",
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.Red,
                Font = new Font("Microsoft YaHei UI", 9)
            };
            selectionPanel.Controls.Add(lblTopDepth);

            // 底深选择
            btnBottomDepth = new Button
            {
                Text = "选择底深列",
                Size = new Size(150, 30),
                BackColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Microsoft YaHei UI", 9)
            };
            btnBottomDepth.Click += (s, e) => SelectColumnType("底深");
            selectionPanel.Controls.Add(btnBottomDepth);

            lblBottomDepth = new Label
            {
                Text = "未选择",
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.Red,
                Font = new Font("Microsoft YaHei UI", 9)
            };
            selectionPanel.Controls.Add(lblBottomDepth);

            // 脆性指数选择
            btnBrittleIndex = new Button
            {
                Text = "选择脆性指数列",
                Size = new Size(150, 30),
                BackColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Microsoft YaHei UI", 9)
            };
            btnBrittleIndex.Click += (s, e) => SelectColumnType("脆性");
            selectionPanel.Controls.Add(btnBrittleIndex);

            lblBrittleIndex = new Label
            {
                Text = "未选择",
                Size = new Size(150, 30),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.Red,
                Font = new Font("Microsoft YaHei UI", 9)
            };
            selectionPanel.Controls.Add(lblBrittleIndex);

            instructionPanel.Controls.Add(selectionPanel);
            this.Controls.Add(instructionPanel);

            // 数据表格
            dataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                DataSource = mineralData,
                ReadOnly = true,
                AllowUserToAddRows = false,
                BackgroundColor = SystemColors.Window,
                BorderStyle = BorderStyle.Fixed3D,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                ScrollBars = ScrollBars.Both,
                SelectionMode = DataGridViewSelectionMode.FullColumnSelect
            };

            // 禁用列排序以避免与FullColumnSelect冲突
            foreach (DataGridViewColumn column in dataGridView.Columns)
            {
                column.SortMode = DataGridViewColumnSortMode.NotSortable;
            }
            dataGridView.CellClick += DataGridView_CellClick;
            this.Controls.Add(dataGridView);

            // 确认按钮
            btnConfirm = new Button
            {
                Text = "确定",
                Dock = DockStyle.Bottom,
                Height = 40,
                Font = new Font("Microsoft YaHei UI", 10, FontStyle.Bold),
                BackColor = Color.LightGreen
            };
            btnConfirm.Click += BtnConfirm_Click;
            btnConfirm.Enabled = false;
            this.Controls.Add(btnConfirm);
        }

        private void SelectColumnType(string columnType)
        {
            // 高亮当前选择的按钮
            btnTopDepth.BackColor = columnType == "顶深" ? Color.LightBlue : Color.White;
            btnBottomDepth.BackColor = columnType == "底深" ? Color.LightBlue : Color.White;
            btnBrittleIndex.BackColor = columnType == "脆性" ? Color.LightBlue : Color.White;

            currentSelectionType = columnType;
            lblInstructions.Text = $"请点击表格中的列标题选择{columnType}列（点击列标题）";

            // 高亮所有列标题以提示用户可以点击
            for (int i = 0; i < dataGridView.Columns.Count; i++)
            {
                dataGridView.Columns[i].HeaderCell.Style.BackColor = Color.LightYellow;
            }
        }

        private void UpdateSelectionLabels()
        {
            if (selectedColumns.ContainsKey("顶深") && dataGridView.Columns.Count > selectedColumns["顶深"])
            {
                lblTopDepth.Text = dataGridView.Columns[selectedColumns["顶深"]].HeaderText;
                lblTopDepth.ForeColor = Color.Green;
            }
            else
            {
                lblTopDepth.Text = "未选择";
                lblTopDepth.ForeColor = Color.Red;
            }

            if (selectedColumns.ContainsKey("底深") && dataGridView.Columns.Count > selectedColumns["底深"])
            {
                lblBottomDepth.Text = dataGridView.Columns[selectedColumns["底深"]].HeaderText;
                lblBottomDepth.ForeColor = Color.Green;
            }
            else
            {
                lblBottomDepth.Text = "未选择";
                lblBottomDepth.ForeColor = Color.Red;
            }

            if (selectedColumns.ContainsKey("脆性") && dataGridView.Columns.Count > selectedColumns["脆性"])
            {
                lblBrittleIndex.Text = dataGridView.Columns[selectedColumns["脆性"]].HeaderText;
                lblBrittleIndex.ForeColor = Color.Green;
            }
            else
            {
                lblBrittleIndex.Text = "未选择";
                lblBrittleIndex.ForeColor = Color.Red;
            }

            // 如果所有列都选好了，更新指导文本
            if (selectedColumns.Count == 3)
            {
                lblInstructions.Text = "所有列已选择完成，点击确定按钮继续";
                btnConfirm.Enabled = true;
            }
            else
            {
                btnConfirm.Enabled = false;
            }
        }

        private Color GetColorForType(string type)
        {
            switch (type)
            {
                case "顶深": return Color.LightBlue;
                case "底深": return Color.LightGreen;
                case "脆性": return Color.LightPink;
                default: return Color.Yellow;
            }
        }

        private void DataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            // 只处理列标题点击
            if (e.RowIndex != -1 || e.ColumnIndex == -1 || currentSelectionType == null)
                return;

            // 清除之前的选择（如果有）
            if (selectedColumns.ContainsKey(currentSelectionType))
            {
                int oldColumnIndex = selectedColumns[currentSelectionType];
                if (oldColumnIndex >= 0 && oldColumnIndex < dataGridView.Columns.Count)
                {
                    dataGridView.Columns[oldColumnIndex].HeaderCell.Style.BackColor = Color.White;
                }
            }

            // 设置新的选择
            selectedColumns[currentSelectionType] = e.ColumnIndex;
            dataGridView.Columns[e.ColumnIndex].HeaderCell.Style.BackColor = GetColorForType(currentSelectionType);

            // 更新选择标签
            UpdateSelectionLabels();

            // 如果所有列都已选择，自动处理数据
            if (selectedColumns.Count == 3)
            {
                ProcessData();
            }
        }

        private void ProcessData()
        {
            try
            {
                // 创建新的数据表
                processedData = new DataTable();
                processedData.Columns.Add("顶深/m", typeof(double));
                processedData.Columns.Add("底深/m", typeof(double));
                processedData.Columns.Add("脆性指数", typeof(double));

                // 处理数据
                int validRows = 0;
                int invalidRows = 0;
                StringBuilder errorLog = new StringBuilder();

                for (int i = 0; i < mineralData.Rows.Count; i++)
                {
                    try
                    {
                        // 跳过空行
                        bool isEmptyRow = true;
                        for (int j = 0; j < mineralData.Columns.Count; j++)
                        {
                            if (!string.IsNullOrWhiteSpace(mineralData.Rows[i][j]?.ToString()))
                            {
                                isEmptyRow = false;
                                break;
                            }
                        }
                        if (isEmptyRow) continue;

                        // 解析数据
                        double top = ParseCell(mineralData.Rows[i][selectedColumns["顶深"]]);
                        double bottom = ParseCell(mineralData.Rows[i][selectedColumns["底深"]]);
                        double brittle = ParseCell(mineralData.Rows[i][selectedColumns["脆性"]]);

                        // 验证数据
                        if (!double.IsNaN(top) && !double.IsNaN(bottom) && !double.IsNaN(brittle) &&
                            top < bottom && brittle >= 0 && brittle <= 100)
                        {
                            processedData.Rows.Add(top, bottom, brittle);
                            validRows++;
                        }
                        else
                        {
                            invalidRows++;
                            errorLog.AppendLine($"行 {i + 1}: 顶深={top}, 底深={bottom}, 脆性={brittle} - 数据无效");
                        }
                    }
                    catch (Exception ex)
                    {
                        invalidRows++;
                        errorLog.AppendLine($"行 {i + 1}: 处理错误 - {ex.Message}");
                    }
                }

                // 更新数据网格
                dataGridView.DataSource = processedData;

                // 显示成功信息
                string message = $"数据处理完成\n\n有效行数: {validRows}\n无效行数: {invalidRows}";
                if (invalidRows > 0 && errorLog.Length > 0)
                {
                    message += "\n\n是否查看错误日志?";
                    var result = MessageBox.Show(message, "处理完成", MessageBoxButtons.YesNo, MessageBoxIcon.Information);
                    if (result == DialogResult.Yes)
                    {
                        MessageBox.Show(errorLog.ToString(), "错误日志", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                else
                {
                    MessageBox.Show(message, "处理成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private double ParseCell(object cellValue)
        {
            if (cellValue == null || cellValue == DBNull.Value || string.IsNullOrWhiteSpace(cellValue.ToString()))
            {
                return double.NaN;
            }

            string strValue = cellValue.ToString().Trim();

            // 尝试直接解析为数字
            if (double.TryParse(strValue, out double result))
            {
                if (double.IsInfinity(result) || double.IsNaN(result))
                {
                    return double.NaN;
                }
                return result;
            }

            // 检查是否是Excel公式（如AU5+AT5+AS5+AR5+AQ5）
            if (IsExcelFormula(strValue))
            {
                // 尝试计算公式结果
                double formulaResult = EvaluateExcelFormula(strValue);
                if (!double.IsNaN(formulaResult))
                {
                    return formulaResult;
                }
            }

            return double.NaN;
        }

        // 检查字符串是否是Excel公式
        private bool IsExcelFormula(string value)
        {
            // 检查是否包含Excel单元格引用模式（如A1、B2等）和运算符
            return Regex.IsMatch(value, @"[A-Z]+\d+[\+\-\*\/][A-Z]+\d+");
        }

        // 计算Excel公式的值
        private double EvaluateExcelFormula(string formula)
        {
            try
            {
                // 分解公式（例如：AU5+AT5+AS5+AR5+AQ5）
                string[] parts = Regex.Split(formula, @"(\+|\-|\*|\/)");
                if (parts.Length < 3) // 至少需要两个操作数和一个运算符
                {
                    return double.NaN;
                }

                // 提取所有数值
                List<double> values = new List<double>();
                List<string> operators = new List<string>();

                for (int i = 0; i < parts.Length; i++)
                {
                    string part = parts[i].Trim();
                    if (string.IsNullOrEmpty(part)) continue;

                    // 如果是运算符，添加到运算符列表
                    if (part == "+" || part == "-" || part == "*" || part == "/")
                    {
                        operators.Add(part);
                    }
                    // 否则尝试获取单元格引用的值
                    else if (Regex.IsMatch(part, @"^[A-Z]+\d+$"))
                    {
                        // 这里我们假设单元格引用的值已经在Excel中计算好了
                        // 由于我们无法直接访问Excel单元格的值，我们假设每个单元格引用的值为0
                        // 在实际应用中，这里应该从对应的数据表中获取单元格的值
                        values.Add(0);
                    }
                    // 尝试直接解析为数字
                    else if (double.TryParse(part, out double value))
                    {
                        values.Add(value);
                    }
                    else
                    {
                        // 无法解析的部分，返回NaN
                        return double.NaN;
                    }
                }

                // 如果值的数量不等于运算符数量+1，则公式无效
                if (values.Count != operators.Count + 1)
                {
                    return double.NaN;
                }

                // 计算公式结果
                double result = values[0];
                for (int i = 0; i < operators.Count; i++)
                {
                    switch (operators[i])
                    {
                        case "+":
                            result += values[i + 1];
                            break;
                        case "-":
                            result -= values[i + 1];
                            break;
                        case "*":
                            result *= values[i + 1];
                            break;
                        case "/":
                            if (values[i + 1] == 0) return double.NaN; // 避免除以零
                            result /= values[i + 1];
                            break;
                    }
                }

                return result;
            }
            catch
            {
                return double.NaN;
            }
        }

        private void BtnConfirm_Click(object sender, EventArgs e)
        {
            if (selectedColumns.Count == 3)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                // 更新指导文本，提示用户需要选择所有列
                lblInstructions.Text = "请先选择所有列（顶深、底深和脆性指数）才能继续。";
                lblInstructions.ForeColor = Color.Red;
            }
        }

        private double CalculateBrittlenessIndex()
        {
            if (processedData == null || processedData.Rows.Count == 0)
                return 0;

            try
            {
                // 计算所有脆性指数的平均值
                double sum = 0;
                int count = 0;

                foreach (DataRow row in processedData.Rows)
                {
                    double brittle = Convert.ToDouble(row["脆性指数"]);
                    sum += brittle;
                    count++;
                }

                if (count > 0)
                    return sum / count;
                else
                    return 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算脆性指数时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return 0;
            }
        }

        private void btnBrittlenessAlgorithm_Click(object sender, EventArgs e)
        {
            if (ProcessedData == null || ProcessedData.Rows.Count == 0)
            {
                MessageBox.Show("请先处理数据再进行脆性指数计算。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // 这里实现脆性指数计算逻辑
                double brittlenessIndex = CalculateBrittlenessIndex();
                MessageBox.Show($"计算完成！脆性指数为: {brittlenessIndex:F2}", "结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算脆性指数时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
