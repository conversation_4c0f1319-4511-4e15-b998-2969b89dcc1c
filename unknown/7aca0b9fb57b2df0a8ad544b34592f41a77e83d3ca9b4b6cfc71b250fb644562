using System;
using System.Windows.Forms;

namespace BritSystem
{
    public class NativeFormTest : Form
    {
        private Button btnLogin;
        private Button btnDashboard;
        private Button btnMineralogical;

        public NativeFormTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "原生窗体测试";
            this.Size = new System.Drawing.Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;

            btnLogin = new Button();
            btnLogin.Text = "测试登录窗体";
            btnLogin.Location = new System.Drawing.Point(100, 50);
            btnLogin.Size = new System.Drawing.Size(200, 40);
            btnLogin.Click += BtnLogin_Click;

            btnDashboard = new Button();
            btnDashboard.Text = "测试仪表盘窗体";
            btnDashboard.Location = new System.Drawing.Point(100, 120);
            btnDashboard.Size = new System.Drawing.Size(200, 40);
            btnDashboard.Click += BtnDashboard_Click;

            btnMineralogical = new Button();
            btnMineralogical.Text = "测试矿物组分法窗体";
            btnMineralogical.Location = new System.Drawing.Point(100, 190);
            btnMineralogical.Size = new System.Drawing.Size(200, 40);
            btnMineralogical.Click += BtnMineralogical_Click;

            this.Controls.Add(btnLogin);
            this.Controls.Add(btnDashboard);
            this.Controls.Add(btnMineralogical);
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            LoginForm loginForm = new LoginForm();
            DialogResult result = loginForm.ShowDialog();
            if (result == DialogResult.OK)
            {
                MessageBox.Show($"登录成功，用户名：{loginForm.Username}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("登录取消或失败", "信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnDashboard_Click(object sender, EventArgs e)
        {
            DashboardForm dashboardForm = new DashboardForm("测试用户");
            dashboardForm.ShowDialog();
        }

        private void BtnMineralogical_Click(object sender, EventArgs e)
        {
            MineralogicalForm mineralogicalForm = new MineralogicalForm("测试用户");
            mineralogicalForm.ShowDialog();
        }
    }
}
