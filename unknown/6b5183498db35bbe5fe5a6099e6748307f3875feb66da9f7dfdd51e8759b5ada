using System;
using System.Windows.Forms;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using System.IO;
using System.Diagnostics;

class WebView2Tester
{
    [STAThread]
    static void Main()
    {
        Application.EnableVisualStyles();
        Application.SetCompatibleTextRenderingDefault(false);
        Application.Run(new MainForm());
    }
}

class MainForm : Form
{
    private WebView2 webView;
    private TextBox logTextBox;
    private string logFilePath;

    public MainForm()
    {
        this.Text = "WebView2 测试工具";
        this.Size = new System.Drawing.Size(1000, 800);
        this.StartPosition = FormStartPosition.CenterScreen;

        // 创建日志文件路径
        logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "webview2_test_log.txt");
        
        // 创建分割面板
        SplitContainer splitContainer = new SplitContainer();
        splitContainer.Dock = DockStyle.Fill;
        splitContainer.Orientation = Orientation.Vertical;
        splitContainer.SplitterDistance = 700;
        this.Controls.Add(splitContainer);

        // 创建WebView2控件
        webView = new WebView2();
        webView.Dock = DockStyle.Fill;
        splitContainer.Panel1.Controls.Add(webView);

        // 创建日志面板
        Panel logPanel = new Panel();
        logPanel.Dock = DockStyle.Fill;
        splitContainer.Panel2.Controls.Add(logPanel);

        // 创建日志标题
        Label logLabel = new Label();
        logLabel.Text = "日志输出";
        logLabel.Dock = DockStyle.Top;
        logLabel.Height = 30;
        logLabel.Font = new System.Drawing.Font("Microsoft YaHei", 10, System.Drawing.FontStyle.Bold);
        logPanel.Controls.Add(logLabel);

        // 创建日志文本框
        logTextBox = new TextBox();
        logTextBox.Multiline = true;
        logTextBox.ScrollBars = ScrollBars.Vertical;
        logTextBox.Dock = DockStyle.Fill;
        logTextBox.ReadOnly = true;
        logTextBox.BackColor = System.Drawing.Color.Black;
        logTextBox.ForeColor = System.Drawing.Color.Lime;
        logTextBox.Font = new System.Drawing.Font("Consolas", 9);
        logPanel.Controls.Add(logTextBox);
        logTextBox.BringToFront();

        // 注册窗体加载事件
        this.Load += MainForm_Load;
    }

    private async void MainForm_Load(object sender, EventArgs e)
    {
        try
        {
            Log("正在初始化WebView2...");
            
            // 使用最简单的方式初始化WebView2
            await webView.EnsureCoreWebView2Async();
            Log("WebView2初始化成功！");

            // 设置WebView2的设置
            webView.CoreWebView2.Settings.AreHostObjectsAllowed = true;
            webView.CoreWebView2.Settings.IsWebMessageEnabled = true;
            webView.CoreWebView2.Settings.AreDefaultScriptDialogsEnabled = true;
            webView.CoreWebView2.Settings.IsScriptEnabled = true;
            Log("WebView2设置已配置");

            // 创建一个简单的HTML内容
            string htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>WebView2测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #333;
            color: #fff;
            text-align: center;
            padding: 50px;
        }
        h1 {
            color: #0078d7;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #444;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }
        button {
            background-color: #0078d7;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background-color: #005a9e;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .info {
            color: #2196F3;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>WebView2测试页面</h1>
        <p class='success'>如果您能看到这个页面，说明WebView2已经正确加载。</p>
        <p>当前时间: <span id='current-time'></span></p>
        <p class='info'>WebView2运行时版本: <span id='runtime-version'></span></p>
        <button id='test-button'>测试WebView2通信</button>
        <p id='result'></p>
    </div>

    <script>
        // 显示当前时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        updateTime();
        setInterval(updateTime, 1000);
        
        // 显示WebView2运行时版本
        document.getElementById('runtime-version').textContent = window.chrome.webview.hostObjects.sync.environment.BrowserVersionString;

        // 测试WebView2通信
        document.getElementById('test-button').addEventListener('click', function() {
            // 发送消息到C#
            window.chrome.webview.postMessage({
                action: 'test',
                message: '这是一条测试消息',
                timestamp: new Date().toISOString()
            });
            
            document.getElementById('result').textContent = '消息已发送，等待响应...';
        });

        // 接收来自C#的消息
        window.chrome.webview.addEventListener('message', function(event) {
            if (event.data.action === 'testResponse') {
                document.getElementById('result').textContent = '收到响应: ' + event.data.message;
            }
        });
    </script>
</body>
</html>";

            // 直接使用NavigateToString加载HTML内容
            webView.CoreWebView2.NavigateToString(htmlContent);
            Log("已加载测试页面");

            // 注册消息处理程序
            webView.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
            Log("WebMessageReceived处理程序已注册");
        }
        catch (Exception ex)
        {
            Log($"错误: {ex.Message}");
            Log($"错误类型: {ex.GetType().Name}");
            Log($"堆栈跟踪: {ex.StackTrace}");
            MessageBox.Show($"初始化WebView2失败: {ex.Message}\n\n类型: {ex.GetType().Name}\n\n堆栈跟踪: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
    {
        string messageJson = e.WebMessageAsJson;
        Log($"收到消息: {messageJson}");

        try
        {
            // 解析从JavaScript发送的消息
            var message = System.Text.Json.JsonDocument.Parse(messageJson);
            var root = message.RootElement;

            if (root.TryGetProperty("action", out var actionElement))
            {
                string action = actionElement.GetString();
                Log($"消息动作: {action}");

                // 处理测试消息
                if (action == "test")
                {
                    string messageText = root.GetProperty("message").GetString();
                    Log($"测试消息内容: {messageText}");

                    // 发送响应回到前端
                    var response = new
                    {
                        action = "testResponse",
                        message = "C#已收到您的消息: " + messageText,
                        receivedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")
                    };
                    string json = System.Text.Json.JsonSerializer.Serialize(response);
                    webView.CoreWebView2.PostWebMessageAsJson(json);
                    Log($"已发送响应: {json}");
                }
            }
        }
        catch (Exception ex)
        {
            Log($"处理消息时出错: {ex.Message}");
        }
    }

    private void Log(string message)
    {
        // 在UI线程上更新日志文本框
        if (logTextBox.InvokeRequired)
        {
            logTextBox.Invoke(new Action(() => Log(message)));
            return;
        }

        string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
        string logEntry = $"[{timestamp}] {message}";
        
        // 添加到文本框
        logTextBox.AppendText(logEntry + Environment.NewLine);
        logTextBox.SelectionStart = logTextBox.Text.Length;
        logTextBox.ScrollToCaret();
        
        // 写入日志文件
        try
        {
            File.AppendAllText(logFilePath, logEntry + Environment.NewLine);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"写入日志文件失败: {ex.Message}");
        }
    }
}
