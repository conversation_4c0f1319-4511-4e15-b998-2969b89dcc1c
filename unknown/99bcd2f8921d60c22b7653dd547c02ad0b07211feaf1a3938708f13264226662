// 识别白云石的代码
Dictionary<string, List<string>> brittleMinerals = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase)
{
    { "白云石", new List<string> { "白云石", "白云石%", "白云岩", "白云岩%", "dolomite", "dol", "bys", "bys%", "column43" } },
};

// 检查是否包含脆性矿物关键词
string[] brittleKeywords = { "石英", "quartz", "白云石", "dolomite", "菱铁矿", "siderite", "斜长石", "plagioclase", "钾长石", "k-feldspar", "正长石" };
foreach (string keyword in brittleKeywords)
{
    if (columnName.Contains(keyword, StringComparison.OrdinalIgnoreCase))
    {
        System.Diagnostics.Debug.WriteLine($"包含脆性矿物关键词: {columnName} -> {keyword} -> 是脆性矿物");
        return true;
    }
}

// 识别黏土矿物的代码
Dictionary<string, List<string>> ductileMinerals = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase)
{
    { "黏土矿物总量", new List<string> { "黏土矿物总量", "黏土矿物总量%", "黏土矿物总量%z", "黏土矿物相对含量", "黏土矿物相对含量%",
                                  "粘土矿物总量", "粘土矿物总量%", "粘土矿物相对含量", "粘土矿物相对含量%",
                                  "黏土", "黏土%", "粘土", "粘土%", "clay", "clay%", "clay minerals", "clay minerals%",
                                  "clay content", "clay content%", "clay total", "clay total%", "total clay", "total clay%",
                                  "nt", "nt%", "ntw", "ntw%", "zt", "zt%", "黏土总量", "黏土总量%", "粘土总量", "粘土总量%" } },
};

// 特殊方法：检查列名是否是黏土矿物总量%z
private bool IsClayTotalZ(string columnName)
{
    if (string.IsNullOrEmpty(columnName))
        return false;

    // 精确匹配黏土矿物总量%z
    bool isClayTotalZ =
        columnName.Equals("黏土矿物总量%z", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("粘土矿物总量%z", StringComparison.OrdinalIgnoreCase) ||
        columnName.IndexOf("黏土矿物总量%z", StringComparison.OrdinalIgnoreCase) >= 0 ||
        columnName.IndexOf("粘土矿物总量%z", StringComparison.OrdinalIgnoreCase) >= 0;

    // 确保不是TOC
    if (isClayTotalZ && IsTOC(columnName))
    {
        System.Diagnostics.Debug.WriteLine($"警告: 列名 '{columnName}' 同时匹配黏土矿物总量%z和TOC，优先识别为黏土矿物总量%z");
    }

    System.Diagnostics.Debug.WriteLine($"特殊检查是否是黏土矿物总量%z: {columnName} -> {isClayTotalZ}");
    return isClayTotalZ;
}

// 检查列名是否是塑性矿物
private bool IsDuctileMineral(string columnName)
{
    if (string.IsNullOrEmpty(columnName))
        return false;

    System.Diagnostics.Debug.WriteLine($"检查列名是否是塑性矿物: {columnName}");

    // 首先检查是否是黏土矿物总量%z
    if (IsClayTotalZ(columnName))
    {
        System.Diagnostics.Debug.WriteLine($"通过IsClayTotalZ确认: {columnName} -> 是塑性矿物");
        return true;
    }

    // 特殊处理黏土矿物总量%z和黏土矿物总量%
    if (columnName.Equals("黏土矿物总量%z", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("黏土矿物总量%", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("黏土矿物总量", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("粘土矿物总量%", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("粘土矿物总量", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("黏土矿物相对含量/%", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("黏土矿物相对含量%", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("黏土矿物相对含量", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("粘土矿物相对含量/%", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("粘土矿物相对含量%", StringComparison.OrdinalIgnoreCase) ||
        columnName.Equals("粘土矿物相对含量", StringComparison.OrdinalIgnoreCase))
    {
        System.Diagnostics.Debug.WriteLine($"特殊处理黏土矿物总量: {columnName} -> 是塑性矿物");
        return true;
    }

    if (columnName.Contains("黏土矿物") || columnName.Contains("粘土矿物"))
    {
        System.Diagnostics.Debug.WriteLine($"包含黏土矿物或粘土矿物: {columnName} -> 是塑性矿物");
        return true;
    }

    // 检查是否包含塑性矿物关键词
    string[] ductileKeywords = { "黏土", "粘土", "clay", "高岭石", "kaolinite", "绿泥石", "chlorite", "伊利石", "illite", "蒙脱石", "montmorillonite", "伊蒙混层", "i/s" };
    foreach (string keyword in ductileKeywords)
    {
        if (columnName.Contains(keyword, StringComparison.OrdinalIgnoreCase) && !IsTOC(columnName))
        {
            System.Diagnostics.Debug.WriteLine($"包含塑性矿物关键词: {columnName} -> {keyword} -> 是塑性矿物");
            return true;
        }
    }

    System.Diagnostics.Debug.WriteLine($"列名不是塑性矿物: {columnName}");
    return false;
}

// 强制检查黏土矿物总量%z列
private void ForceCheckClayTotalZColumn()
{
    try
    {
        System.Diagnostics.Debug.WriteLine("===== 开始强制检查黏土矿物总量%z列 =====");
        
        // 检查数据表中是否有黏土矿物总量%z列
        List<string> clayTotalZColumns = new List<string>();
        foreach (DataColumn column in _sourceData.Columns)
        {
            if (IsClayTotalZ(column.ColumnName))
            {
                clayTotalZColumns.Add(column.ColumnName);
                System.Diagnostics.Debug.WriteLine($"找到黏土矿物总量%z列: {column.ColumnName}");
            }
        }
        
        // 如果找到了黏土矿物总量%z列，确保它们在塑性矿物列表中
        foreach (string columnName in clayTotalZColumns)
        {
            // 如果在脆性矿物列表中，移除
            if (_brittleColumns.Contains(columnName))
            {
                _brittleColumns.Remove(columnName);
                lstBrittleColumns.Items.Remove(columnName);
                System.Diagnostics.Debug.WriteLine($"从脆性矿物列表中移除黏土矿物总量%z列: {columnName}");
            }
            
            // 如果不在塑性矿物列表中，添加
            if (!_ductileColumns.Contains(columnName))
            {
                _ductileColumns.Add(columnName);
                lstDuctileColumns.Items.Add(columnName);
                System.Diagnostics.Debug.WriteLine($"强制添加黏土矿物总量%z列到塑性矿物列表: {columnName}");
                
                // 从可用列表中移除
                if (lstAvailableColumns.Items.Contains(columnName))
                {
                    lstAvailableColumns.Items.Remove(columnName);
                }
            }
        }
        
        System.Diagnostics.Debug.WriteLine("===== 强制检查黏土矿物总量%z列完成 =====");
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"强制检查黏土矿物总量%z列时出错: {ex.Message}\n{ex.StackTrace}");
    }
}

// 直接检查是否有黏土矿物总量%z列的代码
// 直接检查是否有黏土矿物总量%z列
double clayTotalZValue = 0;
foreach (DataColumn column in _sourceData.Columns)
{
    if (column.ColumnName.Contains("黏土矿物总量%z") || column.ColumnName.Contains("粘土矿物总量%z"))
    {
        if (sourceRow[column] != DBNull.Value && double.TryParse(sourceRow[column].ToString(), out double value))
        {
            clayTotalZValue = value;
            System.Diagnostics.Debug.WriteLine($"行 {i + 1} 直接检查到黏土矿物总量%z列: {column.ColumnName} = {value}");
            
            // 检查这个值是否已经包含在塑性矿物总量中
            bool alreadyIncluded = false;
            foreach (string mineral in ductileMinerals)
            {
                if (mineral.StartsWith(column.ColumnName) || mineral.Contains("黏土矿物总量%z") || mineral.Contains("粘土矿物总量%z"))
                {
                    alreadyIncluded = true;
                    System.Diagnostics.Debug.WriteLine($"行 {i + 1} 黏土矿物总量%z已经包含在塑性矿物中: {mineral}");
                    break;
                }
            }
            
            // 如果没有包含，添加到塑性矿物总量中
            if (!alreadyIncluded)
            {
                ductileSum += value;
                ductileMinerals.Add($"{column.ColumnName}={value}");
                System.Diagnostics.Debug.WriteLine($"行 {i + 1} 强制添加黏土矿物总量%z到塑性矿物总量: {ductileSum}");
                
                // 确保结果表中有该列
                if (!_resultData.Columns.Contains(column.ColumnName))
                {
                    _resultData.Columns.Add(column.ColumnName, typeof(double));
                }
                
                resultRow[column.ColumnName] = value;
            }
        }
    }
}
