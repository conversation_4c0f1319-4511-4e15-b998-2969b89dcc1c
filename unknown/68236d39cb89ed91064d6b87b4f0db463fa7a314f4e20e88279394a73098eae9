矿物脆性指数分析系统 - 用户手册
=======================

目录
----
1. 系统简介
2. 安装说明
3. 登录系统
4. 主界面介绍
5. 数据导入
6. 曲线生成与分析
7. 数据交互
8. 常见问题
9. 技术支持

1. 系统简介
---------
矿物脆性指数分析系统是一款专为矿物脆性指数数据分析和可视化设计的软件。本系统可以帮助用户导入、分析和可视化矿物脆性指数数据，提供直观的图表展示和交互功能。

2. 安装说明
---------
系统要求：
- Windows 10/11
- .NET 8.0 或更高版本
- 最低屏幕分辨率: 1280x720

安装步骤：
1. 运行安装程序 BritSystemSetup.exe
2. 按照安装向导的提示完成安装
3. 安装完成后，可以从开始菜单或桌面快捷方式启动程序

3. 登录系统
---------
1. 启动程序后，会显示登录界面
2. 输入以下凭据：
   - 用户名: admin
   - 密码: 123
3. 点击"登录"按钮进入系统

4. 主界面介绍
-----------
主界面包含以下主要部分：
- 顶部菜单栏：提供系统功能入口
- 左侧功能区：包含"矿物组成法"等功能按钮
- 中央工作区：显示数据和图表
- 状态栏：显示系统状态和提示信息

5. 数据导入
---------
导入Excel数据：
1. 点击"矿物组成法"按钮
2. 在打开的窗口中，点击"导入Excel"按钮
3. 选择包含脆性指数数据的Excel文件
4. 系统会自动识别顶深、底深和脆性指数列
5. 导入成功后，数据将显示在左侧数据表中

数据格式要求：
- Excel文件应包含顶深/顶深度、底深/底深度和脆性指数列
- 列名可能略有不同，系统会自动识别
- 脆性指数值为0的数据点将被忽略
- 确保数据不包含NaN或无效值

6. 曲线生成与分析
--------------
生成曲线：
1. 导入数据后，点击"生成曲线"按钮
2. 系统会根据导入的数据生成脆性指数曲线
3. 曲线将显示在右侧图表区域

曲线分析：
- X轴表示脆性指数值
- Y轴表示深度
- 红色点表示数据点
- 青色线表示趋势线

7. 数据交互
---------
图表交互：
- 点击曲线上的数据点可以在数据表中高亮显示对应的行
- 鼠标悬停在数据点上会显示详细信息
- 使用鼠标滚轮可以缩放曲线
- 按住Ctrl键点击多个数据点可以同时选择多个点

数据操作：
- 选择要删除的数据点
- 点击"删除点"按钮
- 确认删除操作

8. 常见问题
---------
Q: 为什么我的Excel文件无法导入？
A: 请确保Excel文件包含顶深/顶深度、底深/底深度和脆性指数列，且数据格式正确。

Q: 为什么某些数据点没有显示在图表上？
A: 脆性指数值为0或NaN的数据点会被忽略。

Q: 如何重置图表缩放？
A: 双击图表区域可以重置缩放。

9. 技术支持
---------
如有问题，请联系开发团队。

版本信息
-------
当前版本: v1.1
发布日期: 2025年4月22日
