using System;
using System.IO;
using System.Text.Json;
using System.Windows.Forms;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;

namespace BritSystem
{
    public partial class BritSystemWebView : Form
    {
        private WebView2 webView;
        private string baseDirectory;

        // 模拟脆性指数系统的数据
        private class SystemData
        {
            public int TotalRecords { get; set; } = 1234;
            public int ActiveTasks { get; set; } = 5;
            public int GeneratedReports { get; set; } = 42;
            public string SystemStatus { get; set; } = "正常";
        }

        private SystemData systemData = new SystemData();

        public BritSystemWebView()
        {
            InitializeComponent();
            InitializeAsync();
        }

        private void InitializeComponent()
        {
            this.Text = "脆性指数系统";
            this.Size = new System.Drawing.Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;

            webView = new WebView2();
            webView.Dock = DockStyle.Fill;
            this.Controls.Add(webView);

            this.FormClosing += BritSystemWebView_FormClosing;
        }

        private async void InitializeAsync()
        {
            try
            {
                baseDirectory = AppDomain.CurrentDomain.BaseDirectory;

                // 检查WebView2运行时是否已安装
                if (!CheckWebView2Runtime())
                {
                    return;
                }

                // 初始化WebView2
                try
                {
                    await webView.EnsureCoreWebView2Async(null);
                }
                catch (Exception ex) when (ex.Message.Contains("WebView2 Runtime") || ex.Message.Contains("WebView2运行时"))
                {
                    ShowWebView2InstallationGuide();
                    return;
                }
                catch (Exception ex) when (ex.HResult == unchecked((int)0x80004004)) // E_ABORT
                {
                    MessageBox.Show("WebView2初始化被中止，请检查WebView2运行时是否正确安装或被其他程序占用。", "初始化错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                    return;
                }

                // 注册消息处理程序
                webView.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;

                // 导航到登录页面
                string loginPagePath = Path.Combine(baseDirectory, "index.html");
                webView.CoreWebView2.Navigate(new Uri(loginPagePath).AbsoluteUri);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化WebView2失败: {ex.Message} (0x{ex.HResult:X8})", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            string messageJson = e.WebMessageAsJson;
            try
            {
                // 解析从JavaScript发送的消息
                JsonDocument jsonDocument = JsonDocument.Parse(messageJson);
                JsonElement root = jsonDocument.RootElement;

                if (root.TryGetProperty("action", out JsonElement actionElement))
                {
                    string action = actionElement.GetString();

                    switch (action)
                    {
                        case "login":
                            HandleLogin(root);
                            break;
                        case "logout":
                            HandleLogout();
                            break;
                        case "getUserInfo":
                            SendUserInfo();
                            break;
                        case "getSystemStats":
                            SendSystemStats();
                            break;
                        case "getRecentActivities":
                            SendRecentActivities();
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理消息时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void HandleLogin(JsonElement message)
        {
            string username = "";
            string password = "";

            if (message.TryGetProperty("username", out JsonElement usernameElement))
            {
                username = usernameElement.GetString();
            }

            if (message.TryGetProperty("password", out JsonElement passwordElement))
            {
                password = passwordElement.GetString();
            }

            // 验证用户名和密码
            bool isValid = (username == "admin" && password == "123");

            // 发送验证结果回JavaScript
            string responseJson = JsonSerializer.Serialize(new
            {
                action = "loginResult",
                success = isValid,
                message = isValid ? "登录成功" : "用户名或密码错误"
            });

            webView.CoreWebView2.PostWebMessageAsJson(responseJson);

            // 如果登录成功，导航到仪表盘页面
            if (isValid)
            {
                string dashboardPagePath = Path.Combine(baseDirectory, "dashboard.html");
                webView.CoreWebView2.Navigate(new Uri(dashboardPagePath).AbsoluteUri);
            }
        }

        private void HandleLogout()
        {
            // 处理退出登录
            string loginPagePath = Path.Combine(baseDirectory, "index.html");
            webView.CoreWebView2.Navigate(new Uri(loginPagePath).AbsoluteUri);
        }

        private void SendUserInfo()
        {
            // 发送用户信息到JavaScript
            string responseJson = JsonSerializer.Serialize(new
            {
                action = "userInfo",
                username = "admin"
            });

            webView.CoreWebView2.PostWebMessageAsJson(responseJson);
        }

        private void SendSystemStats()
        {
            // 发送系统统计数据到JavaScript
            string responseJson = JsonSerializer.Serialize(new
            {
                action = "systemStats",
                stats = new[]
                {
                    new { value = systemData.TotalRecords.ToString(), description = "条记录" },
                    new { value = systemData.ActiveTasks.ToString(), description = "个进行中" },
                    new { value = systemData.GeneratedReports.ToString(), description = "份已生成" },
                    new { value = systemData.SystemStatus, description = "运行良好" }
                }
            });

            webView.CoreWebView2.PostWebMessageAsJson(responseJson);
        }

        private void SendRecentActivities()
        {
            // 发送最近活动数据到JavaScript
            string responseJson = JsonSerializer.Serialize(new
            {
                action = "recentActivities",
                activities = new[]
                {
                    new { time = DateTime.Now.AddHours(-1).ToString("yyyy-MM-dd HH:mm"), action = "数据导入", user = "admin", status = "success", statusText = "成功" },
                    new { time = DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm"), action = "指数计算", user = "admin", status = "success", statusText = "成功" },
                    new { time = DateTime.Now.AddHours(-5).ToString("yyyy-MM-dd HH:mm"), action = "报告生成", user = "admin", status = "success", statusText = "成功" },
                    new { time = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd HH:mm"), action = "系统备份", user = "admin", status = "success", statusText = "成功" }
                }
            });

            webView.CoreWebView2.PostWebMessageAsJson(responseJson);
        }

        private void BritSystemWebView_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 清理资源
            webView?.Dispose();
        }

        // 检查WebView2运行时是否已安装
        private bool CheckWebView2Runtime()
        {
            try
            {
                // 检查WebView2运行时版本
                string version = CoreWebView2Environment.GetAvailableBrowserVersionString();
                return !string.IsNullOrEmpty(version);
            }
            catch (Exception)
            {
                ShowWebView2InstallationGuide();
                return false;
            }
        }

        // 显示WebView2安装指南
        private void ShowWebView2InstallationGuide()
        {
            string message = "检测到系统未安装WebView2运行时环境，请按照以下步骤安装：\n\n" +
                            "1. 访问Microsoft官方下载页面：\n" +
                            "https://developer.microsoft.com/zh-cn/microsoft-edge/webview2/\n\n" +
                            "2. 下载并安装'Evergreen Bootstrapper'版本\n\n" +
                            "3. 安装完成后重新启动应用程序\n\n" +
                            "是否现在打开下载页面？";

            DialogResult result = MessageBox.Show(message, "需要安装WebView2运行时",
                                                MessageBoxButtons.YesNo, MessageBoxIcon.Information);

            if (result == DialogResult.Yes)
            {
                // 打开WebView2下载页面
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "https://developer.microsoft.com/zh-cn/microsoft-edge/webview2/",
                    UseShellExecute = true
                });
            }

            this.Close();
        }

        // 程序入口点
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // 添加全局异常处理
            Application.ThreadException += new System.Threading.ThreadExceptionEventHandler(Application_ThreadException);
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);

            try
            {
                Application.Run(new BritSystemWebView());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"程序启动失败: {ex.Message} (0x{ex.HResult:X8})", "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 线程异常处理
        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            MessageBox.Show($"应用程序发生异常: {e.Exception.Message} (0x{e.Exception.HResult:X8})", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        // 未处理异常处理
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = e.ExceptionObject as Exception;
            MessageBox.Show($"发生未处理的异常: {(ex != null ? ex.Message : "未知错误")} {(ex != null ? $"(0x{ex.HResult:X8})" : "")}", "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
