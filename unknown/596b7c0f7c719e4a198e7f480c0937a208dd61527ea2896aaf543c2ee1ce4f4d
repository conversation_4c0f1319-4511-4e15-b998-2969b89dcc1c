using System;
using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;

namespace BritSystem
{
    public static class ExcelFormulaParser
    {
        // 解析Excel公式并计算结果
        public static double ParseFormula(string formula, DataTable dataTable)
        {
            if (string.IsNullOrWhiteSpace(formula))
                return double.NaN;

            try
            {
                // 检查是否是类似 AU5+AT5+AS5+AR5+AQ5 的公式
                if (Regex.IsMatch(formula, @"^[A-Z]+\d+(\+[A-Z]+\d+)+$"))
                {
                    return ParseCellReferenceFormula(formula, dataTable);
                }
                
                // 其他类型的公式可以在这里添加处理逻辑
                
                return double.NaN;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析公式出错: {ex.Message}");
                return double.NaN;
            }
        }

        // 解析单元格引用公式，如 AU5+AT5+AS5+AR5+AQ5
        private static double ParseCellReferenceFormula(string formula, DataTable dataTable)
        {
            string[] cellReferences = formula.Split('+');
            double sum = 0;

            foreach (string cellRef in cellReferences)
            {
                double cellValue = GetCellValue(cellRef.Trim(), dataTable);
                if (!double.IsNaN(cellValue))
                {
                    sum += cellValue;
                    System.Diagnostics.Debug.WriteLine($"单元格 {cellRef} 的值: {cellValue}, 当前总和: {sum}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"单元格 {cellRef} 的值无效");
                }
            }

            return sum;
        }

        // 获取单元格的值
        private static double GetCellValue(string cellReference, DataTable dataTable)
        {
            try
            {
                // 解析单元格引用，如 AU5
                Match match = Regex.Match(cellReference, @"^([A-Z]+)(\d+)$");
                if (!match.Success)
                    return double.NaN;

                string colName = match.Groups[1].Value;
                int rowNumber = int.Parse(match.Groups[2].Value) - 1; // 转为0基索引

                // 将列名转换为列索引，如A=0, B=1, ..., Z=25, AA=26, ...
                int colIndex = 0;
                for (int i = 0; i < colName.Length; i++)
                {
                    colIndex = colIndex * 26 + (char.ToUpper(colName[i]) - 'A');
                }

                System.Diagnostics.Debug.WriteLine($"单元格引用 {cellReference}: 列={colName}(索引={colIndex}), 行={rowNumber + 1}");

                // 检查索引是否有效
                if (colIndex >= dataTable.Columns.Count || rowNumber >= dataTable.Rows.Count || rowNumber < 0)
                {
                    System.Diagnostics.Debug.WriteLine($"单元格索引超出范围: 列={colIndex}, 行={rowNumber}");
                    return double.NaN;
                }

                // 获取单元格的值
                object cellValue = dataTable.Rows[rowNumber][colIndex];
                if (cellValue == null || cellValue == DBNull.Value)
                    return 0; // 空单元格返回0

                // 尝试解析为数字
                if (double.TryParse(cellValue.ToString(), out double result))
                    return result;

                return 0; // 无法解析为数字时返回0
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取单元格值出错: {ex.Message}");
                return double.NaN;
            }
        }
    }
}
