using System;
using System.Windows.Forms;
using System.Drawing;

namespace NativeFormTest
{
    public class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblTitle;
        private Label lblSubtitle;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblError;
        private bool loginSuccessful = false;

        public string RequestedForm { get; private set; } = string.Empty;
        public string Username { get; private set; } = string.Empty;

        public LoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // 窗体设置
            this.Text = "脆性指数系统 - 登录";
            this.Size = new Size(400, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(33, 33, 33); // 深色背景

            // 标题
            lblTitle = new Label();
            lblTitle.Text = "脆性指数系统";
            lblTitle.Font = new Font("Microsoft YaHei", 20, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Height = 50;
            lblTitle.Padding = new Padding(0, 20, 0, 0);

            // 副标题
            lblSubtitle = new Label();
            lblSubtitle.Text = "请登录以继续";
            lblSubtitle.Font = new Font("Microsoft YaHei", 10);
            lblSubtitle.ForeColor = Color.White;
            lblSubtitle.TextAlign = ContentAlignment.MiddleCenter;
            lblSubtitle.Dock = DockStyle.Top;
            lblSubtitle.Height = 30;

            // 用户名标签
            lblUsername = new Label();
            lblUsername.Text = "用户名:";
            lblUsername.Font = new Font("Microsoft YaHei", 10);
            lblUsername.ForeColor = Color.White;
            lblUsername.Location = new Point(50, 100);
            lblUsername.Size = new Size(100, 25);

            // 用户名输入框
            txtUsername = new TextBox();
            txtUsername.Location = new Point(150, 100);
            txtUsername.Size = new Size(200, 25);
            txtUsername.Font = new Font("Microsoft YaHei", 10);

            // 密码标签
            lblPassword = new Label();
            lblPassword.Text = "密码:";
            lblPassword.Font = new Font("Microsoft YaHei", 10);
            lblPassword.ForeColor = Color.White;
            lblPassword.Location = new Point(50, 140);
            lblPassword.Size = new Size(100, 25);

            // 密码输入框
            txtPassword = new TextBox();
            txtPassword.Location = new Point(150, 140);
            txtPassword.Size = new Size(200, 25);
            txtPassword.Font = new Font("Microsoft YaHei", 10);
            txtPassword.PasswordChar = '*';

            // 登录按钮
            btnLogin = new Button();
            btnLogin.Text = "登录";
            btnLogin.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnLogin.Location = new Point(150, 190);
            btnLogin.Size = new Size(100, 35);
            btnLogin.BackColor = Color.Cyan;
            btnLogin.ForeColor = Color.Black;
            btnLogin.FlatStyle = FlatStyle.Flat;
            btnLogin.Click += BtnLogin_Click;

            // 错误信息标签
            lblError = new Label();
            lblError.Text = "";
            lblError.Font = new Font("Microsoft YaHei", 9);
            lblError.ForeColor = Color.Red;
            lblError.TextAlign = ContentAlignment.MiddleCenter;
            lblError.Location = new Point(50, 240);
            lblError.Size = new Size(300, 25);
            lblError.Visible = false;

            // 添加控件到窗体
            this.Controls.Add(lblTitle);
            this.Controls.Add(lblSubtitle);
            this.Controls.Add(lblUsername);
            this.Controls.Add(txtUsername);
            this.Controls.Add(lblPassword);
            this.Controls.Add(txtPassword);
            this.Controls.Add(btnLogin);
            this.Controls.Add(lblError);

            // 设置按键事件
            this.AcceptButton = btnLogin;
            this.FormClosing += LoginForm_FormClosing;
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            Username = txtUsername.Text.Trim();
            string password = txtPassword.Text.Trim();

            // 验证输入
            if (string.IsNullOrEmpty(Username) || string.IsNullOrEmpty(password))
            {
                ShowError("用户名和密码不能为空");
                return;
            }

            // 验证用户名和密码
            if (Username == "admin" && password == "123")
            {
                loginSuccessful = true;
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                ShowError("用户名或密码错误");
                txtPassword.Clear();
                txtPassword.Focus();
            }
        }

        private void ShowError(string message)
        {
            lblError.Text = message;
            lblError.Visible = true;
        }

        private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 如果不是通过成功登录关闭窗体，则设置DialogResult为Cancel
            if (!loginSuccessful && this.DialogResult != DialogResult.OK)
            {
                this.DialogResult = DialogResult.Cancel;
            }
        }
    }
}
