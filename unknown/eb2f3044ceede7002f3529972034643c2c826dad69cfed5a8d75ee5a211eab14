@echo off
echo 编译并运行WebView2测试程序...

set DOTNET_PATH=dotnet.exe
set PROJECT_DIR=%~dp0
set OUTPUT_DIR=%PROJECT_DIR%bin\Release\net8.0-windows
set CS_FILE=%PROJECT_DIR%WebView2Test.cs
set REFERENCES=-r:"%OUTPUT_DIR%\Microsoft.Web.WebView2.WinForms.dll" -r:"%OUTPUT_DIR%\Microsoft.Web.WebView2.Core.dll"

echo 编译WebView2Test.cs...
%DOTNET_PATH% build "%PROJECT_DIR%BritSystem.csproj" -c Release

echo 运行WebView2Test...
cd /d "%OUTPUT_DIR%"
%DOTNET_PATH% exec "%OUTPUT_DIR%\BritSystem.dll" WebView2Test

echo 完成。
pause
