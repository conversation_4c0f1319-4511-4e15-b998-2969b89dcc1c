﻿namespace BritSystem
{
    partial class AlgorithmFormulaCal
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            leftPanel = new Panel();
            ICombox = new ComboBox();
            btnBottomDepth = new Button();
            btnTopDepth = new Button();
            lblBottomDepth = new Label();
            lblTopDepth = new Label();
            btnRemoveDuctile = new Button();
            btnRemoveBrittle = new Button();
            btnAddDuctile = new Button();
            btnAddBrittle = new Button();
            lstDuctileColumns = new ListBox();
            lblDuctile = new Label();
            lstBrittleColumns = new ListBox();
            lblBrittle = new Label();
            lstAvailableColumns = new ListBox();
            lblAvailable = new Label();
            rightPanel = new Panel();
            BackBtnGenerateCurve = new Button();
            dgvResult = new DataGridView();
            resultLabel = new Label();
            btnCalculate = new Button();
            btnLoadData = new Button();
            btnVisualize = new Button();
            btnSaveData = new Button();
            formulaPanel = new Panel();
            formulaText = new Label();
            formulaLabel = new Label();
            btnManualMapping = new Button();
            leftPanel.SuspendLayout();
            rightPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvResult).BeginInit();
            formulaPanel.SuspendLayout();
            SuspendLayout();
            //
            // leftPanel
            //
            leftPanel.BackColor = Color.FromArgb(50, 50, 50);
            leftPanel.Controls.Add(ICombox);
            leftPanel.Controls.Add(btnBottomDepth);
            leftPanel.Controls.Add(btnTopDepth);
            leftPanel.Controls.Add(lblBottomDepth);
            leftPanel.Controls.Add(lblTopDepth);
            leftPanel.Controls.Add(btnRemoveDuctile);
            leftPanel.Controls.Add(btnRemoveBrittle);
            leftPanel.Controls.Add(btnAddDuctile);
            leftPanel.Controls.Add(btnAddBrittle);
            leftPanel.Controls.Add(lstDuctileColumns);
            leftPanel.Controls.Add(lblDuctile);
            leftPanel.Controls.Add(lstBrittleColumns);
            leftPanel.Controls.Add(lblBrittle);
            leftPanel.Controls.Add(lstAvailableColumns);
            leftPanel.Controls.Add(lblAvailable);
            leftPanel.Dock = DockStyle.Left;
            leftPanel.Location = new Point(0, 0);
            leftPanel.Margin = new Padding(6);
            leftPanel.Name = "leftPanel";
            leftPanel.Size = new Size(758, 1410);
            leftPanel.TabIndex = 0;
            //
            // ICombox
            //
            ICombox.FormattingEnabled = true;
            ICombox.Location = new Point(286, 24);
            ICombox.Margin = new Padding(4);
            ICombox.Name = "ICombox";
            ICombox.Size = new Size(312, 32);
            ICombox.TabIndex = 13;
            //
            // btnBottomDepth
            //
            btnBottomDepth.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            btnBottomDepth.Location = new Point(192, 546);
            btnBottomDepth.Margin = new Padding(4);
            btnBottomDepth.Name = "btnBottomDepth";
            btnBottomDepth.Size = new Size(364, 36);
            btnBottomDepth.TabIndex = 12;
            btnBottomDepth.Text = "选择底深列";
            btnBottomDepth.UseVisualStyleBackColor = true;
            //
            // btnTopDepth
            //
            btnTopDepth.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            btnTopDepth.Location = new Point(192, 456);
            btnTopDepth.Margin = new Padding(4);
            btnTopDepth.Name = "btnTopDepth";
            btnTopDepth.Size = new Size(364, 36);
            btnTopDepth.TabIndex = 7;
            btnTopDepth.Text = "选择顶深列";
            btnTopDepth.UseVisualStyleBackColor = true;
            //
            // lblBottomDepth
            //
            lblBottomDepth.AutoSize = true;
            lblBottomDepth.Location = new Point(36, 546);
            lblBottomDepth.Margin = new Padding(4, 0, 4, 0);
            lblBottomDepth.Name = "lblBottomDepth";
            lblBottomDepth.Size = new Size(71, 24);
            lblBottomDepth.TabIndex = 11;
            lblBottomDepth.Text = "底深/m";
            //
            // lblTopDepth
            //
            lblTopDepth.AutoSize = true;
            lblTopDepth.Location = new Point(36, 456);
            lblTopDepth.Margin = new Padding(4, 0, 4, 0);
            lblTopDepth.Name = "lblTopDepth";
            lblTopDepth.Size = new Size(71, 24);
            lblTopDepth.TabIndex = 10;
            lblTopDepth.Text = "顶深/m";
            //
            // btnRemoveDuctile
            //
            btnRemoveDuctile.BackColor = Color.FromArgb(80, 80, 80);
            btnRemoveDuctile.FlatStyle = FlatStyle.Flat;
            btnRemoveDuctile.ForeColor = Color.White;
            btnRemoveDuctile.Location = new Point(386, 1036);
            btnRemoveDuctile.Margin = new Padding(6);
            btnRemoveDuctile.Name = "btnRemoveDuctile";
            btnRemoveDuctile.Size = new Size(147, 54);
            btnRemoveDuctile.TabIndex = 9;
            btnRemoveDuctile.Text = "← 移除";
            btnRemoveDuctile.UseVisualStyleBackColor = false;
            btnRemoveDuctile.Click += btnRemoveDuctile_Click;
            //
            // btnRemoveBrittle
            //
            btnRemoveBrittle.BackColor = Color.FromArgb(80, 80, 80);
            btnRemoveBrittle.FlatStyle = FlatStyle.Flat;
            btnRemoveBrittle.ForeColor = Color.White;
            btnRemoveBrittle.Location = new Point(386, 638);
            btnRemoveBrittle.Margin = new Padding(6);
            btnRemoveBrittle.Name = "btnRemoveBrittle";
            btnRemoveBrittle.Size = new Size(147, 54);
            btnRemoveBrittle.TabIndex = 8;
            btnRemoveBrittle.Text = "← 移除";
            btnRemoveBrittle.UseVisualStyleBackColor = false;
            btnRemoveBrittle.Click += btnRemoveBrittle_Click;
            //
            // btnAddDuctile
            //
            btnAddDuctile.BackColor = Color.SteelBlue;
            btnAddDuctile.FlatStyle = FlatStyle.Flat;
            btnAddDuctile.ForeColor = Color.White;
            btnAddDuctile.Location = new Point(550, 1036);
            btnAddDuctile.Margin = new Padding(6);
            btnAddDuctile.Name = "btnAddDuctile";
            btnAddDuctile.Size = new Size(147, 54);
            btnAddDuctile.TabIndex = 7;
            btnAddDuctile.Text = "→ 塑性";
            btnAddDuctile.UseVisualStyleBackColor = false;
            btnAddDuctile.Click += btnAddDuctile_Click;
            //
            // btnAddBrittle
            //
            btnAddBrittle.BackColor = Color.SteelBlue;
            btnAddBrittle.FlatStyle = FlatStyle.Flat;
            btnAddBrittle.ForeColor = Color.White;
            btnAddBrittle.Location = new Point(550, 638);
            btnAddBrittle.Margin = new Padding(6);
            btnAddBrittle.Name = "btnAddBrittle";
            btnAddBrittle.Size = new Size(147, 54);
            btnAddBrittle.TabIndex = 6;
            btnAddBrittle.Text = "→ 脆性";
            btnAddBrittle.UseVisualStyleBackColor = false;
            btnAddBrittle.Click += btnAddBrittle_Click;
            //
            // lstDuctileColumns
            //
            lstDuctileColumns.BackColor = Color.FromArgb(120, 80, 100);
            lstDuctileColumns.BorderStyle = BorderStyle.FixedSingle;
            lstDuctileColumns.ForeColor = Color.White;
            lstDuctileColumns.FormattingEnabled = true;
            lstDuctileColumns.ItemHeight = 24;
            lstDuctileColumns.Location = new Point(36, 1112);
            lstDuctileColumns.Margin = new Padding(6);
            lstDuctileColumns.Name = "lstDuctileColumns";
            lstDuctileColumns.Size = new Size(659, 290);
            lstDuctileColumns.TabIndex = 5;
            //
            // lblDuctile
            //
            lblDuctile.AutoSize = true;
            lblDuctile.ForeColor = Color.White;
            lblDuctile.Location = new Point(36, 1052);
            lblDuctile.Margin = new Padding(6, 0, 6, 0);
            lblDuctile.Name = "lblDuctile";
            lblDuctile.Size = new Size(86, 24);
            lblDuctile.TabIndex = 4;
            lblDuctile.Text = "塑性矿物:";
            //
            // lstBrittleColumns
            //
            lstBrittleColumns.BackColor = Color.FromArgb(80, 100, 120);
            lstBrittleColumns.BorderStyle = BorderStyle.FixedSingle;
            lstBrittleColumns.ForeColor = Color.White;
            lstBrittleColumns.FormattingEnabled = true;
            lstBrittleColumns.ItemHeight = 24;
            lstBrittleColumns.Location = new Point(28, 717);
            lstBrittleColumns.Margin = new Padding(6);
            lstBrittleColumns.Name = "lstBrittleColumns";
            lstBrittleColumns.Size = new Size(676, 290);
            lstBrittleColumns.TabIndex = 3;
            //
            // lblBrittle
            //
            lblBrittle.AutoSize = true;
            lblBrittle.ForeColor = Color.White;
            lblBrittle.Location = new Point(36, 646);
            lblBrittle.Margin = new Padding(6, 0, 6, 0);
            lblBrittle.Name = "lblBrittle";
            lblBrittle.Size = new Size(86, 24);
            lblBrittle.TabIndex = 2;
            lblBrittle.Text = "脆性矿物:";
            //
            // lstAvailableColumns
            //
            lstAvailableColumns.BackColor = Color.FromArgb(60, 60, 60);
            lstAvailableColumns.BorderStyle = BorderStyle.FixedSingle;
            lstAvailableColumns.ForeColor = Color.White;
            lstAvailableColumns.FormattingEnabled = true;
            lstAvailableColumns.ItemHeight = 24;
            lstAvailableColumns.Location = new Point(36, 90);
            lstAvailableColumns.Margin = new Padding(6);
            lstAvailableColumns.Name = "lstAvailableColumns";
            lstAvailableColumns.SelectionMode = SelectionMode.MultiExtended;
            lstAvailableColumns.Size = new Size(659, 290);
            lstAvailableColumns.TabIndex = 1;
            lstAvailableColumns.DoubleClick += lstAvailableColumns_DoubleClick;
            //
            // lblAvailable
            //
            lblAvailable.AutoSize = true;
            lblAvailable.ForeColor = Color.White;
            lblAvailable.Location = new Point(36, 36);
            lblAvailable.Margin = new Padding(6, 0, 6, 0);
            lblAvailable.Name = "lblAvailable";
            lblAvailable.Size = new Size(68, 24);
            lblAvailable.TabIndex = 0;
            lblAvailable.Text = "可用列:";
            //
            // rightPanel
            //
            rightPanel.BackColor = Color.FromArgb(40, 40, 40);
            rightPanel.Controls.Add(BackBtnGenerateCurve);
            rightPanel.Controls.Add(dgvResult);
            rightPanel.Controls.Add(resultLabel);
            rightPanel.Controls.Add(btnCalculate);
            rightPanel.Controls.Add(btnLoadData);
            rightPanel.Controls.Add(btnVisualize);
            rightPanel.Controls.Add(btnSaveData);
            rightPanel.Controls.Add(formulaPanel);
            rightPanel.Controls.Add(btnManualMapping);
            rightPanel.Dock = DockStyle.Fill;
            rightPanel.Location = new Point(758, 0);
            rightPanel.Margin = new Padding(6);
            rightPanel.Name = "rightPanel";
            rightPanel.Size = new Size(1392, 1410);
            rightPanel.TabIndex = 1;
            //
            // BackBtnGenerateCurve
            //
            BackBtnGenerateCurve.BackColor = Color.RoyalBlue;
            BackBtnGenerateCurve.FlatStyle = FlatStyle.Flat;
            BackBtnGenerateCurve.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            BackBtnGenerateCurve.ForeColor = Color.White;
            BackBtnGenerateCurve.Location = new Point(36, 1330);
            BackBtnGenerateCurve.Margin = new Padding(6);
            BackBtnGenerateCurve.Name = "BackBtnGenerateCurve";
            BackBtnGenerateCurve.Size = new Size(274, 72);
            BackBtnGenerateCurve.TabIndex = 8;
            BackBtnGenerateCurve.Text = "脆性指数曲线";
            BackBtnGenerateCurve.UseVisualStyleBackColor = false;
            //
            // dgvResult
            //
            dgvResult.AllowUserToAddRows = false;
            dgvResult.AllowUserToDeleteRows = false;
            dgvResult.BackgroundColor = Color.FromArgb(60, 60, 60);
            dgvResult.BorderStyle = BorderStyle.Fixed3D;
            dgvResult.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvResult.GridColor = Color.FromArgb(80, 80, 80);
            dgvResult.Location = new Point(36, 357);
            dgvResult.Margin = new Padding(6);
            dgvResult.Name = "dgvResult";
            dgvResult.ReadOnly = true;
            dgvResult.RowHeadersWidth = 62;
            dgvResult.RowTemplate.Height = 25;
            dgvResult.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvResult.Size = new Size(1336, 946);
            dgvResult.TabIndex = 3;
            dgvResult.CellClick += DgvResult_CellClick;
            dgvResult.CellFormatting += DgvResult_CellFormatting;
            //
            // resultLabel
            //
            resultLabel.AutoSize = true;
            resultLabel.ForeColor = Color.White;
            resultLabel.Location = new Point(36, 324);
            resultLabel.Margin = new Padding(6, 0, 6, 0);
            resultLabel.Name = "resultLabel";
            resultLabel.Size = new Size(86, 24);
            resultLabel.TabIndex = 2;
            resultLabel.Text = "计算结果:";
            //
            // btnCalculate
            //
            btnCalculate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnCalculate.BackColor = Color.SeaGreen;
            btnCalculate.FlatStyle = FlatStyle.Flat;
            btnCalculate.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnCalculate.ForeColor = Color.White;
            btnCalculate.Location = new Point(1096, 216);
            btnCalculate.Margin = new Padding(6);
            btnCalculate.Name = "btnCalculate";
            btnCalculate.Size = new Size(274, 72);
            btnCalculate.TabIndex = 1;
            btnCalculate.Text = "计算脆性指数";
            btnCalculate.UseVisualStyleBackColor = false;
            btnCalculate.Click += btnCalculate_Click;
            //
            // btnLoadData
            //
            btnLoadData.BackColor = Color.RoyalBlue;
            btnLoadData.FlatStyle = FlatStyle.Flat;
            btnLoadData.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnLoadData.ForeColor = Color.White;
            btnLoadData.Location = new Point(39, 216);
            btnLoadData.Margin = new Padding(6);
            btnLoadData.Name = "btnLoadData";
            btnLoadData.Size = new Size(274, 72);
            btnLoadData.TabIndex = 4;
            btnLoadData.Text = "加载数据";
            btnLoadData.UseVisualStyleBackColor = false;
            btnLoadData.Click += btnLoadData_Click;
            //
            // btnVisualize
            //
            btnVisualize.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnVisualize.BackColor = Color.DarkOrange;
            btnVisualize.FlatStyle = FlatStyle.Flat;
            btnVisualize.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnVisualize.ForeColor = Color.White;
            btnVisualize.Location = new Point(740, 216);
            btnVisualize.Margin = new Padding(6);
            btnVisualize.Name = "btnVisualize";
            btnVisualize.Size = new Size(274, 72);
            btnVisualize.TabIndex = 5;
            btnVisualize.Text = "可视化数据";
            btnVisualize.UseVisualStyleBackColor = false;
            btnVisualize.Click += BtnVisualize_Click;
            //
            // btnSaveData
            //
            btnSaveData.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnSaveData.BackColor = Color.SteelBlue;
            btnSaveData.FlatStyle = FlatStyle.Flat;
            btnSaveData.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnSaveData.ForeColor = Color.White;
            btnSaveData.Location = new Point(1118, 1420);
            btnSaveData.Margin = new Padding(6);
            btnSaveData.Name = "btnSaveData";
            btnSaveData.Size = new Size(274, 72);
            btnSaveData.TabIndex = 6;
            btnSaveData.Text = "保存数据";
            btnSaveData.UseVisualStyleBackColor = false;
            btnSaveData.Click += BtnSaveData_Click;
            //
            // formulaPanel
            //
            formulaPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            formulaPanel.AutoSize = true;
            formulaPanel.BackColor = Color.FromArgb(50, 50, 50);
            formulaPanel.BorderStyle = BorderStyle.FixedSingle;
            formulaPanel.Controls.Add(formulaText);
            formulaPanel.Controls.Add(formulaLabel);
            formulaPanel.Location = new Point(36, 36);
            formulaPanel.Margin = new Padding(6);
            formulaPanel.Name = "formulaPanel";
            formulaPanel.Size = new Size(1342, 143);
            formulaPanel.TabIndex = 0;
            //
            // formulaText
            //
            formulaText.AutoSize = true;
            formulaText.Font = new Font("微软雅黑", 10F, FontStyle.Italic);
            formulaText.ForeColor = Color.Cyan;
            formulaText.Location = new Point(18, 72);
            formulaText.Margin = new Padding(6, 0, 6, 0);
            formulaText.Name = "formulaText";
            formulaText.Size = new Size(558, 27);
            formulaText.TabIndex = 1;
            formulaText.Text = "脆性指数 = [V脆性矿物 / (V脆性矿物 + V塑性矿物)] × 100%";
            //
            // formulaLabel
            //
            formulaLabel.AutoSize = true;
            formulaLabel.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            formulaLabel.ForeColor = Color.White;
            formulaLabel.Location = new Point(18, 18);
            formulaLabel.Margin = new Padding(6, 0, 6, 0);
            formulaLabel.Name = "formulaLabel";
            formulaLabel.Size = new Size(178, 27);
            formulaLabel.TabIndex = 0;
            formulaLabel.Text = "脆性指数计算公式:";
            //
            // btnManualMapping
            //
            btnManualMapping.BackColor = Color.DarkSlateBlue;
            btnManualMapping.FlatStyle = FlatStyle.Flat;
            btnManualMapping.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnManualMapping.ForeColor = Color.White;
            btnManualMapping.Location = new Point(381, 216);
            btnManualMapping.Margin = new Padding(6);
            btnManualMapping.Name = "btnManualMapping";
            btnManualMapping.Size = new Size(274, 72);
            btnManualMapping.TabIndex = 7;
            btnManualMapping.Text = "手动映射列";
            btnManualMapping.UseVisualStyleBackColor = false;
            btnManualMapping.Click += BtnManualMapping_Click;
            //
            // AlgorithmFormulaCal
            //
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(45, 45, 45);
            ClientSize = new Size(2150, 1410);
            Controls.Add(rightPanel);
            Controls.Add(leftPanel);
            ForeColor = Color.White;
            Margin = new Padding(6);
            Name = "AlgorithmFormulaCal";
            Text = "脆性指数计算器";
            Load += AlgorithmFormulaCal_Load;
            leftPanel.ResumeLayout(false);
            leftPanel.PerformLayout();
            rightPanel.ResumeLayout(false);
            rightPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvResult).EndInit();
            formulaPanel.ResumeLayout(false);
            formulaPanel.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.Panel leftPanel;
        private System.Windows.Forms.Button btnRemoveDuctile;
        private System.Windows.Forms.Button btnRemoveBrittle;
        private System.Windows.Forms.Button btnAddDuctile;
        private System.Windows.Forms.Button btnAddBrittle;
        private System.Windows.Forms.ListBox lstDuctileColumns;
        private System.Windows.Forms.Label lblDuctile;
        private System.Windows.Forms.ListBox lstBrittleColumns;
        private System.Windows.Forms.Label lblBrittle;
        private System.Windows.Forms.ListBox lstAvailableColumns;
        private System.Windows.Forms.Label lblAvailable;
        private System.Windows.Forms.Panel rightPanel;
        private System.Windows.Forms.DataGridView dgvResult;
        private System.Windows.Forms.Label resultLabel;
        private System.Windows.Forms.Button btnCalculate;
        private System.Windows.Forms.Button btnLoadData;
        private System.Windows.Forms.Button btnVisualize;
        private System.Windows.Forms.Button btnSaveData;
        private System.Windows.Forms.Button btnManualMapping;
        private System.Windows.Forms.Panel formulaPanel;
        private System.Windows.Forms.Label formulaText;
        private System.Windows.Forms.Label formulaLabel;
        private Label lblBottomDepth;
        private Label lblTopDepth;
        private Button btnTopDepth;
        private Button btnBottomDepth;
        private ComboBox ICombox;
        private Button BackBtnGenerateCurve;
    }
}