using System;
using System.Drawing;
using System.Windows.Forms;

namespace BritSystem
{
    /// <summary>
    /// 应用程序配置类，用于集中管理应用程序的全局设置
    /// </summary>
    public static class AppConfig
    {
        /// <summary>
        /// 应用程序默认字体
        /// </summary>
        public static Font DefaultFont { get; } = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point);

        /// <summary>
        /// 应用程序默认高DPI模式
        /// </summary>
        public static HighDpiMode DefaultHighDpiMode { get; } = HighDpiMode.PerMonitorV2;

        /// <summary>
        /// 应用程序默认自动缩放模式
        /// </summary>
        public static AutoScaleMode DefaultAutoScaleMode { get; } = AutoScaleMode.Dpi;

        /// <summary>
        /// 应用程序默认主题颜色
        /// </summary>
        public static class ThemeColors
        {
            public static Color Primary { get; } = Color.FromArgb(0, 120, 212);
            public static Color Secondary { get; } = Color.FromArgb(0, 99, 177);
            public static Color Background { get; } = Color.White;
            public static Color Text { get; } = Color.Black;
            public static Color TextLight { get; } = Color.Gray;
            public static Color Success { get; } = Color.FromArgb(16, 124, 16);
            public static Color Warning { get; } = Color.FromArgb(234, 157, 0);
            public static Color Error { get; } = Color.FromArgb(232, 17, 35);
        }

        /// <summary>
        /// 应用高DPI设置到窗体
        /// </summary>
        /// <param name="form">要应用设置的窗体</param>
        public static void ApplyHighDpiSettings(Form form)
        {
            if (form == null)
                return;

            // 设置自动缩放模式
            form.AutoScaleMode = DefaultAutoScaleMode;

            // 启用双缓冲和优化绘制
            form.GetType().GetProperty("DoubleBuffered", 
                System.Reflection.BindingFlags.Instance | 
                System.Reflection.BindingFlags.NonPublic)?.SetValue(form, true);

            // 设置控件样式
            foreach (Control control in GetAllControls(form))
            {
                if (control.Font != null)
                {
                    // 使用微软雅黑字体以获得更好的高DPI显示效果
                    float fontSize = control.Font.Size;
                    FontStyle fontStyle = control.Font.Style;
                    control.Font = new Font("Microsoft YaHei UI", fontSize, fontStyle);
                }
            }
        }

        /// <summary>
        /// 获取窗体中的所有控件（包括嵌套控件）
        /// </summary>
        private static System.Collections.Generic.List<Control> GetAllControls(Control parent)
        {
            var controls = new System.Collections.Generic.List<Control>();
            GetAllControlsRecursive(parent, controls);
            return controls;
        }

        /// <summary>
        /// 递归获取控件及其子控件
        /// </summary>
        private static void GetAllControlsRecursive(Control parent, System.Collections.Generic.List<Control> result)
        {
            foreach (Control child in parent.Controls)
            {
                result.Add(child);
                GetAllControlsRecursive(child, result);
            }
        }
    }
}
