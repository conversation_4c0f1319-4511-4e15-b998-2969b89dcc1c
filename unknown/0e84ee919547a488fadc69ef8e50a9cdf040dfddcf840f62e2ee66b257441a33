@echo off
echo BritSystem Installer Builder
echo ==========================
echo.

REM Create output directory
if not exist "Output" mkdir "Output"

REM Find Inno Setup compiler
set "ISCC_PATH="

if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
)

if "%ISCC_PATH%"=="" (
    echo Inno Setup not found.
    echo Please install Inno Setup 6.
    pause
    exit /b 1
)

REM Check if Release build exists
if not exist "bin\Release\net8.0-windows\BritSystem.exe" (
    echo Warning: Release build not found.
    echo Continue anyway? (Y/N, default=N):
    set /p BUILD_CONTINUE=

    if /i not "%BUILD_CONTINUE%"=="Y" (
        echo Cancelled. Please build the project first.
        pause
        exit /b 1
    )
)

REM Compile installer
echo Compiling installer...
echo Output directory: %CD%\Output

REM Run Inno Setup compiler
echo 使用 BritSystemSetup.iss 编译安装程序...
"%ISCC_PATH%" "BritSystemSetup.iss" /O"Output"

REM Check result
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    if exist "Output\BritSystemSetup.exe" (
        echo Installer location: %CD%\Output\BritSystemSetup.exe
        echo Installer version: 1.1

        REM Open output directory
        start "" "Output"
    ) else (
        echo Warning: Installer not found in expected location.
        echo Expected path: %CD%\Output\BritSystemSetup.exe
    )
) else (
    echo Build failed. Check error messages.
    echo.
    echo Common issues:
    echo 1. Source files not found - Make sure you've built the project in Release mode
    echo 2. Permission issues - Make sure you have write access to the output directory
    echo 3. Path issues - Avoid paths with special characters or spaces
)

pause
