# 创建一个新的AlgorithmFormulaCal.cs文件，添加列名映射和安全数据解析功能
$filePath = "c:\Users\<USER>\Desktop\Brittleness index system\repos\BritSystem\AlgorithmFormulaCal.cs"
$content = Get-Content $filePath -Raw

# 修改自动选择脆性和塑性矿物的部分
$pattern1 = '(?s)// 脆性矿物：石英、长石类.*?// 塑性矿物：黏土、碳酸盐类'
$replacement1 = @"
// 定义要查找的脆性矿物列名
string[] brittleMinerals = new string[] { "石英%", "石英", "斜长石%", "斜长石", "钾长石（正长石）%", "钾长石（正长石）", "长石%", "长石" };

// 添加脆性矿物
foreach (string mineral in brittleMinerals)
{
    string actualColumnName = GetActualColumnName(mineral);
    if (_sourceData.Columns.Contains(actualColumnName) && !_brittleColumns.Contains(mineral))
    {
        _brittleColumns.Add(mineral);
        lstBrittleColumns.Items.Add(mineral);
        System.Diagnostics.Debug.WriteLine($"自动添加脆性矿物: {mineral} (实际列名: {actualColumnName})");
    }
}

// 塑性矿物：黏土、碳酸盐类
"@

$content = $content -replace $pattern1, $replacement1

# 修改塑性矿物部分
$pattern2 = '(?s)// 塑性矿物：黏土、碳酸盐类.*?if \(_sourceData\.Columns\.Contains\("黏土矿物总量%"\)\).*?lstDuctileColumns\.Items\.Add\("菱铁矿%"\);\s+}'
$replacement2 = @"
// 塑性矿物：黏土、碳酸盐类
// 定义要查找的塑性矿物列名
string[] ductileMinerals = new string[] { "黏土矿物总量%", "黏土矿物总量", "黏土矿物%", "黏土矿物", "白云石%", "白云石", "菱铁矿%", "菱铁矿" };

// 添加塑性矿物
foreach (string mineral in ductileMinerals)
{
    string actualColumnName = GetActualColumnName(mineral);
    if (_sourceData.Columns.Contains(actualColumnName) && !_ductileColumns.Contains(mineral))
    {
        _ductileColumns.Add(mineral);
        lstDuctileColumns.Items.Add(mineral);
        System.Diagnostics.Debug.WriteLine($"自动添加塑性矿物: {mineral} (实际列名: {actualColumnName})");
    }
}
"@

$content = $content -replace $pattern2, $replacement2

# 保存修改后的文件
$content | Set-Content $filePath -Encoding UTF8
