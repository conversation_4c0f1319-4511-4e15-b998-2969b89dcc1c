using System;
using System.IO;
using System.Windows.Forms;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.WinForms;
using System.Text.Json;
using System.Diagnostics;

namespace BritSystem
{
    public class VerySimpleWebView2Test : Form
    {
        private WebView2 webView;
        private TextBox logTextBox;
        private string baseDirectory;

        public VerySimpleWebView2Test()
        {
            InitializeComponent();
            baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            this.Load += VerySimpleWebView2Test_Load;
        }

        private void InitializeComponent()
        {
            this.Text = "极简WebView2测试";
            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // 创建分割面板
            SplitContainer splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Horizontal;
            splitContainer.SplitterDistance = 400;
            this.Controls.Add(splitContainer);

            // 创建WebView2控件
            webView = new WebView2();
            webView.Dock = DockStyle.Fill;
            splitContainer.Panel1.Controls.Add(webView);

            // 创建日志文本框
            logTextBox = new TextBox();
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.Dock = DockStyle.Fill;
            logTextBox.ReadOnly = true;
            logTextBox.BackColor = System.Drawing.Color.Black;
            logTextBox.ForeColor = System.Drawing.Color.Lime;
            logTextBox.Font = new System.Drawing.Font("Consolas", 10);
            splitContainer.Panel2.Controls.Add(logTextBox);
        }

        private async void VerySimpleWebView2Test_Load(object sender, EventArgs e)
        {
            try
            {
                Log("正在初始化WebView2...");
                
                // 使用最简单的方式初始化WebView2
                await webView.EnsureCoreWebView2Async();
                Log("WebView2初始化成功！");

                // 设置WebView2的设置
                webView.CoreWebView2.Settings.AreHostObjectsAllowed = true;
                webView.CoreWebView2.Settings.IsWebMessageEnabled = true;
                webView.CoreWebView2.Settings.AreDefaultScriptDialogsEnabled = true;
                webView.CoreWebView2.Settings.IsScriptEnabled = true;
                Log("WebView2设置已配置");

                // 注册消息处理程序
                webView.CoreWebView2.WebMessageReceived += CoreWebView2_WebMessageReceived;
                Log("WebMessageReceived处理程序已注册");

                // 导航到测试页面
                string testPagePath = Path.Combine(baseDirectory, "SimpleWebView2Test.html");
                if (File.Exists(testPagePath))
                {
                    Log($"找到测试页面: {testPagePath}");
                    string htmlContent = File.ReadAllText(testPagePath);
                    webView.CoreWebView2.NavigateToString(htmlContent);
                    Log("已加载测试页面");
                }
                else
                {
                    Log($"错误: 找不到测试页面 {testPagePath}");
                    MessageBox.Show($"找不到测试页面: {testPagePath}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Log($"错误: {ex.Message}");
                Log($"错误类型: {ex.GetType().Name}");
                Log($"堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"初始化WebView2失败: {ex.Message}\n\n类型: {ex.GetType().Name}\n\n堆栈跟踪: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CoreWebView2_WebMessageReceived(object sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            string messageJson = e.WebMessageAsJson;
            Log($"收到消息: {messageJson}");

            try
            {
                // 解析从JavaScript发送的消息
                JsonDocument jsonDocument = JsonDocument.Parse(messageJson);
                JsonElement root = jsonDocument.RootElement;

                if (root.TryGetProperty("action", out JsonElement actionElement))
                {
                    string action = actionElement.GetString();
                    Log($"消息动作: {action}");

                    // 处理测试消息
                    if (action == "test")
                    {
                        string message = root.GetProperty("message").GetString();
                        Log($"测试消息内容: {message}");

                        // 发送响应回到前端
                        var response = new
                        {
                            action = "testResponse",
                            message = "C#已收到您的消息: " + message
                        };
                        string json = JsonSerializer.Serialize(response);
                        webView.CoreWebView2.PostWebMessageAsJson(json);
                        Log($"已发送响应: {json}");
                    }
                }
            }
            catch (Exception ex)
            {
                Log($"处理消息时出错: {ex.Message}");
            }
        }

        private void Log(string message)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action(() => Log(message)));
                return;
            }

            logTextBox.AppendText($"[{DateTime.Now:HH:mm:ss.fff}] {message}{Environment.NewLine}");
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
            
            // 同时输出到调试窗口
            Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] {message}");
        }

        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new VerySimpleWebView2Test());
        }
    }
}
