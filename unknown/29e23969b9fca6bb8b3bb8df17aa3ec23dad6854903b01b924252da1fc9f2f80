<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- 禁用默认项目项 -->
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
  </PropertyGroup>

  <!-- 包引用 -->
  <ItemGroup>
    <PackageReference Include="AntDesign" Version="1.3.2" />
    <PackageReference Include="AntDesign.Charts" Version="0.6.1" />
    <PackageReference Include="AntDesign.ProLayout" Version="1.3.1" />
    <PackageReference Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageReference Include="EPPlus" Version="8.0.1" />
    <PackageReference Include="HIC.System.Windows.Forms.DataVisualization" Version="1.0.1" />
    <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
    <PackageReference Include="System.Data.OleDb" Version="9.0.4" />
  </ItemGroup>

  <!-- 源代码文件 -->
  <ItemGroup>
    <!-- 非窗体类 -->
    <Compile Include="ExcelFormulaParser.cs" />
    <Compile Include="Program.cs" />
    
    <!-- 窗体类 -->
    <Compile Include="DetectColumnsPositionNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DetectColumnsPositionNew.Designer.cs">
      <DependentUpon>DetectColumnsPositionNew.cs</DependentUpon>
    </Compile>
    
    <Compile Include="Main.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main.Designer.cs">
      <DependentUpon>Main.cs</DependentUpon>
    </Compile>
    
    <Compile Include="ManualDetectForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ManualDetectForm.Designer.cs">
      <DependentUpon>ManualDetectForm.cs</DependentUpon>
    </Compile>
    
    <Compile Include="MineralogicalForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MineralogicalForm.Designer.cs">
      <DependentUpon>MineralogicalForm.cs</DependentUpon>
    </Compile>
  </ItemGroup>

  <!-- 资源文件 - 使用完全限定的逻辑名称 -->
  <ItemGroup>
    <EmbeddedResource Include="DetectColumnsPositionNew.resx">
      <DependentUpon>DetectColumnsPositionNew.cs</DependentUpon>
      <LogicalName>BritSystem.UI.DetectColumnsPositionNew.resources</LogicalName>
    </EmbeddedResource>
    
    <EmbeddedResource Include="Main.resx">
      <DependentUpon>Main.cs</DependentUpon>
      <LogicalName>BritSystem.UI.Main.resources</LogicalName>
    </EmbeddedResource>
    
    <EmbeddedResource Include="MineralogicalForm.resx">
      <DependentUpon>MineralogicalForm.cs</DependentUpon>
      <LogicalName>BritSystem.UI.MineralogicalForm.resources</LogicalName>
    </EmbeddedResource>
  </ItemGroup>

</Project>
