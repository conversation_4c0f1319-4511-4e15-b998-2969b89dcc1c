                    
                    // 如果没有找到有效的列，尝试使用下一行作为表头
                    if (_sourceData.Columns.Count == 0 && headerRowIndex < sheet.LastRowNum)
                    {
                        headerRowIndex++;
                        headerRow = sheet.GetRow(headerRowIndex);
                        
                        for (int i = 0; i < headerRow.LastCellNum; i++)
                        {
                            ICell cell = headerRow.GetCell(i);
                            if (cell != null)
                            {
                                string columnName = cell.ToString().Trim();
                                if (!string.IsNullOrEmpty(columnName))
                                {
                                    _sourceData.Columns.Add(columnName, typeof(double));
                                    System.Diagnostics.Debug.WriteLine($"添加列(第二次尝试): {columnName}, 类型: {typeof(double)}");
                                }
                            }
                        }
                    }
                    
                    // 如果仍然没有列，创建默认列
                    if (_sourceData.Columns.Count == 0)
                    {
                        // 创建默认列
                        for (int i = 0; i < 10; i++)
                        {
                            _sourceData.Columns.Add($"列{i+1}", typeof(double));
                        }
                        System.Diagnostics.Debug.WriteLine("未找到有效的列名，创建默认列");
                    }
                    
                    // 数据开始行通常是表头行的下一行
                    dataStartRowIndex = headerRowIndex + 1;
                    
                    // 添加数据行
                    int rowsAdded = 0;
                    for (int i = dataStartRowIndex; i <= sheet.LastRowNum; i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row == null) continue;
                        
                        // 检查行是否有数据（至少有一个单元格不为空）
                        bool hasData = false;
                        for (int j = 0; j < row.LastCellNum; j++)
                        {
                            ICell cell = row.GetCell(j);
                            if (cell != null && cell.CellType != CellType.Blank)
                            {
                                hasData = true;
                                break;
                            }
                        }
                        
                        if (hasData)
                        {
                            try
                            {
                                DataRow dataRow = _sourceData.NewRow();
                                
                                // 填充数据
                                for (int j = 0; j < Math.Min(row.LastCellNum, _sourceData.Columns.Count); j++)
                                {
                                    ICell cell = row.GetCell(j);
                                    if (cell != null)
                                    {
                                        try
                                        {
                                            switch (cell.CellType)
                                            {
                                                case CellType.Numeric:
                                                    dataRow[j] = cell.NumericCellValue;
                                                    break;
                                                case CellType.String:
                                                    string strValue = cell.StringCellValue.Trim();
                                                    
                                                    // 处理特殊符号
                                                    if (strValue == "√" || strValue == "✓")
                                                    {
                                                        dataRow[j] = 1.0; // 将√转换为1
                                                    }
                                                    // 处理科学计数法（如0.81×10⁻⁴）
                                                    else if (strValue.Contains("×10") || strValue.Contains("*10"))
                                                    {
                                                        try
                                                        {
                                                            // 替换×为*，处理上标
                                                            strValue = strValue.Replace("×", "*").Replace("⁻", "-");
                                                            
                                                            // 分割数字和指数部分
                                                            string[] parts = strValue.Split(new string[] { "*10" }, StringSplitOptions.None);
                                                            if (parts.Length == 2)
                                                            {
                                                                double baseNum = double.Parse(parts[0]);
                                                                int exponent = int.Parse(parts[1]);
                                                                dataRow[j] = baseNum * Math.Pow(10, exponent);
                                                            }
                                                            else
                                                            {
                                                                dataRow[j] = 0.0;
                                                            }
                                                        }
                                                        catch
                                                        {
                                                            dataRow[j] = 0.0;
                                                        }
                                                    }
                                                    // 尝试将字符串转换为数字
                                                    else if (double.TryParse(strValue, out double numValue))
                                                    {
                                                        dataRow[j] = numValue;
                                                    }
                                                    else
                                                    {
                                                        dataRow[j] = 0.0; // 默认值
                                                    }
                                                    break;
                                                case CellType.Boolean:
                                                    dataRow[j] = cell.BooleanCellValue ? 1.0 : 0.0;
                                                    break;
                                                case CellType.Formula:
                                                    try
                                                    {
                                                        dataRow[j] = cell.NumericCellValue;
                                                    }
                                                    catch
                                                    {
                                                        dataRow[j] = 0.0;
                                                    }
                                                    break;
                                                default:
                                                    dataRow[j] = 0.0;
                                                    break;
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            System.Diagnostics.Debug.WriteLine($"处理单元格({i},{j})时出错: {ex.Message}");
                                            dataRow[j] = 0.0;
                                        }
                                    }
                                    else
                                    {
                                        dataRow[j] = 0.0;
                                    }
                                }
                                
                                _sourceData.Rows.Add(dataRow);
                                rowsAdded++;
                                System.Diagnostics.Debug.WriteLine($"添加了第 {i} 行数据");
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"添加第 {i} 行时出错: {ex.Message}");
                            }
                        }
                    }
