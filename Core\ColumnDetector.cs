using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace BritSystem.Core
{
    /// <summary>
    /// 列检测器类，负责检测数据表中的特定列
    /// </summary>
    public class ColumnDetector
    {
        private DataTable _sourceData;
        private List<string> _columnNames;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="sourceData">数据源</param>
        public ColumnDetector(DataTable sourceData)
        {
            _sourceData = sourceData;
            _columnNames = new List<string>();
            
            if (sourceData != null)
            {
                foreach (DataColumn column in sourceData.Columns)
                {
                    _columnNames.Add(column.ColumnName);
                }
            }
        }

        /// <summary>
        /// 检测顶深列名
        /// </summary>
        /// <returns>可能的顶深列名列表</returns>
        public List<string> DetectTopDepthColumns()
        {
            List<string> result = new List<string>();
            List<string> keywords = new List<string> 
            { 
                "顶深", "top", "depth top", "上深", "start depth", "开始深度"
            };
            
            foreach (string columnName in _columnNames)
            {
                foreach (string keyword in keywords)
                {
                    if (columnName.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        result.Add(columnName);
                        break;
                    }
                }
            }
            
            return result;
        }

        /// <summary>
        /// 检测底深列名
        /// </summary>
        /// <returns>可能的底深列名列表</returns>
        public List<string> DetectBottomDepthColumns()
        {
            List<string> result = new List<string>();
            List<string> keywords = new List<string> 
            { 
                "底深", "bottom", "depth bottom", "下深", "end depth", "结束深度"
            };
            
            foreach (string columnName in _columnNames)
            {
                foreach (string keyword in keywords)
                {
                    if (columnName.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        result.Add(columnName);
                        break;
                    }
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 检测脆性矿物列
        /// </summary>
        /// <returns>可能的脆性矿物列列表</returns>
        public List<string> DetectBrittleMineralColumns()
        {
            List<string> result = new List<string>();
            List<string> keywords = new List<string> 
            { 
                "石英", "quartz", "长石", "feldspar", "钠长石", "钾长石", "斜长石", 
                "albite", "orthoclase", "plagioclase", "方解石", "calcite",
                "白云石", "dolomite", "菱镁矿", "magnesite", "硬石膏", "anhydrite",
                "黄铁矿", "pyrite", "碳酸盐", "carbonate", "沸石", "zeolite"
            };
            
            foreach (string columnName in _columnNames)
            {
                if (IsTOC(columnName))
                    continue;
                    
                foreach (string keyword in keywords)
                {
                    if (columnName.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        result.Add(columnName);
                        break;
                    }
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 检测塑性矿物列
        /// </summary>
        /// <returns>可能的塑性矿物列列表</returns>
        public List<string> DetectDuctileMineralColumns()
        {
            List<string> result = new List<string>();
            List<string> keywords = new List<string> 
            { 
                "黏土", "粘土", "clay", "伊利石", "illite", "蒙脱石", "蒙皂石", "蒙沸石",
                "montmorillonite", "高岭石", "kaolinite", "绿泥石", "chlorite",
                "伊/蒙", "伊蒙", "伊+蒙", "伊-蒙", "混层", "mixed layer"
            };
            
            foreach (string columnName in _columnNames)
            {
                foreach (string keyword in keywords)
                {
                    if (columnName.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        // 排除黏土矿物总量列，它会单独处理
                        if (!IsClayMineralTotalColumn(columnName))
                        {
                            result.Add(columnName);
                            break;
                        }
                    }
                }
            }
            
            return result;
        }

        /// <summary>
        /// 检测黏土矿物总量列
        /// </summary>
        /// <returns>可能的黏土矿物总量列列表</returns>
        public List<string> DetectClayMineralTotalColumns()
        {
            List<string> result = new List<string>();
            List<string> keywords = new List<string> 
            { 
                "黏土矿物总量", "粘土矿物总量", "黏土总量", "粘土总量",
                "clay minerals total", "clay total", "total clay"
            };
            
            foreach (string columnName in _columnNames)
            {
                if (IsTOC(columnName))
                {
                    Debug.WriteLine($"列 '{columnName}' 被识别为TOC列，跳过黏土矿物总量检测");
                    continue;
                }
                
                if (IsClayMineralTotalColumn(columnName))
                {
                    Debug.WriteLine($"列 '{columnName}' 被识别为黏土矿物总量列");
                    result.Add(columnName);
                    continue;
                }
                
                foreach (string keyword in keywords)
                {
                    if (columnName.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        Debug.WriteLine($"列 '{columnName}' 通过关键词匹配被识别为黏土矿物总量列: {keyword}");
                        result.Add(columnName);
                        break;
                    }
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// 检查列名是否为TOC列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>是否为TOC列</returns>
        public bool IsTOC(string columnName)
        {
            return ColumnDetectionHelper.IsTOC(columnName);
        }
        
        /// <summary>
        /// 检查列名是否为黏土矿物总量列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>是否为黏土矿物总量列</returns>
        public bool IsClayMineralTotalColumn(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return false;
                
            // 检查是否是TOC列，如果是则不是黏土矿物总量列
            if (IsTOC(columnName))
            {
                Debug.WriteLine($"列名 '{columnName}' 是TOC列，不是黏土矿物总量列");
                return false;
            }
            
            // 黏土矿物总量关键词
            string[] clayTotalKeywords = {
                "黏土矿物总量", "粘土矿物总量", "黏土总量", "粘土总量",
                "clay minerals total", "clay total", "total clay"
            };
            
            // 精确匹配
            foreach (string keyword in clayTotalKeywords)
            {
                if (columnName.Equals(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    Debug.WriteLine($"列名 '{columnName}' 精确匹配黏土矿物总量关键词");
                    return true;
                }
            }
            
            // 部分匹配检查
            if ((columnName.Contains("黏土", StringComparison.OrdinalIgnoreCase) || 
                 columnName.Contains("粘土", StringComparison.OrdinalIgnoreCase) ||
                 columnName.Contains("clay", StringComparison.OrdinalIgnoreCase)) &&
                (columnName.Contains("总量", StringComparison.OrdinalIgnoreCase) || 
                 columnName.Contains("total", StringComparison.OrdinalIgnoreCase) ||
                 columnName.Contains("含量", StringComparison.OrdinalIgnoreCase) || 
                 columnName.Contains("content", StringComparison.OrdinalIgnoreCase)))
            {
                Debug.WriteLine($"列名 '{columnName}' 通过组合关键词匹配被识别为黏土矿物总量列");
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查列名是否为脆性矿物列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>是否为脆性矿物列</returns>
        public bool IsBrittleMineral(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return false;
                
            // 脆性矿物关键词
            string[] brittleMineralKeywords = { 
                "石英", "quartz", "长石", "feldspar", "钠长石", "钾长石", "斜长石", 
                "albite", "orthoclase", "plagioclase", "方解石", "calcite",
                "白云石", "dolomite", "菱镁矿", "magnesite", "硬石膏", "anhydrite",
                "黄铁矿", "pyrite", "碳酸盐", "carbonate", "沸石", "zeolite"
            };
            
            // 检查是否包含脆性矿物关键词
            foreach (string keyword in brittleMineralKeywords)
            {
                if (columnName.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查列名是否为塑性矿物列
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>是否为塑性矿物列</returns>
        public bool IsDuctileMineral(string columnName)
        {
            if (string.IsNullOrEmpty(columnName))
                return false;
                
            // 检查是否是黏土矿物总量列，如果是则不是单独的塑性矿物列
            if (IsClayMineralTotalColumn(columnName))
                return false;
                
            // 塑性矿物关键词
            string[] ductileMineralKeywords = { 
                "黏土", "粘土", "clay", "伊利石", "illite", "蒙脱石", "蒙皂石", "蒙沸石",
                "montmorillonite", "高岭石", "kaolinite", "绿泥石", "chlorite",
                "伊/蒙", "伊蒙", "伊+蒙", "伊-蒙", "混层", "mixed layer"
            };
            
            // 检查是否包含塑性矿物关键词
            foreach (string keyword in ductileMineralKeywords)
            {
                if (columnName.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    return true;
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取列名对应的索引
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <returns>列索引，如果未找到返回-1</returns>
        public int GetColumnIndex(string columnName)
        {
            if (_sourceData == null || string.IsNullOrEmpty(columnName))
                return -1;
                
            // 遍历所有列名，查找匹配的列
            for (int i = 0; i < _sourceData.Columns.Count; i++)
            {
                if (string.Equals(_sourceData.Columns[i].ColumnName, columnName, StringComparison.OrdinalIgnoreCase))
                {
                    return i;
                }
            }
            
            return -1;
        }
        
        /// <summary>
        /// 获取顶深列
        /// </summary>
        /// <returns>顶深列名，如果未检测到则返回空字符串</returns>
        public string DetectTopDepthColumn()
        {
            List<string> columns = DetectTopDepthColumns();
            return columns.Count > 0 ? columns[0] : string.Empty;
        }
        
        /// <summary>
        /// 获取底深列
        /// </summary>
        /// <returns>底深列名，如果未检测到则返回空字符串</returns>
        public string DetectBottomDepthColumn()
        {
            List<string> columns = DetectBottomDepthColumns();
            return columns.Count > 0 ? columns[0] : string.Empty;
        }
    }
} 