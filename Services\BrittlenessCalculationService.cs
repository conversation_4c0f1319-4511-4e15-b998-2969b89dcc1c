using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using BritSystem.Models;
using System.Diagnostics;
using BritSystem;
using System.Reflection;
using System.Text.RegularExpressions;
namespace BritSystem.Services
{
    /// <summary>
    /// 脆性指数计算服务
    /// </summary>
    public class BrittlenessCalculationService
    {
        /// <summary>
        /// 将数据点列表转换为DataTable
        /// </summary>
        /// <param name="dataPoints">脆性指数数据点列表</param>
        /// <param name="sourceData">源数据表</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        /// <returns>转换后的DataTable</returns>
        public static DataTable ConvertToDataTable(
            List<BrittlenessDataPoint> dataPoints,
            DataTable sourceData,
            List<string> brittleColumns,
            List<string> ductileColumns)
        {
            return DataTableExtensions.ConvertToDataTable(dataPoints, sourceData, brittleColumns, ductileColumns);
        }

        /// <summary>
        /// 计算脆性指数
        /// </summary>
        /// <param name="sourceData">源数据表</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        /// <param name="topDepthColumnName">顶深列名</param>
        /// <param name="bottomDepthColumnName">底深列名</param>
        /// <returns>计算结果数据点列表</returns>
        public List<BrittlenessDataPoint> CalculateBrittlenessIndex(
            DataTable sourceData,
            List<string> brittleColumns,
            List<string> ductileColumns,
            string topDepthColumnName,
            string bottomDepthColumnName)
        {
            // 参数验证代码保持不变

            var dataPoints = new List<BrittlenessDataPoint>();

            string actualTopDepthColumn = FindActualColumnName(sourceData, topDepthColumnName);
            string actualBottomDepthColumn = FindActualColumnName(sourceData, bottomDepthColumnName);

            LoggingService.Instance.Debug($"顶深列映射: {topDepthColumnName} -> {actualTopDepthColumn}");
            LoggingService.Instance.Debug($"底深列映射: {bottomDepthColumnName} -> {actualBottomDepthColumn}");

            for (int i = 0; i < sourceData.Rows.Count; i++)
            {
                DataRow row = sourceData.Rows[i];
                var dataPoint = new BrittlenessDataPoint { RowIndex = i };

                // 设置顶深和底深
                if (!string.IsNullOrEmpty(actualTopDepthColumn) && sourceData.Columns.Contains(actualTopDepthColumn))
                {
                    if (row[actualTopDepthColumn] != DBNull.Value &&
                        double.TryParse(row[actualTopDepthColumn].ToString(), out double topDepth))
                    {
                        dataPoint.TopDepth = topDepth;
                    }
                }

                if (!string.IsNullOrEmpty(actualBottomDepthColumn) &&
                    sourceData.Columns.Contains(actualBottomDepthColumn))
                {
                    if (row[actualBottomDepthColumn] != DBNull.Value &&
                        double.TryParse(row[actualBottomDepthColumn].ToString(), out double bottomDepth))
                    {
                        dataPoint.BottomDepth = bottomDepth;
                    }
                }

                // 计算脆性矿物总和
                double brittleSum = brittleColumns
                    .Select(col => FindActualColumnName(sourceData, col))
                    .Where(actualCol => !string.IsNullOrEmpty(actualCol))
                    .Sum(actualCol => row[actualCol] != DBNull.Value &&
                         double.TryParse(row[actualCol].ToString(), out double val) ? val : 0);

                // 计算塑性矿物总和
                double ductileSum = ductileColumns
                    .Select(col => FindActualColumnName(sourceData, col))
                    .Where(actualCol => !string.IsNullOrEmpty(actualCol))
                    .Sum(actualCol => row[actualCol] != DBNull.Value &&
                         double.TryParse(row[actualCol].ToString(), out double val) ? val : 0);

                dataPoint.BrittleIndex = brittleSum + ductileSum > 0
                    ? (brittleSum / (brittleSum + ductileSum)) * 100
                    : 0;

                dataPoints.Add(dataPoint);
            }
            return dataPoints;
        }

        private string FindActualColumnName(DataTable table, string columnPattern)
        {
            // 实现列名查找逻辑
            return columnPattern;
        }

        private bool IsClayTotalColumn(string columnName)
        {
            return columnName.Contains("黏土") || columnName.Contains("粘土");
        }

        private bool IsTOCColumn(string columnName)
        {
            return columnName.Contains("TOC") || columnName.Contains("有机碳");
        }
    }

    public static class DataTableExtensions
    {
        public static DataTable ConvertToDataTable(
            this List<BrittlenessDataPoint> dataPoints,
            DataTable sourceData,
            List<string> brittleColumns,
            List<string> ductileColumns)
        {
            DataTable dt = new DataTable();
            
            // 添加基础地质数据列
            dt.Columns.Add("GeoID", typeof(string));
            dt.Columns.Add("TopDepth", typeof(double));
            dt.Columns.Add("BottomDepth", typeof(double));
            dt.Columns.Add("BrittleIndex", typeof(double));
    
            // 动态添加矿物组分列
            foreach (var col in brittleColumns.Concat(ductileColumns))
            {
                dt.Columns.Add(col, typeof(double));
            }
    
            // 填充复合数据
            for (int i = 0; i < dataPoints.Count; i++)
            {
                DataRow newRow = dt.NewRow();
                var dataPoint = dataPoints[i];
                var sourceRow = sourceData.Rows[i];
    
                // 基础数据
                newRow["GeoID"] = dataPoint.GeoID ?? string.Empty;
                newRow["TopDepth"] = dataPoint.TopDepth;
                newRow["BottomDepth"] = dataPoint.BottomDepth;
                newRow["BrittleIndex"] = dataPoint.BrittleIndex;
    
                // 矿物组分数据
                foreach (var col in brittleColumns.Concat(ductileColumns))
                {
                    string actualCol = FindActualColumnName(sourceData, col);
                    newRow[col] = sourceRow[actualCol] != DBNull.Value 
                        ? Convert.ToDouble(sourceRow[actualCol]) 
                        : 0.0;
                }
    
                dt.Rows.Add(newRow);
            }
            return dt;
        }
    
        private static string FindActualColumnName(DataTable table, string columnPattern)
        {
            return columnPattern;
        }
    }
}