@echo on
echo 编译安装程序...

REM 创建输出目录
if not exist "Output" mkdir "Output"

REM 查找 Inno Setup 编译器
set "ISCC_PATH="

if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
    echo 找到 Inno Setup 6 (32位): %ISCC_PATH%
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
    echo 找到 Inno Setup 6 (64位): %ISCC_PATH%
) else if exist "%ProgramFiles(x86)%\Inno Setup 5\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles(x86)%\Inno Setup 5\ISCC.exe"
    echo 找到 Inno Setup 5 (32位): %ISCC_PATH%
) else if exist "%ProgramFiles%\Inno Setup 5\ISCC.exe" (
    set "ISCC_PATH=%ProgramFiles%\Inno Setup 5\ISCC.exe"
    echo 找到 Inno Setup 5 (64位): %ISCC_PATH%
)

if "%ISCC_PATH%"=="" (
    echo 未找到 Inno Setup。请安装 Inno Setup 后再试。
    echo 下载地址: https://jrsoftware.org/isdl.php
    pause
    exit /b 1
)

REM 检查发布文件是否存在
set "PUBLISH_PATH=bin\Release\net8.0-windows\publish"
set "EXE_PATH=%PUBLISH_PATH%\BritSystem.exe"

echo 检查文件是否存在: %EXE_PATH%
if not exist "%EXE_PATH%" (
    echo 警告: 发布文件未找到。
    echo 是否要先构建和发布项目? (Y/N, 默认=Y):
    set /p BUILD_PROJECT=
    
    if not defined BUILD_PROJECT set "BUILD_PROJECT=Y"
    
    if /i "%BUILD_PROJECT%"=="Y" (
        echo 正在构建项目...
        dotnet build -c Release
        
        echo 正在发布项目...
        dotnet publish -c Release
        
        echo 构建和发布完成。
        
        if not exist "%EXE_PATH%" (
            echo 发布完成，但可执行文件仍未找到: %EXE_PATH%
            echo 无法继续。
            pause
            exit /b 1
        )
    ) else (
        echo 跳过构建。无法继续。
        pause
        exit /b 1
    )
)

echo 找到可执行文件: %EXE_PATH%

REM 编译安装程序
echo 正在编译安装程序...
echo 使用 ISS 文件: SimpleSetup.iss

REM 运行 Inno Setup 编译器
echo 运行命令: "%ISCC_PATH%" "SimpleSetup.iss"
"%ISCC_PATH%" "SimpleSetup.iss"

REM 检查结果
if %ERRORLEVEL% EQU 0 (
    echo 构建成功!
    if exist "Output\BritSystemSetup.exe" (
        echo 安装程序位置: %CD%\Output\BritSystemSetup.exe
        
        REM 打开输出目录
        start "" "Output"
    ) else (
        echo 警告: 安装程序未在预期位置找到。
        echo 预期路径: %CD%\Output\BritSystemSetup.exe
    )
) else (
    echo 构建失败。请检查错误信息。
)

echo.
echo 按任意键退出...
pause
