using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Diagnostics;

namespace BritSystem.Core
{
    /// <summary>
    /// 脆性指数计算器类，用于计算样本的脆性指数
    /// </summary>
    public class BrittlenessCalculator
    {
        private readonly DataTable _sourceData;
        private readonly DataTable _resultData;
        private readonly List<string> _brittleColumns;
        private readonly List<string> _ductileColumns;
        private readonly int _topDepthIndex;
        private readonly int _bottomDepthIndex;
        private readonly string _topDepthColumnName;
        private readonly string _bottomDepthColumnName;
        private readonly Dictionary<string, string> _columnMappings;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="sourceData">源数据</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        /// <param name="topDepthIndex">顶深列索引</param>
        /// <param name="bottomDepthIndex">底深列索引</param>
        public BrittlenessCalculator(DataTable sourceData, List<string> brittleColumns, List<string> ductileColumns, int topDepthIndex, int bottomDepthIndex)
        {
            _sourceData = sourceData ?? throw new ArgumentNullException(nameof(sourceData));
            _brittleColumns = brittleColumns ?? throw new ArgumentNullException(nameof(brittleColumns));
            _ductileColumns = ductileColumns ?? throw new ArgumentNullException(nameof(ductileColumns));
            _topDepthIndex = topDepthIndex;
            _bottomDepthIndex = bottomDepthIndex;
            _topDepthColumnName = string.Empty;
            _bottomDepthColumnName = string.Empty;
            _columnMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

            _resultData = CreateResultTable();
        }

        /// <summary>
        /// 带完整参数的构造函数
        /// </summary>
        /// <param name="sourceData">源数据</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        /// <param name="topDepthIndex">顶深列索引</param>
        /// <param name="bottomDepthIndex">底深列索引</param>
        /// <param name="topDepthColumnName">顶深列名</param>
        /// <param name="bottomDepthColumnName">底深列名</param>
        /// <param name="columnMappings">列名映射</param>
        public BrittlenessCalculator(
            DataTable sourceData,
            List<string> brittleColumns,
            List<string> ductileColumns,
            int topDepthIndex,
            int bottomDepthIndex,
            string topDepthColumnName,
            string bottomDepthColumnName,
            Dictionary<string, string> columnMappings)
        {
            _sourceData = sourceData ?? throw new ArgumentNullException(nameof(sourceData));
            _brittleColumns = brittleColumns ?? throw new ArgumentNullException(nameof(brittleColumns));
            _ductileColumns = ductileColumns ?? throw new ArgumentNullException(nameof(ductileColumns));
            _topDepthIndex = topDepthIndex;
            _bottomDepthIndex = bottomDepthIndex;
            _topDepthColumnName = topDepthColumnName ?? string.Empty;
            _bottomDepthColumnName = bottomDepthColumnName ?? string.Empty;
            _columnMappings = columnMappings ?? new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

            _resultData = CreateResultTable();
        }

        /// <summary>
        /// 计算脆性指数
        /// </summary>
        /// <returns>计算结果表</returns>
        public DataTable CalculateBrittlenessIndex()
        {
            Debug.WriteLine("开始计算脆性指数...");

            // 清空结果表
            _resultData.Rows.Clear();

            for (int i = 0; i < _sourceData.Rows.Count; i++)
            {
                // 计算单行的脆性指数
                CalculateRow(_sourceData.Rows[i], i);
            }

            return _resultData;
        }

        /// <summary>
        /// 计算脆性指数 - 新API
        /// </summary>
        /// <returns>计算结果表</returns>
        public DataTable Calculate()
        {
            // 调用原有方法以实现向下兼容
            return CalculateBrittlenessIndex();
        }

        /// <summary>
        /// 计算单行的脆性指数
        /// </summary>
        /// <param name="row">数据行</param>
        /// <param name="rowIndex">行索引</param>
        private void CalculateRow(DataRow row, int rowIndex)
        {
            try
            {
                // 计算脆性矿物总量和塑性矿物总量
                double brittleTotal = CalculateBrittleMineralsTotal(row);
                double ductileTotal = CalculateDuctileMineralsTotal(row);

                // 计算脆性指数
                double brittlenessIndex = CalculateBrittlenessIndex(brittleTotal, ductileTotal);

                // 创建结果行
                DataRow resultRow = _resultData.NewRow();

                // 设置GeoID
                resultRow["GeoID"] = $"Sample_{rowIndex + 1}";

                // 设置顶深和底深
                if (_topDepthIndex >= 0 && _topDepthIndex < row.Table.Columns.Count)
                {
                    if (row[_topDepthIndex] != DBNull.Value &&
                        double.TryParse(row[_topDepthIndex].ToString(), out double topDepth))
                    {
                        resultRow["顶深/m"] = topDepth;
                    }
                }

                if (_bottomDepthIndex >= 0 && _bottomDepthIndex < row.Table.Columns.Count)
                {
                    if (row[_bottomDepthIndex] != DBNull.Value &&
                        double.TryParse(row[_bottomDepthIndex].ToString(), out double bottomDepth))
                    {
                        resultRow["底深/m"] = bottomDepth;
                    }
                }

                // 设置脆性指数和矿物总量
                resultRow["脆性指数"] = Math.Round(brittlenessIndex, 4);
                resultRow["脆性矿物总量"] = Math.Round(brittleTotal, 2);
                resultRow["塑性矿物总量"] = Math.Round(ductileTotal, 2);

                // 添加结果行
                _resultData.Rows.Add(resultRow);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"计算第 {rowIndex + 1} 行时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算脆性矿物总量
        /// </summary>
        /// <param name="row">数据行</param>
        /// <returns>脆性矿物总量</returns>
        private double CalculateBrittleMineralsTotal(DataRow row)
        {
            double total = 0;

            // 处理常规脆性矿物列
            foreach (string columnName in _brittleColumns)
            {
                if (row.Table.Columns.Contains(columnName) &&
                    row[columnName] != DBNull.Value &&
                    double.TryParse(row[columnName].ToString(), out double value))
                {
                    total += value;
                    Debug.WriteLine($"添加脆性矿物: {columnName} = {value}");
                }
            }

            // 处理手动映射的脆性矿物列
            foreach (var mapping in _columnMappings)
            {
                if (mapping.Value == "脆性矿物")
                {
                    string sourceColumn = mapping.Key;
                    if (row.Table.Columns.Contains(sourceColumn) &&
                        row[sourceColumn] != DBNull.Value &&
                        double.TryParse(row[sourceColumn].ToString(), out double value))
                    {
                        total += value;
                        Debug.WriteLine($"添加手动映射的脆性矿物: {sourceColumn} = {value}");
                    }
                }
            }

            Debug.WriteLine($"脆性矿物总量: {total}");
            return total;
        }

        /// <summary>
        /// 计算塑性矿物总量
        /// </summary>
        /// <param name="row">数据行</param>
        /// <returns>塑性矿物总量</returns>
        private double CalculateDuctileMineralsTotal(DataRow row)
        {
            double total = 0;

            // 处理常规塑性矿物列
            foreach (string columnName in _ductileColumns)
            {
                if (row.Table.Columns.Contains(columnName) &&
                    row[columnName] != DBNull.Value &&
                    double.TryParse(row[columnName].ToString(), out double value))
                {
                    total += value;
                    Debug.WriteLine($"添加塑性矿物: {columnName} = {value}");
                }
            }

            // 处理手动映射的塑性矿物列
            foreach (var mapping in _columnMappings)
            {
                if (mapping.Value == "塑性矿物")
                {
                    string sourceColumn = mapping.Key;
                    if (row.Table.Columns.Contains(sourceColumn) &&
                        row[sourceColumn] != DBNull.Value &&
                        double.TryParse(row[sourceColumn].ToString(), out double value))
                    {
                        total += value;
                        Debug.WriteLine($"添加手动映射的塑性矿物: {sourceColumn} = {value}");
                    }
                }
            }

            Debug.WriteLine($"塑性矿物总量: {total}");
            return total;
        }

        /// <summary>
        /// 计算脆性指数
        /// </summary>
        /// <param name="brittleTotal">脆性矿物总量</param>
        /// <param name="ductileTotal">塑性矿物总量</param>
        /// <returns>脆性指数</returns>
        private double CalculateBrittlenessIndex(double brittleTotal, double ductileTotal)
        {
            // 计算脆性指数: 脆性矿物总量 / (脆性矿物总量 + 塑性矿物总量)
            if (brittleTotal + ductileTotal <= 0)
            {
                return 0;
            }

            return brittleTotal / (brittleTotal + ductileTotal);
        }

        /// <summary>
        /// 创建结果表
        /// </summary>
        /// <returns>结果表结构</returns>
        private DataTable CreateResultTable()
        {
            DataTable resultTable = new DataTable();

            // 添加列
            resultTable.Columns.Add("GeoID", typeof(string));
            resultTable.Columns.Add("顶深/m", typeof(double));
            resultTable.Columns.Add("底深/m", typeof(double));
            resultTable.Columns.Add("脆性指数", typeof(double));
            resultTable.Columns.Add("脆性矿物总量", typeof(double));
            resultTable.Columns.Add("塑性矿物总量", typeof(double));

            return resultTable;
        }
    }
}